{"originHash": "44c749453652cedb274fa7dfdeea01b9a9d047946426da5ca1f9d65e2cf09a75", "pins": [{"identity": "abseil-cpp-binary", "kind": "remoteSourceControl", "location": "https://github.com/google/abseil-cpp-binary.git", "state": {"revision": "194a6706acbd25e4ef639bcaddea16e8758a3e27", "version": "1.2024011602.0"}}, {"identity": "app-check", "kind": "remoteSourceControl", "location": "https://github.com/google/app-check.git", "state": {"revision": "3b62f154d00019ae29a71e9738800bb6f18b236d", "version": "10.19.2"}}, {"identity": "appauth-ios", "kind": "remoteSourceControl", "location": "https://github.com/openid/AppAuth-iOS.git", "state": {"revision": "2781038865a80e2c425a1da12cc1327bcd56501f", "version": "1.7.6"}}, {"identity": "firebase-ios-sdk", "kind": "remoteSourceControl", "location": "https://github.com/firebase/firebase-ios-sdk", "state": {"revision": "eca84fd638116dd6adb633b5a3f31cc7befcbb7d", "version": "10.29.0"}}, {"identity": "googleappmeasurement", "kind": "remoteSourceControl", "location": "https://github.com/google/GoogleAppMeasurement.git", "state": {"revision": "fe727587518729046fc1465625b9afd80b5ab361", "version": "10.28.0"}}, {"identity": "googledatatransport", "kind": "remoteSourceControl", "location": "https://github.com/google/GoogleDataTransport.git", "state": {"revision": "a637d318ae7ae246b02d7305121275bc75ed5565", "version": "9.4.0"}}, {"identity": "googlesignin-ios", "kind": "remoteSourceControl", "location": "https://github.com/google/GoogleSignIn-iOS", "state": {"revision": "a7965d134c5d3567026c523e0a8a583f73b62b0d", "version": "7.1.0"}}, {"identity": "googleutilities", "kind": "remoteSourceControl", "location": "https://github.com/google/GoogleUtilities.git", "state": {"revision": "57a1d307f42df690fdef2637f3e5b776da02aad6", "version": "7.13.3"}}, {"identity": "grpc-binary", "kind": "remoteSourceControl", "location": "https://github.com/google/grpc-binary.git", "state": {"revision": "e9fad491d0673bdda7063a0341fb6b47a30c5359", "version": "1.62.2"}}, {"identity": "gtm-session-fetcher", "kind": "remoteSourceControl", "location": "https://github.com/google/gtm-session-fetcher.git", "state": {"revision": "a2ab612cb980066ee56d90d60d8462992c07f24b", "version": "3.5.0"}}, {"identity": "gtmappauth", "kind": "remoteSourceControl", "location": "https://github.com/google/GTMAppAuth.git", "state": {"revision": "5d7d66f647400952b1758b230e019b07c0b4b22a", "version": "4.1.1"}}, {"identity": "interop-ios-for-google-sdks", "kind": "remoteSourceControl", "location": "https://github.com/google/interop-ios-for-google-sdks.git", "state": {"revision": "2d12673670417654f08f5f90fdd62926dc3a2648", "version": "100.0.0"}}, {"identity": "leveldb", "kind": "remoteSourceControl", "location": "https://github.com/firebase/leveldb.git", "state": {"revision": "a0bc79961d7be727d258d33d5a6b2f1023270ba1", "version": "1.22.5"}}, {"identity": "nanopb", "kind": "remoteSourceControl", "location": "https://github.com/firebase/nanopb.git", "state": {"revision": "b7e1104502eca3a213b46303391ca4d3bc8ddec1", "version": "2.30910.0"}}, {"identity": "promises", "kind": "remoteSourceControl", "location": "https://github.com/google/promises.git", "state": {"revision": "540318ecedd63d883069ae7f1ed811a2df00b6ac", "version": "2.4.0"}}, {"identity": "richtext", "kind": "remoteSourceControl", "location": "https://github.com/NuPlay/RichText.git", "state": {"revision": "ec3469ee47c17e0f90e4b43f23d7e19bc42e069b", "version": "2.7.0"}}, {"identity": "sdwebimage", "kind": "remoteSourceControl", "location": "https://github.com/SDWebImage/SDWebImage.git", "state": {"revision": "b62cb63bf4ed1f04c961a56c9c6c9d5ab8524ec6", "version": "5.21.1"}}, {"identity": "sdwebimageswiftui", "kind": "remoteSourceControl", "location": "https://github.com/SDWebImage/SDWebImageSwiftUI.git", "state": {"revision": "53573d6dd017e354c0e7d8f1c86b77ef1383c996", "version": "2.2.7"}}, {"identity": "siren", "kind": "remoteSourceControl", "location": "https://github.com/ArtSabintsev/Siren.git", "state": {"revision": "6139af3394bc3635c6c8d5255339796feaa7d1a0", "version": "6.1.3"}}, {"identity": "swift-protobuf", "kind": "remoteSourceControl", "location": "https://github.com/apple/swift-protobuf.git", "state": {"revision": "102a647b573f60f73afdce5613a51d71349fe507", "version": "1.30.0"}}, {"identity": "<PERSON><PERSON><PERSON><PERSON>", "kind": "remoteSourceControl", "location": "https://github.com/SwiftyJSON/SwiftyJSON.git", "state": {"revision": "af76cf3ef710b6ca5f8c05f3a31307d44a3c5828", "version": "5.0.2"}}, {"identity": "wrappinghstack", "kind": "remoteSourceControl", "location": "https://github.com/dkk/WrappingHStack.git", "state": {"revision": "425d9488ba55f58f0b34498c64c054c77fc2a44b", "version": "2.2.11"}}], "version": 3}