//
//  Extensions.swift
//  Zebeel
//
//  Created by <PERSON><PERSON> on 26/09/20.
//

import Foundation
import UIKit

typealias Completion = () -> Void

extension UIViewController {
    func hideNavigationBar(){
        // Hide the navigation bar on the this view controller
//        self.navigationController?.setNavigationBarHidden(true, animated: true)
        self.navigationController?.navigationBar.isHidden = true
        
    }
    
    func showNavigationBar() {
        // Show the navigation bar on other view controllers
        self.navigationController?.setNavigationBarHidden(false, animated: true)
    }
    func switchRootViewController(rootViewController: UIViewController, animated: Bool, completion: (() -> Void)?) {
         guard let window = UIApplication.shared.keyWindow else {
            let window = UIApplication.shared.windows[0]
            setWindow(window:window, rootViewController:rootViewController, animated: animated, completion: completion)
            return
            
        }
        print("window fixed")
        setWindow(window:window, rootViewController:rootViewController, animated: animated, completion: completion)
        }
    func setWindow(window:UIWindow,rootViewController: UIViewController, animated: Bool, completion: (() -> Void)?) {
        if animated {
            UIView.transition(with: window, duration: 0.5, options: .transitionCrossDissolve, animations: {
                let oldState: Bool = UIView.areAnimationsEnabled
                UIView.setAnimationsEnabled(false)
                window.rootViewController = UINavigationController(rootViewController:rootViewController)
                UIView.setAnimationsEnabled(oldState)
            }, completion: { (finished: Bool) -> () in
                if (completion != nil) {
                    completion!()
                }
            })
        } else {
            window.rootViewController =   UINavigationController(rootViewController:rootViewController)
        }
    }
    
    @available(iOS 13.0, *)
    func switchRootViewController2(rootViewController: UIViewController, animated: Bool, completion: (() -> Void)?) {
        
        let window = UIApplication.shared.windows[0]
        if animated {
            UIView.transition(with: window, duration: 0.5, options: .transitionCrossDissolve, animations: {
                let oldState: Bool = UIView.areAnimationsEnabled
                UIView.setAnimationsEnabled(false)
                window.rootViewController = UINavigationController(rootViewController:rootViewController)
                UIView.setAnimationsEnabled(oldState)
            }, completion: { (finished: Bool) -> () in
                if (completion != nil) {
                    completion!()
                }
            })
        } else {
            window.rootViewController =   UINavigationController(rootViewController:rootViewController)
        }
    }
}
//extension UIWindow {
//    static var key: UIWindow {
//        if #available(iOS 13, *) {
//            return UIApplication.shared.windows[0]
//        } else {
//            return UIApplication.shared.keyWindow
//        }
//    }
//}
extension NSLayoutConstraint {
    
    func setMultiplier(multiplier: CGFloat) -> NSLayoutConstraint {
        guard let firstItem = firstItem else {
            return self
        }
        NSLayoutConstraint.deactivate([self])
        let newConstraint = NSLayoutConstraint(item: firstItem, attribute: firstAttribute, relatedBy: relation, toItem: secondItem, attribute: secondAttribute, multiplier: multiplier, constant: constant)
        newConstraint.priority = priority
        newConstraint.shouldBeArchived = self.shouldBeArchived
        newConstraint.identifier = self.identifier
        NSLayoutConstraint.activate([newConstraint])
        return newConstraint
    }
}
extension UIWindow {
    static var key: UIWindow? {
        if #available(iOS 13, *) {
            return UIApplication.shared.windows.first { $0.isKeyWindow }
        } else {
            return UIApplication.shared.keyWindow
        }
    }
}
extension NSMutableAttributedString {

    func setColorForText(textForAttribute: String, withColor color: UIColor) {
        let range: NSRange = self.mutableString.range(of: textForAttribute, options: .caseInsensitive)

        // Swift 4.2 and above
        self.addAttribute(NSAttributedString.Key.foregroundColor, value: color, range: range)

        // Swift 4.1 and below
        self.addAttribute(NSAttributedString.Key.foregroundColor, value: color, range: range)
    }

}
extension UIViewController {
    
    func removeChild() {
        self.children.forEach {
            $0.willMove(toParent: nil)
            $0.view.removeFromSuperview()
            $0.removeFromParent()
        }
    }
    
    
}
//MARK:Checking_Devices
struct CurrentDevice {
    static let IS_IPAD   = UIDevice.current.userInterfaceIdiom == .pad
    static let IS_IPHONE = UIDevice.current.userInterfaceIdiom == .phone
}
extension UIView {
    func addShadow(offset: CGSize = CGSize.init(width: 0, height: 3), color: UIColor = UIColor.black, radius: CGFloat =  2.0, opacity: Float = 0.35) {
        layer.masksToBounds = false
        layer.shadowOffset = offset
        layer.shadowColor = color.cgColor
        layer.shadowRadius = radius
        layer.shadowOpacity = opacity
        
        let backgroundCGColor = backgroundColor?.cgColor
        backgroundColor = nil
        layer.backgroundColor =  backgroundCGColor
    }
    
    func horizontalgradient(colours: [UIColor]) -> Void {
        let gradient: CAGradientLayer = CAGradientLayer()
        gradient.frame = self.bounds
        gradient.colors = colours.map { $0.cgColor }
        gradient.startPoint = CGPoint(x: 0.0, y: 0.5)
        gradient.endPoint = CGPoint(x: 1.0, y: 0.5)
        self.layer.insertSublayer(gradient, at: 0)
        
    }
    func gradient(colours: [UIColor]) -> Void {
        let gradient: CAGradientLayer = CAGradientLayer()
        gradient.frame = self.bounds
        gradient.colors = colours.map { $0.cgColor }
        gradient.locations = [0.4,1.0]
        self.layer.insertSublayer(gradient, at: 0)
        
    }
    func removeViews() {
        self.subviews.forEach({ $0.removeFromSuperview() })
    }
    //transition from bottom
    func animateFromBottom() {
        let transition = CATransition()
        transition.type = CATransitionType.push
        transition.subtype = CATransitionSubtype.fromTop
        transition.duration = 0.3
        self.isHidden = false
        self.layer.add(transition, forKey:nil)
    }
    func animateFromTop() {
        let transition = CATransition()
        transition.type = CATransitionType.push
        transition.subtype = CATransitionSubtype.fromBottom
        transition.duration = 0.3
        self.isHidden = true
        self.layer.add(transition, forKey:nil)
    }
    func animateFromLeft() {
        let transition = CATransition()
        transition.type = CATransitionType.push
        transition.subtype = CATransitionSubtype.fromLeft
        transition.duration = 0.3
        self.isHidden = true
        self.layer.add(transition, forKey:nil)
    }
    func animateFromRight() {
        let transition = CATransition()
        transition.type = CATransitionType.push
        transition.subtype = CATransitionSubtype.fromRight
        transition.duration = 0.3
        self.isHidden = false
        self.layer.add(transition, forKey:nil)
    }
    /// Adds shadow to the view.
    /// - Parameters:
    ///     - color: Color of the shadow. Default DarkGray with 0.2 opacity.
    ///     - offset: The offset (in points) of the layer’s shadow.
    func setupShadow(color: UIColor = UIColor.darkGray.withAlphaComponent(0.3), offset: CGSize = .init(width: 0, height: 5), radius: CGFloat = 3) {
        DispatchQueue.main.asyncAfter(deadline: .now() + 0.2) { [weak self] in
            self?.layer.shadowColor = color.cgColor
            self?.layer.shadowOpacity = 1
            self?.layer.shadowOffset = offset
            self?.layer.shadowRadius = radius
            self?.layer.shadowPath = UIBezierPath(roundedRect: self?.bounds ?? .zero, cornerRadius: self?.cornerRadius ?? 0).cgPath
            self?.layer.shouldRasterize = false
            self?.layer.masksToBounds = false
        }
    }
    
    
    
    
    
    func roundCorners(corners: UIRectCorner, radius: Int = 8) {
        let maskPath1 = UIBezierPath(roundedRect: bounds,
                                     byRoundingCorners: corners,
                                     cornerRadii: CGSize(width: radius, height: radius))
        let maskLayer1 = CAShapeLayer()
        maskLayer1.frame = bounds
        maskLayer1.path = maskPath1.cgPath
        layer.mask = maskLayer1
    }
    func setShadow(color: UIColor = UIColor.darkGray.withAlphaComponent(0.3), offset: CGSize = .init(width: 0, height: 5), radius: CGFloat = 3,opacity:Float = 1.0) {
        DispatchQueue.main.asyncAfter(deadline: .now() + 0.2) { [weak self] in
            self?.layer.shadowColor = color.cgColor
            self?.layer.shadowOpacity = opacity
            self?.layer.shadowOffset = offset
            self?.layer.shadowRadius = radius
            self?.layer.shadowPath = UIBezierPath(roundedRect: self?.bounds ?? .zero, cornerRadius: self?.cornerRadius ?? 0).cgPath
            self?.layer.shouldRasterize = false
            self?.layer.masksToBounds = false
        }
    }
    func blink(duration: TimeInterval = 1.0, delay: TimeInterval = 0.0, alpha: CGFloat = 0.0) {
        UIView.animate(withDuration: duration, delay: delay, options: [.curveEaseInOut], animations: {
            if self.alpha == 0 {
                self.alpha = 1
            }
            self.alpha = alpha
        })
    }
    
    
    func setCornerRadius() {
        self.layer.cornerRadius = self.frame.size.width / 2
        self.clipsToBounds = true
    }
    var cornerRadius: CGFloat {
        get { return layer.cornerRadius }
        set {
            layer.cornerRadius  = newValue
            layer.masksToBounds = newValue != 0
            clipsToBounds       = newValue != 0
        }
    }
    
    /// Adds shadow to the view.
    /// - Parameters:
    ///     - color: Color of the shadow. Default DarkGray with 0.2 opacity.
    ///     - offset: The offset (in points) of the layer’s shadow.
    func setShadow(color: UIColor = UIColor.black.withAlphaComponent(0.2), offset: CGSize = .init(width: 0, height: 5), radius: CGFloat = 3) {
        enqueueUIStack(after: 0.2) { [weak self] in
            self?.layer.shadowColor = color.cgColor
            self?.layer.shadowOpacity = 0
            self?.layer.shadowOffset = offset
            self?.layer.shadowRadius = radius
            self?.layer.shadowPath = UIBezierPath(roundedRect: self?.bounds ?? .zero, cornerRadius: self?.layer.cornerRadius ?? 0).cgPath
            self?.layer.shouldRasterize = true
            self?.layer.rasterizationScale = UIScreen.main.scale
            self?.layer.masksToBounds = false
            self?.layer.needsDisplayOnBoundsChange = true
            
            let animation = CABasicAnimation(keyPath: "shadowOpacity")
            animation.fromValue = self?.layer.shadowOpacity
            animation.toValue = 1.0
            animation.duration = 1.0
            self?.layer.add(animation, forKey: animation.keyPath)
            self?.layer.shadowOpacity = 1.0
        }
    }
    
    func addborder(width: CGFloat = 0.3, color: UIColor = .lightGray, alpha: CGFloat = 1.0) {
        layer.borderWidth = width
        layer.borderColor = color.withAlphaComponent(alpha).cgColor
    }
    
    func addGradient() {
        let colorTop = #colorLiteral(red: 0.01568627451, green: 0.4156862745, blue: 0.7215686275, alpha: 1).cgColor
        let colorBottom = #colorLiteral(red: 0.441876173, green: 0.8435254693, blue: 0.07106629759, alpha: 1)
        let gradient = CAGradientLayer()
        gradient.colors = [colorTop, colorBottom]
        gradient.locations = [0.0, 1.0]
        gradient.frame = bounds
        layer.insertSublayer(gradient, at:0)
    }
    
    func setRadius(in corners: UIRectCorner = .allCorners,
                         width: CGFloat = 25.0,
                         height: CGFloat = 25.0,
                         color: UIColor = UIColor(red: 224/255, green: 223/255, blue: 223/255, alpha: 1.0)) {
        let rectShape = CAShapeLayer()
        rectShape.bounds = frame
        rectShape.position = center
        rectShape.path = UIBezierPath(roundedRect: bounds,
                                      byRoundingCorners: corners,
                                      cornerRadii: CGSize(width: width, height: height)).cgPath
        
        layer.backgroundColor = color.cgColor
        layer.mask = rectShape
    }
    
    func round(corners: UIRectCorner, radius: CGFloat) {
        let path = UIBezierPath(roundedRect: bounds, byRoundingCorners: corners, cornerRadii: CGSize(width: radius, height: radius))
        let mask = CAShapeLayer()
        mask.path = path.cgPath
        layer.mask = mask
    }
    
   
        
        enum Direction: Int {
            case topToBottom = 0
            case bottomToTop
            case leftToRight
            case rightToLeft
        }
        
        func applyGradient(colors: [Any]?, locations: [NSNumber]? = [0.0, 1.0], direction: Direction = .topToBottom) {
            
            let gradientLayer = CAGradientLayer()
            gradientLayer.frame = self.bounds
            gradientLayer.colors = colors
            gradientLayer.locations = locations
            
            switch direction {
            case .topToBottom:
                gradientLayer.startPoint = CGPoint(x: 0.5, y: 0.0)
                gradientLayer.endPoint = CGPoint(x: 0.5, y: 1.0)
                
            case .bottomToTop:
                gradientLayer.startPoint = CGPoint(x: 0.5, y: 1.0)
                gradientLayer.endPoint = CGPoint(x: 0.5, y: 0.0)
                
            case .leftToRight:
                gradientLayer.startPoint = CGPoint(x: 0.0, y: 0.5)
                gradientLayer.endPoint = CGPoint(x: 1.0, y: 0.5)
                
            case .rightToLeft:
                gradientLayer.startPoint = CGPoint(x: 1.0, y: 0.5)
                gradientLayer.endPoint = CGPoint(x: 0.0, y: 0.5)
            }
            
            self.layer.insertSublayer(gradientLayer, at: 0)
        }
//    let gradient: CAGradientLayer = CAGradientLayer()
//    gradient.frame = self.bounds
//    gradient.colors = colours.map { $0.cgColor }
//    gradient.locations = [0.4,1.0]
//    self.layer.insertSublayer(gradient, at: 0)
}


extension String {
    func height(withConstrainedWidth width: CGFloat, font: UIFont) -> CGFloat {
        let constraintRect = CGSize(width: width, height: .greatestFiniteMagnitude)
        let boundingBox = self.boundingRect(with: constraintRect, options: .usesLineFragmentOrigin, attributes: [.font: font], context: nil)
        
        return ceil(boundingBox.height)
    }
    
    func width(withConstrainedHeight height: CGFloat, font: UIFont) -> CGFloat {
        let constraintRect = CGSize(width: .greatestFiniteMagnitude, height: height)
        let boundingBox = self.boundingRect(with: constraintRect, options: .usesLineFragmentOrigin, attributes: [.font: font], context: nil)
        
        return ceil(boundingBox.width)
    }
}
// MARK: Method to Run a block in Main thread
//----------------------------------------------------------------------------------------------
/// Async Any UI Operation to Main.
/// - Parameter deadline: The time at which to schedule the block for execution.
/// - Parameter completion: The block containing the work to perform.
func enqueueUIStack(after deadline: TimeInterval = 0.0, completion: @escaping Completion) {
    DispatchQueue.main.asyncAfter(deadline: .now() + deadline, execute: completion)
}

extension UIColor {
    convenience init(colorWithHexValue value: Int, alpha:CGFloat = 1.0){
        self.init(
            red: CGFloat((value & 0xFF0000) >> 16) / 255.0,
            green: CGFloat((value & 0x00FF00) >> 8) / 255.0,
            blue: CGFloat(value & 0x0000FF) / 255.0,
            alpha: alpha
        )
    }
}

extension Notification.Name {
    static var Subviewclick: Notification.Name {
        return .init("Subviewclick")
    }
}
extension UserDefaults {
    var getStarted: Bool {
        get { return Bool(bool(forKey:#function)) }
        set { set(newValue, forKey: #function) }
    }
}
protocol CBackgroundTextSettable {
    var customTag: Int { get }
    var backgroundView: UIView? { get set }
    var backgroundText: String? { get set }
}

extension CBackgroundTextSettable {
    var customTag: Int { return 7766 }
}

extension CBackgroundTextSettable where Self: UIView {
    
    var backgroundText: String? {
        get { return (viewWithTag(customTag) as? UILabel)?.text }
        
        mutating set {
            guard newValue != nil else {
                backgroundView = nil
                return
            }
            
            if let existingLabel = viewWithTag(customTag) as? UILabel {
                existingLabel.text = newValue
                return
            }
            
            let label = UILabel(frame: bounds)
            //            label.center = center
            //            label.center.y *= 0.8//label.center.y * 0.8
            //            label.frame.size.height = 100 // reducing height to 80%, to raise the label from center.
            label.font = UIFont.systemFont(ofSize: 15, weight: UIFont.Weight.medium)
            label.backgroundColor = .clear
            label.textColor = .lightGray
            label.lineBreakMode = .byWordWrapping
            label.textAlignment = .center
            label.numberOfLines = 0
            label.tag = customTag
            label.text = newValue
            backgroundView = label
        }
    }
}
extension UITableView: CBackgroundTextSettable {}

extension UICollectionView: CBackgroundTextSettable {}
extension UIView{

    func loadIndicator( ) {
        let backgroundView = UIView()
        backgroundView.frame = CGRect.init(x: 0, y: 0, width: self.bounds.width, height: self.bounds.height)
        backgroundView.backgroundColor = UIColor.clear
        backgroundView.tag = 475647
        
        let loadingIndicator = ProgressView(colors: [.red, .systemGreen, .systemBlue], lineWidth: 5)
        loadingIndicator.frame = CGRect.init(x:UIScreen.main.bounds.maxX/2-30, y:UIScreen.main.bounds.maxX/2, width: 50, height: 50)
        
        loadingIndicator.isAnimating = true
        self.isUserInteractionEnabled = false
        backgroundView.addSubview(loadingIndicator)
       
       self.addSubview(backgroundView)
    }

    func stopIndicator() {
        if let background = viewWithTag(475647){
            background.removeFromSuperview()
        }
        self.isUserInteractionEnabled = true
    }
}
extension UITextField {

   func addInputViewDatePicker(target: Any, selector: Selector) {

    let screenWidth = UIScreen.main.bounds.width

    //Add DatePicker as inputView
    let datePicker = UIDatePicker(frame: CGRect(x: 0, y: 0, width: screenWidth, height: 216))
    datePicker.datePickerMode = .date
    if #available(iOS 13.4, *) {
        datePicker.preferredDatePickerStyle = .wheels
    } 
    self.inputView = datePicker

    //Add Tool Bar as input AccessoryView
    let toolBar = UIToolbar(frame: CGRect(x: 0, y: 0, width: screenWidth, height: 44))
    let flexibleSpace = UIBarButtonItem(barButtonSystemItem: .flexibleSpace, target: nil, action: nil)
    let cancelBarButton = UIBarButtonItem(title: "Cancel", style: .plain, target: self, action: #selector(cancelPressed))
    let doneBarButton = UIBarButtonItem(title: "Done", style: .plain, target: target, action: selector)
    toolBar.setItems([cancelBarButton, flexibleSpace, doneBarButton], animated: false)

    self.inputAccessoryView = toolBar
 }

   @objc func cancelPressed() {
     self.resignFirstResponder()
   }
}

extension UIView{
    func removeAnimation(){
       self.layer.removeAllAnimations()
       self.layer.removeAllAnimations()
       self.layoutIfNeeded()
    }
}
extension String {
    func trimmed() -> Self { return self.trimmingCharacters(in: .whitespaces) }
        var isSpace : Bool {
            let s = self
            let cset = NSCharacterSet.newlines.inverted
            let r = s.rangeOfCharacter(from:cset)
            let ok = s.isEmpty || r == nil
            return ok
        }
    
    //To check text field or String is blank or not
    var isBlank: Bool {
        get {
            let trimmed = trimmingCharacters(in: CharacterSet.whitespaces)
            return trimmed.isEmpty
        }
    }
    
    //Validate Email
    
    var isEmail: Bool {
        do {
            
            let regex = try NSRegularExpression(pattern: "[A-Z0-9a-z._%+-]+@[A-Za-z0-9.-]+\\.[A-Za-z]{2,4}", options: .caseInsensitive)
            return regex.firstMatch(in: self, options: NSRegularExpression.MatchingOptions(rawValue: 0), range: NSMakeRange(0, self.count)) != nil
        } catch {
            return false
        }
    }
    
    var isAlphanumeric: Bool {
        return !isEmpty && range(of: "[^a-zA-Z0-9]", options: .regularExpression) == nil
    }
}
extension UIAlertController {
    
    func isValidEmail(_ email: String) -> Bool {
        return email.count > 0 && email.isEmail
    }
    
    
    @objc func textDidChangeInLoginAlert() {
        
        if let email = textFields?[0].text,
          
            let action = actions.first {
            action.isEnabled = isValidEmail(email)
        }
        
        
    }
}
extension Date {
    func timeAgoDisplay() -> String {

        let calendar = Calendar.current
        let minuteAgo = calendar.date(byAdding: .minute, value: -1, to: Date())!
        let hourAgo = calendar.date(byAdding: .hour, value: -1, to: Date())!
        let dayAgo = calendar.date(byAdding: .day, value: -1, to: Date())!
        let weekAgo = calendar.date(byAdding: .day, value: -7, to: Date())!

        if minuteAgo < self {
            let diff = Calendar.current.dateComponents([.second], from: self, to: Date()).second ?? 0
            return "\(diff) sec ago"
        } else if hourAgo < self {
            let diff = Calendar.current.dateComponents([.minute], from: self, to: Date()).minute ?? 0
            return "\(diff) min ago"
        } else if dayAgo < self {
            let diff = Calendar.current.dateComponents([.hour], from: self, to: Date()).hour ?? 0
            return "\(diff) hrs ago"
        } else if weekAgo < self {
            let diff = Calendar.current.dateComponents([.day], from: self, to: Date()).day ?? 0
            return "\(diff) days ago"
        }
        let diff = Calendar.current.dateComponents([.weekOfYear], from: self, to: Date()).weekOfYear ?? 0
        return "\(diff) weeks ago"
    }
}
extension String {
    var htmlToAttributedString: NSAttributedString? {
        guard let data = data(using: .utf8) else { return nil }
        do {
            return try NSAttributedString(data: data, options: [.documentType: NSAttributedString.DocumentType.html, .characterEncoding:String.Encoding.utf8.rawValue], documentAttributes: nil)
        } catch {
            return nil
        }
    }
    var htmlToString: String {
        return htmlToAttributedString?.string ?? ""
    }
}

extension UIView {
    
    func add4sideShadow() {
               
                self.layer.shadowColor = UIColor.lightGray.cgColor
                self.layer.shadowOpacity = 1.0
                self.layer.shadowRadius = 5.0
                self.layer.shadowOffset = .zero
                self.layer.shadowPath = UIBezierPath(rect: self.bounds).cgPath
                self.layer.shouldRasterize = true
    }
    func addBottomShadow(radius:CGFloat = 20) {
    layer.cornerRadius = radius
    layer.masksToBounds = false
    layer.shadowRadius = 4
    layer.shadowOpacity = 1
    layer.shadowColor = UIColor.gray.cgColor
    layer.shadowOffset = CGSize(width: 0 , height: 2)
    layer.shadowPath = UIBezierPath(rect: CGRect(x: 0,
                                                 y: bounds.maxY - layer.shadowRadius,
                                                 width: bounds.width,
                                                 height: layer.shadowRadius)).cgPath
}
}
extension UIView {
func addBottomShadow2() {
    layer.masksToBounds = false
    layer.shadowRadius = 4
    layer.shadowOpacity = 1
    layer.shadowColor = UIColor.gray.cgColor
    layer.shadowOffset = CGSize(width: 0 , height: 2)
    layer.shadowPath = UIBezierPath(rect: CGRect(x: 0,
                                                 y: bounds.maxY - layer.shadowRadius,
                                                 width: bounds.width,
                                                 height: layer.shadowRadius)).cgPath
}
}
extension Notification.Name {
    static let messageAlert = Notification.Name("messegealert")
    static let languageChange = Notification.Name("lanaguageChange")
    static let cartCount = Notification.Name("cartCount")
    static let logout = Notification.Name("logout")
    static let trackOrder = Notification.Name("trackorder")
    
}
typealias GradientPoints = (startPoint: CGPoint, endPoint: CGPoint)

enum GradientOrientation {
    case topRightBottomLeft
    case topLeftBottomRight
    case horizontal
    case vertical

    var startPoint : CGPoint {
        return points.startPoint
    }

    var endPoint : CGPoint {
        return points.endPoint
    }

    var points : GradientPoints {
        switch self {
        case .topRightBottomLeft:
            return (CGPoint(x: 0.0,y: 1.0), CGPoint(x: 1.0,y: 0.0))
        case .topLeftBottomRight:
            return (CGPoint(x: 0.0,y: 0.0), CGPoint(x: 1,y: 1))
        case .horizontal:
            return (CGPoint(x: 0.0,y: 0.5), CGPoint(x: 1.0,y: 0.5))
        case .vertical:
            return (CGPoint(x: 0.0,y: 0.0), CGPoint(x: 0.0,y: 1.0))
        }
    }
}

extension UIView {

    func applyGradient(with colours: [UIColor], locations: [NSNumber]? = nil) {
        let gradient = CAGradientLayer()
        gradient.frame = self.bounds
        gradient.colors = colours.map { $0.cgColor }
        gradient.locations = locations
        self.layer.insertSublayer(gradient, at: 0)
    }

    func applyGradient(with colours: [UIColor], gradient orientation: GradientOrientation) {
        let gradient = CAGradientLayer()
        gradient.frame = self.bounds
        gradient.colors = colours.map { $0.cgColor }
        gradient.startPoint = orientation.startPoint
        gradient.endPoint = orientation.endPoint
        self.layer.insertSublayer(gradient, at: 0)
    }
}
extension UIImageView {
  func setImageColor(color: UIColor) {
    let templateImage = self.image?.withRenderingMode(.alwaysTemplate)
    self.image = templateImage
    self.tintColor = color
  }
}
extension UIColor {
    convenience init(hexFromString:String, alpha:CGFloat = 1.0) {
        var cString:String = hexFromString.trimmingCharacters(in: .whitespacesAndNewlines).uppercased()
        var rgbValue:UInt32 = 10066329 //color #999999 if string has wrong format

        if (cString.hasPrefix("#")) {
            cString.remove(at: cString.startIndex)
        }

        if ((cString.count) == 6) {
            Scanner(string: cString).scanHexInt32(&rgbValue)
        }

        self.init(
            red: CGFloat((rgbValue & 0xFF0000) >> 16) / 255.0,
            green: CGFloat((rgbValue & 0x00FF00) >> 8) / 255.0,
            blue: CGFloat(rgbValue & 0x0000FF) / 255.0,
            alpha: alpha
        )
    }
}
extension UITextField {

    //MARK:- Set Image on the right of text fields

  func setupRightImage(imageName:String){
    let imageView = UIImageView(frame: CGRect(x: 10, y: 10, width: 15, height:15))
    imageView.image = UIImage(named: imageName)
    let imageContainerView: UIView = UIView(frame: CGRect(x: 0, y: 0, width: 45, height: 30))
    imageContainerView.addSubview(imageView)
    rightView = imageContainerView
    rightViewMode = .always
    self.tintColor = .lightGray
}

 //MARK:- Set Image on left of text fields

    func setupLeftImage(imageName:String){
       let imageView = UIImageView(frame: CGRect(x: 10, y: 10, width: 20, height: 20))
       imageView.image = UIImage(named: imageName)
       let imageContainerView: UIView = UIView(frame: CGRect(x: 0, y: 0, width: 55, height: 40))
       imageContainerView.addSubview(imageView)
       leftView = imageContainerView
       leftViewMode = .always
       self.tintColor = .lightGray
     }

  }
