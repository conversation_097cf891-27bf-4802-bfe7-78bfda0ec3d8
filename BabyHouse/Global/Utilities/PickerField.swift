//
//  PickerField.swift
//  General
//
//  Updated by Sijo on 28/08/2020.
//

import UIKit

@objc protocol UIPickerFieldDelegate: NSObjectProtocol {
    func pickerFieldTitles(_ pickerField: PickerField) -> Int
    func pickerFieldTitle(_ pickerField: PickerField, at index: Int) -> String?
    @objc optional func pickerFieldShouldBeginEditing(_ pickerField: PickerField)
    @objc optional func pickerField(_ pickerField: PickerField, didSelectedTitleAt index: Int)
    @objc optional func pickerFieldShouldEndEditing(_ pickerField: PickerField)
    @objc optional func pickerFieldShouldReturn(_ pickerField: PickerField)
}

@nonobjc extension UIColor {
    static let labelColor: UIColor = {
        guard #available(iOS 13.0, *) else { return .black }
        return .label
    }()
    
    static let secondaryBackgroundColor: UIColor = {
        guard #available(iOS 13.0, *) else { return .init(red: 242/255, green: 242/255, blue: 247/255, alpha: 1.0) }
        return .secondarySystemBackground
    }()
    
    static let teritaryGroupedBackgroundColor: UIColor = {
        guard #available(iOS 13.0, *) else { return .init(red: 242/255, green: 242/255, blue: 247/255, alpha: 1.0) }
        return .tertiarySystemGroupedBackground
    }()
}

@nonobjc extension UIImageView {
    convenience init(image: UIImage?, contentMode: ContentMode) {
        self.init(image: image)
        self.contentMode = contentMode
    }
}

@objc class PickerField: UITextField, UITextFieldDelegate, UIPickerViewDelegate, UIPickerViewDataSource {
    
    /// Initaialize layer
    private(set) var border: CALayer?
    
    override var isHidden: Bool {
        didSet {
            super.isHidden = isHidden
            layer.sublayers?.first(where: {
                $0.name == "underline"
            })?.isHidden = isHidden
        }
    }
    
    @IBInspectable var countryCodeOnly: Bool = false
    
    private var insetX : CGFloat { 0 }
    
    // placeholder position
    override func textRect(forBounds bounds: CGRect) -> CGRect {
        bounds.insetBy(dx: insetX, dy: 0)
    }

    // text position
    override func editingRect(forBounds bounds: CGRect) -> CGRect {
        bounds.insetBy(dx: insetX, dy: 0)
    }
    
    override func rightViewRect(forBounds bounds: CGRect) -> CGRect {
        .init(x: bounds.width - 30, y: 0, width: 20 , height: bounds.height)
    }

    @IBOutlet weak var pickerDelegate: UIPickerFieldDelegate?
    
    func countryCode(from text: String?) -> String? {
        text?.components(separatedBy: CharacterSet.decimalDigits.inverted)
             .compactMap { return Int($0) }
             .map({ return "\($0)"})
             .joinedAsCountryCode()
    }
    
    var selectedIndex: Int = -1 {
        didSet {
            self.selectedRow = self.selectedIndex
            if selectedIndex >= 0 {
                let text = pickerDelegate?.pickerFieldTitle(self, at: selectedIndex)
                super.text = countryCodeOnly ? countryCode(from: text) : text
            } else {
                super.text = nil
            }
            self.selectedText = super.text
        }
    }
    
    func selectedTitle(at index: Int = 0, animated: Bool = false) {
        pickerView.selectRow(index, inComponent: 0, animated: animated)
    }
    
    func reload() {
        text = nil
        pickerView.reloadAllComponents()
    }
    
    override var text: String? {
        set { super.text = newValue }
        get { super.text }
    }
    
    @available(*, unavailable)
    override var delegate: UITextFieldDelegate? {
        set { super.delegate = newValue }
        get { super.delegate }
    }
    
    override var inputAccessoryView: UIView? {
        set { super.inputAccessoryView = newValue }
        get { super.inputAccessoryView }
    }
    
    override var inputView: UIView? {
        set { super.inputView = newValue }
        get { super.inputView }
    }
    
    fileprivate var pickerView: UIPickerView = {
        let pickerView = UIPickerView()
        pickerView.backgroundColor = .teritaryGroupedBackgroundColor
        return pickerView
    }()
    
    fileprivate var selectedText: String?
    fileprivate var selectedRow: Int = -1
    
    override init(frame: CGRect) {
        super.init(frame: frame)
        initialisation()
    }
    
    required init?(coder aDecoder: NSCoder) {
        super.init(coder: aDecoder)
        initialisation()
    }
    
    fileprivate func initialisation() {
        
        rightViewMode = .always
        rightView = UIImageView(image: #imageLiteral(resourceName: "Arrow-Right"), contentMode: .scaleAspectFit)
        rightView?.tintColor = .darkGray
        
        super.inputView = pickerView
        super.inputAccessoryView = configureInputAccessoryView()
        
        pickerView.delegate = self
        pickerView.dataSource = self
        
        super.delegate = self
    }
    
    fileprivate func configureInputAccessoryView() -> UIView {
        
        let screenSize: CGSize = UIScreen.main.bounds.size
        
        let toolbar: UIToolbar = UIToolbar(frame: CGRect(x: 0, y: 0, width: screenSize.width, height: 44))
        
        toolbar.isTranslucent = true
        
        let cancelBarButton: UIBarButtonItem = UIBarButtonItem(barButtonSystemItem: .cancel, target: self, action: #selector(cancelAction(_:)))
        
        let fexibleSpace: UIBarButtonItem = UIBarButtonItem(barButtonSystemItem: .flexibleSpace, target: nil, action: nil)
        
        let doneBarButton: UIBarButtonItem = UIBarButtonItem(barButtonSystemItem: .done, target: self, action: #selector(doneAction(_:)))
        
        toolbar.setItems([cancelBarButton, fexibleSpace, doneBarButton], animated: false)
        
        return toolbar
    }

    override func caretRect(for position: UITextPosition) -> CGRect {
        .zero
    }
    
    override func canPerformAction(_ action: Selector, withSender sender: Any?) -> Bool {
        if action == #selector(paste(_:)) {
            return false
        } else if action == #selector(copy(_:)) {
            return false
        }
        
        return super.canPerformAction(action, withSender: sender)
    }

    @objc func cancelAction(_ sender: UIBarButtonItem) {
        resignFirstResponder()
        pickerDelegate?.pickerFieldShouldEndEditing?(self)
    }
    
    @objc func doneAction(_ sender: UIBarButtonItem) {
        resignFirstResponder()
        pickerDelegate?.pickerFieldShouldEndEditing?(self)
        
        if self.selectedIndex != self.selectedRow {
            super.text = selectedText
            self.selectedIndex = selectedRow
            pickerDelegate?.pickerField?(self, didSelectedTitleAt: selectedIndex)
        }
        
        if let selectedText = text,
               selectedText.isEmpty {
            
            if let count = pickerDelegate?.pickerFieldTitles(self),
                   count >= 1 {
                selectedIndex = 0
                selectedTitle(at: 0, animated: true)
            }
        }
        
        pickerDelegate?.pickerFieldShouldReturn?(self)
    }
    
    // MARK: - UITEXTFIELD DELEGATE
    func textFieldShouldBeginEditing(_ textField: UITextField) -> Bool {
        pickerDelegate?.pickerFieldShouldBeginEditing?(self)
        return true
    }
    
    func textFieldDidBeginEditing(_ textField: UITextField) {
        guard selectedIndex > 0 else { return }
        selectedTitle(at: selectedIndex)
    }
    
    func textFieldShouldEndEditing(_ textField: UITextField) -> Bool {
        true
    }
    
    func textFieldDidEndEditing(_ textField: UITextField) {}
    
    func textField(_ textField: UITextField, shouldChangeCharactersIn range: NSRange, replacementString string: String) -> Bool {
        return false
    }
    
    func textFieldShouldClear(_ textField: UITextField) -> Bool {
        false
    }
    
    func textFieldShouldReturn(_ textField: UITextField) -> Bool {
        false
    }
    
    // MARK: - UIPICKER VIEW DATA SOURCE
    func numberOfComponents(in pickerView: UIPickerView) -> Int {
        1
    }
    
    func pickerView(_ pickerView: UIPickerView, numberOfRowsInComponent component: Int) -> Int {
        if let fieldDelegate = pickerDelegate {
            return fieldDelegate.pickerFieldTitles(self)
        }
        
        return 0
    }
    
    // MARK: - UIPICKER VIEW DELEGATE
    func pickerView(_ pickerView: UIPickerView, titleForRow row: Int, forComponent component: Int) -> String? {
        pickerDelegate?.pickerFieldTitle(self, at: row)
    }
    
    func pickerView(_ pickerView: UIPickerView, didSelectRow row: Int, inComponent component: Int) {
        self.selectedRow = row
    }
    func pickerView(_ pickerView: UIPickerView, rowHeightForComponent component: Int) -> CGFloat {
        50
    }
    func pickerView(_ pickerView: UIPickerView, viewForRow row: Int, forComponent component: Int, reusing view: UIView?) -> UIView {
        let label: UILabel

        if let view = view {
            label = view as! UILabel
        }
        else {
            label = UILabel(frame: CGRect(x: 0, y: 0, width: pickerView.frame.width, height: 100))
        }

        let title = pickerDelegate?.pickerFieldTitle(self, at: row)
        let attributes = [NSAttributedString.Key.foregroundColor: UIColor.labelColor,
                          NSAttributedString.Key.font: UIFont(name: "HelveticaNeue-Medium", size: 16.0)!]
        label.attributedText = NSAttributedString(string: title!, attributes: attributes)
        label.lineBreakMode = .byWordWrapping
        label.textAlignment = .center
        label.numberOfLines = 0
        label.sizeToFit()

        return label
    }
}

fileprivate extension Array where Element == String {
    func joinedAsCountryCode(by separator: String = "") -> String? {
        guard !isEmpty else { return nil }
        guard self.count >= 4 else { return "+" + joined(separator: separator) }
        return "+" + first! + " " + dropFirst().joined(separator: separator)
    }
}
