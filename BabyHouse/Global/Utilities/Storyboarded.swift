//
//  Storyboarded.swift
//  MVPDemo2
//
//  Created by <PERSON><PERSON><PERSON> on 19/02/19.
//  Copyright © 2019 Bibin. All rights reserved.
//

import UIKit

enum Storyboard: String {
    case splash,
         category,
         account,
         home,
         profile,
         wishlist,
         login,
         address,
         order,
         cart,
         settings,
         search
    
    var instance: UIStoryboard {
        return .init(name: String(describing:self).capitalized, bundle: .main)
    }
}

protocol Storyboarded {
    static var storyboard: Storyboard { get }
    static func instantiate() -> Self
}

extension Storyboarded where Self: UIViewController {
    static func instantiate() -> Self {
        let identifier = String(describing: self)
        return storyboard.instance.instantiateViewController(withIdentifier: identifier) as! Self
    }
}
