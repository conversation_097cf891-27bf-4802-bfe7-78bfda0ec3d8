//
//  Utilities.swift
//  BabyHouse
//
//  Created by Apple on 02/09/23.
//
import Combine
import SwiftUI

public typealias Cancellable = AnyCancellable
public typealias Cancellables = [Cancellable]
public typealias IterationIdentifiable = CaseIterable & Identifiable
public typealias VoidCallback = () -> Void
public typealias TypeCallback<T> = (T) -> Void
public typealias DictionaryType = [String: Any]


class Utilities {
    
    public static  func enQueue(after deadline: DispatchTime, main async: @escaping () -> Void) {
        DispatchQueue.main.asyncAfter(deadline: deadline) {
            async()
        }
    }
    
    public static  func enQueue(main async: @escaping () -> Void) {
        if Thread.isMainThread {
            async()
        } else {
            DispatchQueue.main.async {
                async()
            }
        }
    }

    public static  func enQueue(after seconds: Int, main async: @escaping () -> Void) {
        DispatchQueue.main.asyncAfter(deadline: .now() + .seconds(seconds)) {
            async()
        }
    }

    
    public static  func withSpeedBouncingAnimation(content: VoidCallback) {
        withAnimation(.speedBouncing) { content() }
    }
    
    
    public static  func withHeroAnimation(content: VoidCallback) {
        
        withAnimation(.hero) { content() }
    }
 
}


extension Animation {
    
    static var speedBouncing: Animation {
        .interpolatingSpring(stiffness: 150, damping: 10, initialVelocity: 10)
    }
    
    static var hero: Animation {
        .interactiveSpring(response: 0.6, dampingFraction: 0.85, blendDuration: 0.25)
    }
    
    
}
