//
//  Fonts.swift
//  Quickerala
//
//  Created by <PERSON><PERSON><PERSON> on 24/05/19.
//  Copyright © 2019 Hifx IT & Media Services Private Limited. All rights reserved.
//

import UIKit
import Device<PERSON>it

/**
 This extention is a list of predefined set of fonts used in the application.
 */
extension UIFont {
    
    /// Font for giant headings.
    static var heading: UIF<PERSON> { return with(size: 22, .bold) }
    /// Font for mini title in a cell or in a screen.
    static var title: <PERSON><PERSON>ont { return with(size: 18, .regular) }
    /// Font for subtitle in a screen.
    static var subtitle: UIFont { return with(size: 15, .regular) }
    /// Font for long description text.
    static var description: UIFont { return with(size: 15, .regular) }
    
    static var boldTitle: UIFont { return with(size: 15, .bold) }
    static var playlistTitle: UIFont { return with(size: 20, .bold) }
    static var playlisttime: UIFont { return with(size: 12, .light) }
    
    
    /// A simpler static function which can be used to generate a font.
    static func with(size: CGFloat, _ weight: UIFont.Weight) -> UIFont {
        
        if  CurrentDevice.IS_IPAD {
            return UIFont(name: weight.fontName, size: size)!
        }
        
        let device = Device.current
        switch device {
            
        //iphone5
        case .iPhone5,.iPhoneSE,.iPhone5s,.iPhone5c:
            return UIFont(name: weight.fontName, size: size)!
        //iphone6
        case .iPhone6,.iPhone6s,.iPhone7,.iPhone8,.iPhoneSE2:
            return UIFont(name: weight.fontName, size: size + 1)!
        //iphoneplus
        case .iPhone7Plus,.iPhone8Plus,.iPhone6sPlus,.iPhone6Plus:
            return UIFont(name: weight.fontName, size: size + 2)!
        //iphone X and more
        case .iPhoneX,.iPhoneXR,.iPhoneXSMax,.iPhoneXS,.iPhone11ProMax,.iPhone11,.iPhone11Pro,.iPhone12,.iPhone12Pro,.iPhone12Mini,.iPhone12ProMax:
            return UIFont(name: weight.fontName, size: size + 3)!
            
        default: return UIFont(name: weight.fontName, size: size)!
        }
        
        
    }

    }
    
    
    



/// Names of application font
private extension UIFont {
    class var applicationFontNameLight: String { return "Metropolis-Light" }
    class var applicationFontNameBlack: String { return "Metropolis-Black" }
    class var applicationFontNameRegular: String { return "Metropolis-Regular" }
    class var applicationFontNameBold: String { return "Metropolis-Bold" }
    class var applicationFontNamemedium: String { return "Metropolis-Medium"}
    
}

/// 
extension UIFont.Weight {
    var fontName: String {
        switch self {
        case .medium : return UIFont.applicationFontNamemedium
        case .light:    return UIFont.applicationFontNameLight
        case .regular:  return UIFont.applicationFontNameRegular
        case .bold:     return UIFont.applicationFontNameBold
        case .black :return UIFont.applicationFontNameBlack
        default:        return UIFont.applicationFontNameRegular
        }
    }
   
}
