//
//  ApplicationConfiguration.swift
//  Localization
//
//  Created by <PERSON><PERSON> on 23/01/20.
//  Copyright © 2020 hifxit. All rights reserved.
//

import UIKit
import IQKeyboardManagerSwift
final class ApplicationConfiguration {
    
    /// Use this function will setup the initial Viewcontroller.
    static func configureInitialViewController(on window: inout UIWindow?) {
        setupUI()
        window = UIWindow(frame: UIScreen.main.bounds)
        let root = SplashView.instantiate()
        window?.rootViewController = UINavigationController(rootViewController:root)
        window?.makeKeyAndVisible()
        //print("current lan = \(LocalizationSystem.sharedInstance.getLanguage())")
    }

    static func setupUI() {
        IQKeyboardManager.shared.enable = true
        IQKeyboardManager.shared.previousNextDisplayMode = .alwaysShow
    }
    
}


