//
//  MActivityIndicator.swift
//  ManoramaKit
//
//  Created by sijo on 15/05/19.
//  Copyright © 2019 Hifx. All rights reserved.
//

import Toast_Swift
import UIKit

/// Activity Indicator wrapper class
final class MActivityIndicator {
    /// Duration for the overlay to appear
    private static let animationDuration: TimeInterval = 0.3
    
    /// Internal count to avoid multiple overlays if already added.
    private static var counter: Int = 0
    
    private var activity: UIView?
    
    /// Spinning activity indicator
    private static let activityIndicator: ProgressView = {
        let indicator = ProgressView(colors: [<PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>], lineWidth: 5)
        indicator.frame = CGRect(x: UIScreen.main.bounds.maxX/2 - 30, y: UIScreen.main.bounds.maxX/2, width: 50, height: 50)
        // indicator.isAnimating = true
        return indicator
    }()
    
    /// Overlay View
    static let overlayView: UIView = {
//        if #available(iOS 15, *) {
//            let view = UIView(frame: UIApplication.shared.windows.first?.frame ?? .zero)
//            view.backgroundColor = UIColor.black.withAlphaComponent(0.35)
//            activityIndicator.center = view.center
//            view.addSubview(activityIndicator)
//            return view
//        }
        
//        let view = UIView(frame: UIApplication.shared.windows.first?.frame ?? .zero)
        let view = UIView(frame: UIScreen.main.bounds)
        view.backgroundColor = UIColor.black.withAlphaComponent(0.35)
        activityIndicator.center = view.center
        view.addSubview(activityIndicator)
        return view
    }()
    
    /// Calling this function will increase the counter and show the overlay and activity indicator on top of the screen.
    /// Multiple calls to this function without calling hide() will only increase the counter with no visible changes.
    
    static func showToast(_ message: String) {
        UIApplication.shared.keyWindow?.makeToast(message)
    }
    
  static  func showErrorMessage(_ message: String) {
        let alertWindow = UIWindow(frame: UIScreen.main.bounds)
        alertWindow.rootViewController = UIViewController()

        let alertController = UIAlertController(title: "Error", message: message, preferredStyle: UIAlertController.Style.alert)
        alertController.addAction(UIAlertAction(title: "Close", style: UIAlertAction.Style.cancel, handler: { _ in
            alertWindow.isHidden = true
        }))
        
        alertWindow.windowLevel = UIWindow.Level.alert + 1
        alertWindow.makeKeyAndVisible()
        alertWindow.rootViewController?.present(alertController, animated: true, completion: nil)
      
      
     
    }
    
    static func showAlert(_ title: String, _ message: String) {
        // Show a basic alert
        func showAlert(title: String, message: String) {
            let alert = UIAlertController(title: title, message: message, preferredStyle: UIAlertController.Style.alert)
            alert.addAction(UIAlertAction(title: "OK", style: UIAlertAction.Style.default, handler: nil))
            // Add more actions as you see fit
//            UIApplication.shared.keyWindow?.rootViewController?.present(alert, animated: true, completion: {})
            alert.presentInOwnWindow(animated: true, completion: nil)
        }
    }
    
    static func show() {
        counter += 1
        if counter == 1 {
            Utilities.enQueue(after: .now()) {
                overlayView.alpha = 0
                
                if #available(iOS 15, *) {
                    UIApplication.shared.keyWindow?.addSubview(overlayView)
                    UIView.animate(withDuration: animationDuration) {
                        overlayView.alpha = 1
                        activityIndicator.isAnimating = true
                    }
                    return
                }
                UIApplication.shared.keyWindow?.addSubview(overlayView)
                
                UIView.animate(withDuration: animationDuration) {
                    overlayView.alpha = 1
                    activityIndicator.isAnimating = true
                }
            }
        }
    }
    
    /// Calling this function will reduce the counter and hide the overlay if counter is zero
    static func hide() {
        counter = max(0, counter - 1)
        if counter == 0 {
            Utilities.enQueue(after: .now()) {
                UIView.animate(withDuration: animationDuration, animations: {
                    overlayView.alpha = 0
                }) { _ in
                    activityIndicator.isAnimating = false
                    overlayView.removeFromSuperview()
                }
            }
        }
    }
}

extension Utilities {
    static func showActivity() {
        Utilities.enQueue {
            MActivityIndicator.show()
        }
    }
    
    static func showAlert(title: String, message: String) {
        Utilities.enQueue {
            MActivityIndicator.showAlert(title, message)
        }
       
    }
    
    
    static func showErrorMessage(_ message:String) {
        Utilities.enQueue {
            MActivityIndicator.showErrorMessage(message)
        }
        
    }
    
    static func hideActivity() {
        Utilities.enQueue {
            MActivityIndicator.hide()
        }
    }
    
    static func showToast(_ message: String) {
        Utilities.enQueue {
            MActivityIndicator.showToast(message)
        }
    }
}

func showActivity() {
    Utilities.enQueue {
        MActivityIndicator.show()
    }
}

func hideActivity() {
    Utilities.enQueue {
        MActivityIndicator.hide()
    }
}

extension UIView {
    func showActivity() {
        let view = MActivityIndicator.overlayView
        Utilities.enQueue(after: .now()) {
            view.alpha = 0
            self.addSubview(view)
            view.frame = self.frame
            UIView.animate(withDuration: 0.35) {
                view.alpha = 1
            }
        }
    }
    
    func hideActivity() {
        subviews.filter { $0.alpha == 0 }.forEach { $0.removeFromSuperview() }
    }
}



extension UIAlertController {

    func presentInOwnWindow(animated: Bool, completion: (() -> Void)?) {

        let windowAlertPresentationController = WindowAlertPresentationController(alert: self)
        windowAlertPresentationController.present(animated: animated, completion: completion)
    }
}

class WindowAlertPresentationController: UIViewController {

    // MARK: - Properties

    private lazy var window: UIWindow? = UIWindow(frame: UIScreen.main.bounds)
    private let alert: UIAlertController

    // MARK: - Initialization

    init(alert: UIAlertController) {

        self.alert = alert
        super.init(nibName: nil, bundle: nil)
    }

    required init?(coder aDecoder: NSCoder) {

        fatalError("This initializer is not supported")
    }

    // MARK: - Presentation

    func present(animated: Bool, completion: (() -> Void)?) {

        window?.rootViewController = self
        window?.windowLevel = UIWindow.Level.alert + 1
        window?.makeKeyAndVisible()
        present(alert, animated: animated, completion: completion)
    }

    // MARK: - Overrides

    override func dismiss(animated flag: Bool, completion: (() -> Void)? = nil) {

        super.dismiss(animated: flag) {
            self.window = nil
            completion?()
        }
    }
}
