//
//  BadgeView.swift
//  BabyHouse
//
//  Created by Apple on 12/09/23.
//

import SwiftUI

struct Badge: View {
    let count: Int
    let color: Color
    let position: CGPoint
    var body: some View {
        if count > 0 {
            ZStack(alignment: .topTrailing) {
                Color.clear
                Text(LocalizedStringKey("\(count)"))
                    .font(.custom("Metropolis-Light", size: 9.0.relativeFontSize))
                    .fontWeight(.light)
                    .padding(4)
                    .background(color)
                    .clipShape(Circle())
                    // custom positioning in the top-right corner
//                    .alignmentGuide(.top) { $0[.bottom] - $0.height * 0.55 }
//                    .alignmentGuide(.trailing) { $0[.trailing] - $0.width * 0.35 }
                    .alignmentGuide(.top) { $0[.bottom] - $0.height * position.y }
                    .alignmentGuide(.trailing) { $0[.trailing] - $0.width * position.x }
            }
        }
    }
}

