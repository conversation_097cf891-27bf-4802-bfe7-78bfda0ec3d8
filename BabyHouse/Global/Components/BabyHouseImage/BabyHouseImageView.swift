//
//  BabyHouseImageView.swift
//  BabyHouse
//
//  Created by Apple on 02/09/23.
//

import SwiftUI
import SDWebImageSwiftUI


struct BabyHouseImageViewContent<Content>: View where Content:View {

    let path: String?
    let contentMode: ContentMode
    let originalColor: Bool
    let onFailure: ((Error) -> Void)?
    let content: () -> Content
    init(_ path: String?, contentMode: ContentMode = .fit, originalColor: Bool = true, onFailure: ((Error) -> Void)? = nil, @ViewBuilder content: @escaping () -> Content) {
        self.originalColor = originalColor
        self.path = path
        self.onFailure = onFailure
        self.content = content
        self.contentMode = contentMode
    }

    var body: some View {

        if let path = path {
            if path.starts(with: "http") {
                WebImage(url: URL(string: path))
                    .resizable()
                    .placeholder { Image(systemName: "photo").foregroundColor(.white) }
                    .onFailure(perform: onFailure)
                    .placeholder(content: content)
                    .indicator(.activity) // Activity Indicator
                    .transition(.fade(duration: 0.5)) // Fade Transition with duration
                    .aspectRatio(contentMode: contentMode)
            } else {
                Image(path)
                    .renderingMode(originalColor ? .original : .template)
                    .resizable()
                    .scaledToFit()
            }

        } else {
            Image(systemName: "photo")
        }
    }
}

struct CustomImage: View {
    let path: String?
    let contentMode: ContentMode
    let originalColor: Bool
    let onFailure: ((Error) -> Void)?
    init(_ path: String?, contentMode: ContentMode = .fit, originalColor: Bool = true, onFailure: ((Error) -> Void)? = nil) {
        self.originalColor = originalColor
        self.path = path
        self.onFailure = onFailure
        self.contentMode = contentMode
        
    }
    var body: some View {
        if let path = path {
            if path.starts(with: "http") {
                WebImage(url: URL(string: path))
                    .resizable()
                    .onFailure(perform: onFailure)
                    .indicator(.activity) // Activity Indicator
                    .transition(.fade(duration: 0.5)) // Fade Transition with duration
                    .aspectRatio(contentMode: contentMode)
            } else {
                Image(path)
                    .renderingMode(originalColor ? .original : .template)
                    .resizable()
                    .scaledToFit()
            }
            
        } else {
            Image(systemName: "photo")
        }
    }
}

