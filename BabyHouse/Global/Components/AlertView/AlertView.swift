//
//  AlertView.swift
//  BabyHouse
//
//  Created by Apple on 02/09/23.
//

import SwiftUI

struct AlertConfig: Equatable {
    static func == (lhs: AlertConfig, rhs: AlertConfig) -> Bool {
        return lhs.title == rhs.title &&
        lhs.text == rhs.text &&
        lhs.alertType == rhs.alertType &&
        lhs.cancelButtonText == rhs.cancelButtonText &&
        lhs.okButtonText == rhs.okButtonText
        
    }
    
    let title, text: String
    let cancelButtonText, okButtonText: String
    let alertType:AlertType
    let onCancel: VoidCallback?
    let onOk: VoidCallback?
    
    init(title: String, text: String, cancelButtonText: String = "Cancel", okButtonText: String = "Ok", alertType:AlertType = .alert, onCancel: VoidCallback? = nil, onOk: VoidCallback? = nil) {
        self.title = title
        self.text = text
        self.cancelButtonText = cancelButtonText
        self.okButtonText = okButtonText
        self.alertType = alertType
        self.onCancel = onCancel
        self.onOk = onOk
    }
   
}

enum AlertType {
    case alert, choiceAlert
}

struct AlertView: View {
    @Binding var pageState:PageState
    let config: AlertConfig
    var body: some View {
        
        switch config.alertType {
            
        case .alert:
            ZStack {
                VStack(alignment: .leading) {
                    Text(config.title)
                        .font(Font.custom("Montserrat", size: 18).weight(.medium))
                        .foregroundColor(.black)

                    Text(config.text)
                        .font(Font.custom("Montserrat", size: 13).weight(.medium))
                        .foregroundColor(Color(red: 0.47, green: 0.47, blue: 0.47))
                        .padding(.vertical, 8)

                    HStack {
                        Button {
                            pageState = .stable
                            config.onOk?()
                        } label: {
                            VStack {
                                Text("Ok")
                                    .font(Font.custom("Montserrat", size: 15).weight(.bold))
                                    .foregroundColor(.white)
                            }
                            .foregroundColor(.clear)
                            .frame(height: 36)
                            .frame(maxWidth: .infinity)
                            .background(Color(red: 0.29, green: 0.73, blue: 0.73))
                            .cornerRadius(5)
                            .overlay(
                                RoundedRectangle(cornerRadius: 5)
                                    .inset(by: 0.50)
                                    .stroke(Color(red: 0.29, green: 0.73, blue: 0.73), lineWidth: 0.50)
                            )
                        }
                        }

                }
                .padding()
                .frame(maxWidth: .infinity,alignment: .leading)
                .background(.white)
                .padding()
            }

         
        case .choiceAlert:
            ZStack {
                VStack(alignment: .leading) {
                    Text(config.title)
                        .font(Font.custom("Montserrat", size: 18).weight(.medium))
                        .foregroundColor(.black)

                    Text(config.text)
                        .font(Font.custom("Montserrat", size: 13).weight(.medium))
                        .foregroundColor(Color(red: 0.47, green: 0.47, blue: 0.47))
                        .padding(.vertical, 8)

                    HStack {
                        Button {
                            pageState = .stable
                            config.onCancel?()
                            
                        } label: {
                            VStack {
                                Text(config.cancelButtonText)
                                    .font(Font.custom("Montserrat", size: 13).weight(.medium))
                                    .foregroundColor(Color(red: 0.29, green: 0.73, blue: 0.73))
                            }
                            .foregroundColor(.clear)
                            .frame(width: 77, height: 36)
                            .background(Color(red: 0.85, green: 0.85, blue: 0.85).opacity(0))
                            .cornerRadius(5)
                            .overlay(
                                RoundedRectangle(cornerRadius: 5)
                                    .inset(by: 0.50)
                                    .stroke(Color(red: 0.29, green: 0.73, blue: 0.73), lineWidth: 0.50)
                            )
                        }


                        Button {
                            pageState = .stable
                            config.onOk?()
                        } label: {
                            VStack {
                                Text(config.okButtonText)
                                    .font(Font.custom("Montserrat", size: 13).weight(.medium))
                                    .foregroundColor(.white)
                            }
                            .foregroundColor(.clear)
                            .frame(height: 36)
                            .frame(maxWidth: .infinity)
                            .background(Color(red: 0.29, green: 0.73, blue: 0.73))
                            .cornerRadius(5)
                            .overlay(
                                RoundedRectangle(cornerRadius: 5)
                                    .inset(by: 0.50)
                                    .stroke(Color(red: 0.29, green: 0.73, blue: 0.73), lineWidth: 0.50)
                            )
                        }

                    }
                }
                .padding()
                .frame(maxWidth: .infinity,alignment: .leading)
                .background(.white)
                .padding()
             
               
                
            }

            
        }
        
       
    }
}

struct AlertView_Previews: PreviewProvider {
    static var previews: some View {
        AlertView(pageState: .constant(.stable), config: .init(title: "Error", text: "error sample message"))
    }
}

