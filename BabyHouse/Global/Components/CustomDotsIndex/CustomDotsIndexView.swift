//
//  CustomDotsIndexView.swift
//  BabyHouse
//
//  Created by Apple on 02/09/23.
//

import SwiftUI

struct CustomDotsIndexView: View {
    // MARK: - Public Properties
  
    let numberOfPages: Int
    @Binding var currentIndex: Int
    let currentPageColor: Color?
    let isCircle:Bool
  
    // MARK: - Drawing Constants
  
    private let circleSize: CGFloat = 10
    private let circleSpacing: CGFloat = -8
  
    private let smallScale: CGFloat = 0.7
    
    private let primaryColor = Color.white
    private let secondaryColor = Color.black.opacity(0.2)
  
    // MARK: - Body
  
    @ViewBuilder
    var body: some View {
        HStack(spacing: circleSpacing) {
            ForEach(0 ..< numberOfPages, id: \.self) { index in // 1
                if shouldShowIndex(index, isCircle ? 1 : 2) {
                    HStack{
                        
                        if(isCircle){
                            Circle()
                                .fill(currentIndex == index ? currentPageColor ?? primaryColor : secondaryColor) // 2
                                .scaleEffect(currentIndex == index ? 1 : smallScale)
                    
                                .frame(width: circleSize, height: circleSize)
               
                                .transition(AnyTransition.opacity.combined(with: .scale)) // 3
                    
                                .id(index) // 4
                                .padding(.vertical, 8)
                                .padding(.horizontal, 8)
                            
                        }else{
                            
                       
                            ZStack{}
                                .frame(width: currentIndex == index ? 22.0.relativeWidth : 5.0.relativeWidth,
                                       height: currentIndex == index ? 4.0.relativeWidth : 5.0.relativeWidth, alignment: .center)
                                .background(Capsule()
                                    .fill(currentIndex == index ? currentPageColor ?? primaryColor : currentPageColor?.opacity(0.4) ?? primaryColor)
                                )
                                .padding(.leading, -4.0)
                                .id(index) // 4
                                .padding(.vertical)
                            
                            RoundedRectangle(cornerRadius: 100, style: .continuous)
//                                .fill(currentIndex == index ? currentPageColor ?? primaryColor : secondaryColor) // 2
                                .fill(.clear)
//                                .scaleEffect(smallScale)
                                .frame(width: currentIndex == index ? circleSize * 1 : circleSize, height: circleSize)
//                                .transition(AnyTransition.opacity.combined(with: .scale)) // 3
                                .id(index) // 4
                                .padding(.vertical, 8)
//                                .padding(.horizontal, 3)
                            
                        }
                        
                        
                    }.background(.clear)
                        .onTapGesture {
                            withAnimation {
                                currentIndex = index
                            }
                            
                        }
                }
            }
        }
    }
  
    // MARK: - Private Methods
  
    func shouldShowIndex(_ index: Int,_ firstCount:Int) -> Bool {
        ((currentIndex - firstCount) ... (currentIndex + firstCount)).contains(index)
    }
}




