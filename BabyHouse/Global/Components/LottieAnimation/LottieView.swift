//
//  LottieView.swift
//  BabyHouse
//
//  Created by Apple on 02/09/23.
//

import Lottie
import SwiftUI

/**
  <PERSON>tieView provide the view with the animation view of Lottie Files

  # Input Variable: #

  - `name`: variable contains the name of the json file of lottie files which included in project.

  - `loopMode`: mode of the animation in view.

  # Example #
 ```
     LottieView(name: "[JsonFileName]", loopMode: [.loop|.autoReverse|.playOnce etc.])

 ```
 */
struct LottieView: UIViewRepresentable {
    var name: String
    var loopMode: LottieLoopMode = .loop

    var animationView = LottieAnimationView()

    func makeUIView(context _: UIViewRepresentableContext<LottieView>) -> UIView {
        let view = UIView(frame: .zero)
        animationView.animation = LottieAnimation.named(name)
        animationView.contentMode = .scaleAspectFit
        animationView.loopMode = loopMode
        animationView.play()

        animationView.translatesAutoresizingMaskIntoConstraints = false
        view.addSubview(animationView)

        NSLayoutConstraint.activate([
            animationView.heightAnchor.constraint(equalTo: view.heightAnchor),
            animationView.widthAnchor.constraint(equalTo: view.widthAnchor),
        ])

        return view
    }
    
    

    func updateUIView(_: UIView, context _: UIViewRepresentableContext<LottieView>) {}
}

