//
//  App.swift
//  Zebeel
//
//  Created by <PERSON><PERSON> on 25/10/20.
//

// MARK: - Identifying App Environment

// ----------------------------------------------------------------------------------------------
enum App {
    enum Environment {
        static let production = "https://babyhousetoys.com/api/"
//        static let production = "https://babyhousetoys.com/babies/api/"
        static let stag = "https://babyhousetoys.com/demo/api/"
        static let upload = "https://babyhousetoys.com/demo/api/"
    }

    static let environment: NEnvironment = Network.environment
}

// MARK: - Session Signed Reporters App Session Endpoint

// ----------------------------------------------------------------------------------------------
protocol ZEndpoint: Endpoint {}

// MARK: - Default Session Requirements

// ----------------------------------------------------------------------------------------------
extension ZEndpoint {
    var production: String {
        App.Environment.production
    }

    var stag: String {
        App.Environment.stag
    }

    var parameters: Parameters { [:] }
    var httpMethod: HTTPMethod { .get }
    var httpHeaders: LightHTTPHeaders { [MHttpHeader.Key.contentType: MHttpHeader.Value.json] }
}
