//
//  Network.swift
//  LightNetwork
//
//  Created by sijo on 23/05/20.
//

import Foundation

// MARK: Public Type Alias

// ----------------------------------------------------------------------------------------------
/// An alias of Key String Value Any type Dictionary.
public typealias JSON = [String: Any]

/// An alias of Generic Result Type
public typealias Response<T> = (Result<T, NError>) -> Void

// MARK: Network Declaration

// ----------------------------------------------------------------------------------------------
public enum Network {
    // MARK: Initialization

    // ----------------------------------------------------------------------------------------------
    /// Specify the environment.
    public static var environment: NEnvironment = .production
    /// Specify the request time out.
    public static let requestTimeout: TimeInterval = 30.0
    /// Initialize default session.
    static let session = URLSession.shared
    
    // MARK: Dynamic Return Properties

    // ----------------------------------------------------------------------------------------------
    /// Identify the current network is reachable or not.
    public static var isAvailable: Bool { return Reachability()?.connection != Reachability.Connection.none }
    
    // MARK: Methods

    // ----------------------------------------------------------------------------------------------
    /// A request with Data response via Network.
    /// - Parameter type       : Specify the type of the request.
    /// - Parameter completion : Completed with Data.
    public static func request<T>(with type: T,
                                  _ completion: @escaping Response<Data>) where T: Endpoint
    {
        // Log detailed request information
        logAPIRequest(endpoint: type)

        var localizedError: NError? {
            didSet {
                if let localizedError = localizedError {
                    // Complete with error response.
                    completion(.failure(localizedError.error))
                    print(localizedError)
                }
            }
        }

        guard isAvailable else {
            localizedError = Reason.noNetwork.error
            return
        }
        
        /// Verify the request.
        guard let _ = type.url else {
            // Stop the execution the error occurs.
            localizedError = Reason.emptyURL.error
            // Early exit.
            return
        }
        
        /// Initialize data task.
        let task = session.dataTask(with: type.request) { data, response, error in

            /// Get status code
            let statusCode = (response as? HTTPURLResponse)?.statusCode ?? NError.StatusCode.unknown

            // Log detailed response information
            logAPIResponse(response: response as? HTTPURLResponse, data: data, error: error, endpoint: type)

            // Verify the request  error.
            if let error = error {
                /// Parse the error to NEError.
                localizedError = Reason.custom(error.localizedDescription, statusCode).error
                return
            }

            /// Verify the request data.
            guard let data = data else {
                /// Parse the error to NEError.
                localizedError = Reason.emptyData.error
                return
            }
            if statusCode == 401 {
                DispatchQueue.main.async {
                    print("Unauthorised")
                    NotificationCenter.default.post(name: Notification.Name("Unauthorisedaccess"), object: nil)
                }

                return
            }

            /// Parse data to requested type.
            // guard (200...299).contains(statusCode) else {
            guard statusCode == 200 else {
                print(data.jsonString)
                localizedError = type.localize(error: data)
                    .append(statusCode)

                return
            }
            // Complete with object.

            completion(.success(data))
        }
        // Resume the task.
        task.resume()
    }
}

public extension Result where Success == Data, Failure == NError {
    func decode<T>() -> Result<T, NError> where T: Decodable {
        do {
            let data = try get()
            let decoded = try JSONDecoder().decode(T.self, from: data)
            
            if let prettyJson = data.prettyPrintedJSONString {
                debugPrint(prettyJson)
            }
            
            return .success(decoded)
        } catch DecodingError.dataCorrupted(let context) {
            let data = (try? get()) ?? Data()
            return .failure(NError(code: NError.StatusCode.validRequest, message: "Data corrupted: \(context.debugDescription)"))
        } catch DecodingError.keyNotFound(let key, let context) {
            let data = (try? get()) ?? Data()
            return .failure(NError(code: NError.StatusCode.validRequest, message: "Key '\(key.stringValue)' not found: \(context.debugDescription)"))
        } catch DecodingError.typeMismatch(let type, let context) {
            let data = (try? get()) ?? Data()
            return .failure(NError(code: NError.StatusCode.validRequest, message: "Type mismatch for type '\(type)': \(context.debugDescription)"))
        } catch DecodingError.valueNotFound(let type, let context) {
            let data = (try? get()) ?? Data()
            return .failure(NError(code: NError.StatusCode.validRequest, message: "Value of type '\(type)' not found: \(context.debugDescription)"))
        } catch {
            let data = (try? get()) ?? Data()
            let message = data.jsonString ?? "Unknown error during decoding"
            return .failure(error as? NError ?? NError(code:NError.StatusCode.validRequest, message: message))
        }
    }
    func serialzedJSON() -> Result<JSON, NError> {
        do {
            let data = try get()
            let json = data.json
            return .success(json)
        } catch {
            let data = try? get()
            return .failure(error as? NError ?? .init(code: NError.StatusCode.validRequest,
                                                      message: data?.jsonString))
        }
    }
}

// MARK: - API Logging Functions
extension Network {

    /// Log detailed API request information for easy Postman testing
    static func logAPIRequest<T: Endpoint>(endpoint: T) {
        let isAuthRequest = endpoint.path.contains("generate-token") || endpoint.path.contains("login")

        if isAuthRequest {
            print("\n" + String(repeating: "=", count: 80))
            print("🔐 AUTHENTICATION API REQUEST")
            print(String(repeating: "=", count: 80))
        }

        print("\n📤 API REQUEST DETAILS:")
        print("┌─────────────────────────────────────────────────────────────────────────────┐")
        print("│ ENDPOINT: \(endpoint.path)")
        print("│ METHOD: \(endpoint.httpMethod.rawValue)")
        print("│ FULL URL: \(endpoint.url?.absoluteString ?? "N/A")")
        print("├─────────────────────────────────────────────────────────────────────────────┤")

        // Log headers
        print("│ HEADERS:")
        if endpoint.httpHeaders.isEmpty {
            print("│   (No custom headers)")
        } else {
            for (key, value) in endpoint.httpHeaders {
                print("│   \(key): \(value)")
            }
        }
        print("├─────────────────────────────────────────────────────────────────────────────┤")

        // Log request body
        print("│ REQUEST BODY:")
        if let httpBody = endpoint.httpBody {
            if let bodyString = String(data: httpBody, encoding: .utf8) {
                print("│   \(bodyString)")
            } else {
                print("│   (Binary data - \(httpBody.count) bytes)")
            }
        } else if !endpoint.parameters.isEmpty {
            print("│   Parameters: \(endpoint.parameters)")
        } else {
            print("│   (No body)")
        }
        print("└─────────────────────────────────────────────────────────────────────────────┘")

        // Postman-ready format
        if isAuthRequest {
            print("\n📋 POSTMAN COPY-PASTE FORMAT:")
            print("┌─────────────────────────────────────────────────────────────────────────────┐")
            print("│ Method: \(endpoint.httpMethod.rawValue)")
            print("│ URL: \(endpoint.url?.absoluteString ?? "N/A")")
            print("│")
            print("│ Headers:")
            for (key, value) in endpoint.httpHeaders {
                print("│   \(key): \(value)")
            }
            if let httpBody = endpoint.httpBody, let bodyString = String(data: httpBody, encoding: .utf8) {
                print("│")
                print("│ Body (raw JSON):")
                print("│   \(bodyString)")
            }
            print("└─────────────────────────────────────────────────────────────────────────────┘")
        }
    }

    /// Log detailed API response information
    static func logAPIResponse<T: Endpoint>(response: HTTPURLResponse?, data: Data?, error: Error?, endpoint: T) {
        let isAuthRequest = endpoint.path.contains("generate-token") || endpoint.path.contains("login")

        if isAuthRequest {
            print("\n🔐 AUTHENTICATION API RESPONSE")
            print(String(repeating: "=", count: 80))
        }

        print("\n📥 API RESPONSE DETAILS:")
        print("┌─────────────────────────────────────────────────────────────────────────────┐")
        print("│ ENDPOINT: \(endpoint.path)")
        print("│ STATUS CODE: \(response?.statusCode ?? -1)")
        print("├─────────────────────────────────────────────────────────────────────────────┤")

        // Log response headers
        print("│ RESPONSE HEADERS:")
        if let headers = response?.allHeaderFields {
            for (key, value) in headers {
                print("│   \(key): \(value)")
            }
        } else {
            print("│   (No headers)")
        }
        print("├─────────────────────────────────────────────────────────────────────────────┤")

        // Log response body
        print("│ RESPONSE BODY:")
        if let data = data {
            if let jsonString = data.prettyPrintedJSONString {
                print("│   \(jsonString)")
            } else if let bodyString = String(data: data, encoding: .utf8) {
                print("│   \(bodyString)")
            } else {
                print("│   (Binary data - \(data.count) bytes)")
            }
        } else {
            print("│   (No data)")
        }

        if let error = error {
            print("├─────────────────────────────────────────────────────────────────────────────┤")
            print("│ ERROR: \(error.localizedDescription)")
        }
        print("└─────────────────────────────────────────────────────────────────────────────┘")

        // Extract and log authentication tokens
        if isAuthRequest, let data = data {
            extractAndLogAuthTokens(from: data, endpoint: endpoint.path)
        }

        if isAuthRequest {
            print(String(repeating: "=", count: 80))
        }
    }

    /// Extract and log authentication tokens from response
    static func extractAndLogAuthTokens(from data: Data, endpoint: String) {
        do {
            if let json = try JSONSerialization.jsonObject(with: data, options: []) as? [String: Any] {
                print("\n🔑 AUTHENTICATION TOKENS EXTRACTED:")
                print("┌─────────────────────────────────────────────────────────────────────────────┐")

                if let dataDict = json["data"] as? [String: Any] {
                    if let accessToken = dataDict["access_token"] as? String {
                        print("│ ACCESS TOKEN: \(accessToken)")
                        print("│ BEARER TOKEN: Bearer \(accessToken)")
                    }
                    if let userID = dataDict["user_id"] as? String ?? dataDict["userid"] as? String {
                        print("│ USER ID: \(userID)")
                    }
                    if let tokenType = dataDict["token_type"] as? String {
                        print("│ TOKEN TYPE: \(tokenType)")
                    }
                    if let storeID = dataDict["store_id"] as? String {
                        print("│ STORE ID: \(storeID)")
                    }
                    if let countryCode = dataDict["country_code"] as? String {
                        print("│ COUNTRY CODE: \(countryCode)")
                    }
                }

                print("└─────────────────────────────────────────────────────────────────────────────┘")
            }
        } catch {
            print("│ Could not extract tokens: \(error.localizedDescription)")
        }
    }
}

public extension Data {
    var jsonString: String { return String(data: self, encoding: .utf8) ?? "No Response" }
    var json: JSON {
        let json = try? JSONSerialization
            .jsonObject(with: self, options: .allowFragments)
        return (json as? JSON) ?? ["Response": jsonString]
    }
}

private extension NErrorDecoder {
    static func decode(error data: Data) -> NError? {
        guard let error = try? JSONDecoder()
            .decode(Self.self, from: data) else { return nil }
        return error.error
    }
}

private extension Endpoint {
    func localize(error data: Data) -> NError {
        guard let error = (errorTypes + [NError.self])
            .compactMap({ $0.decode(error: data) })
            .first,
            ![NError.Description.unexpectedError,
              NError.Description.unableToParse].contains(error.localizedDescription) else { return Reason.custom(data.jsonString, 0).error }
        return error
    }
}

extension Data {
    var prettyPrintedJSONString: NSString? { /// NSString gives us a nice sanitized debugDescription
        guard let object = try? JSONSerialization.jsonObject(with: self, options: []),
              let data = try? JSONSerialization.data(withJSONObject: object, options: [.prettyPrinted]),
              let prettyPrintedString = NSString(data: data, encoding: String.Encoding.utf8.rawValue) else { return nil }

        return prettyPrintedString
    }
}
