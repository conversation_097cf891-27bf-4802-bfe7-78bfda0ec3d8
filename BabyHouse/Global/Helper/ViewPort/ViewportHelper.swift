//
//  ViewportHelper.swift
//  BabyHouse
//
//  Created by Apple on 02/09/23.
//

import Foundation
import UIKit

func getRelativeWidth(_ size: CGFloat) -> CGFloat {
    return size * (CGFloat(UIScreen.main.bounds.width) / 360.0)
}

func getRelativeHeight(_ size: CGFloat) -> CGFloat {
    return (size * (CGFloat(UIScreen.main.bounds.height) / 640.0)) * 0.97
}

func getRelativeFontSize(_ size: CGFloat) -> CGFloat {
    return size * (CGFloat(UIScreen.main.bounds.width) / 360.0)
}


extension Double {
    
    var relativeWidth:Double {
        self * (CGFloat(UIScreen.main.bounds.width) / 360.0)
    }
    
    var relativeHeight:Double {
        (self * (CGFloat(UIScreen.main.bounds.height) / 640.0)) * 0.97
    }
    
    var relativeFontSize:Double {
        self * (CGFloat(UIScreen.main.bounds.width) / 360.0)
    }

}

