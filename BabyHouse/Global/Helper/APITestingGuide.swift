//
//  APITestingGuide.swift
//  BabyHouse
//
//  API Testing and Debugging Guide
//

import Foundation

/*
 
 🔐 COMPREHENSIVE API AUTHENTICATION LOGGING GUIDE
 ================================================
 
 This file contains instructions for using the comprehensive API logging
 that has been added to the BabyHouse iOS app for testing authentication
 endpoints in Postman or other API clients.
 
 WHAT HAS BEEN ADDED:
 -------------------
 
 1. NETWORK LAYER LOGGING (Network.swift):
    - Detailed request logging (URL, method, headers, body)
    - Detailed response logging (status, headers, body)
    - Automatic token extraction from authentication responses
    - Formatted output for easy copying to Postman
 
 2. AUTHENTICATION-SPECIFIC LOGGING:
    - Guest login logging (SplashView.swift)
    - User login logging (LoginViewMode.swift, SignupVM.swift)
    - User registration logging (SignupVM.swift)
    - Home data API logging (HomeVM.swift)
 
 3. AUTHENTICATION LOGGER UTILITY (AuthenticationLogger.swift):
    - Current authentication state logging
    - Token validation
    - Postman collection generation
    - API endpoints documentation
 
 HOW TO USE FOR API TESTING:
 ---------------------------
 
 1. RUN THE APP:
    - Launch the app in Xcode
    - Check the console output for detailed API logs
    - Look for sections marked with 🔐 for authentication APIs
 
 2. GUEST TOKEN GENERATION:
    - The app automatically generates a guest token on first launch
    - Look for "🚀 INITIATING GUEST LOGIN" in console
    - Copy the "ACCESS TOKEN" from the response
    - Use this as "Bearer {token}" in Postman Authorization header
 
 3. USER LOGIN TESTING:
    - Use the guest token to authenticate user login requests
    - Look for "🚀 INITIATING USER LOGIN" in console
    - Copy the user access token from successful login response
    - Use this token for subsequent authenticated API calls
 
 4. POSTMAN SETUP:
    - Create environment variables:
      * base_url: https://babyhousetoys.com/api/
      * guest_token: (from guest login response)
      * user_token: (from user login response)
    - Use {{base_url}} and Bearer {{guest_token}} or Bearer {{user_token}}
 
 CONSOLE OUTPUT EXAMPLES:
 -----------------------
 
 When you run the app, you'll see output like this:
 
 ================================================================================
 🔐 AUTHENTICATION API REQUEST
 ================================================================================
 
 📤 API REQUEST DETAILS:
 ┌─────────────────────────────────────────────────────────────────────────────┐
 │ ENDPOINT: generate-token
 │ METHOD: POST
 │ FULL URL: https://babyhousetoys.com/api/generate-token
 ├─────────────────────────────────────────────────────────────────────────────┤
 │ HEADERS:
 │   (No custom headers)
 ├─────────────────────────────────────────────────────────────────────────────┤
 │ REQUEST BODY:
 │   device_token=ABC123&country_code=KW&clean_app=1
 └─────────────────────────────────────────────────────────────────────────────┘
 
 📋 POSTMAN COPY-PASTE FORMAT:
 ┌─────────────────────────────────────────────────────────────────────────────┐
 │ Method: POST
 │ URL: https://babyhousetoys.com/api/generate-token
 │
 │ Headers:
 │   Content-Type: application/x-www-form-urlencoded
 │
 │ Body (form-data):
 │   device_token=ABC123&country_code=KW&clean_app=1
 └─────────────────────────────────────────────────────────────────────────────┘
 
 🔐 AUTHENTICATION API RESPONSE
 ================================================================================
 
 📥 API RESPONSE DETAILS:
 ┌─────────────────────────────────────────────────────────────────────────────┐
 │ ENDPOINT: generate-token
 │ STATUS CODE: 200
 ├─────────────────────────────────────────────────────────────────────────────┤
 │ RESPONSE HEADERS:
 │   Content-Type: application/json
 │   Content-Length: 245
 ├─────────────────────────────────────────────────────────────────────────────┤
 │ RESPONSE BODY:
 │   {
 │     "status": 200,
 │     "error": false,
 │     "messages": "Success",
 │     "data": {
 │       "access_token": "eyJ0eXAiOiJKV1QiLCJhbGciOiJSUzI1NiJ9...",
 │       "user_id": "0",
 │       "token_type": "Bearer"
 │     }
 │   }
 └─────────────────────────────────────────────────────────────────────────────┘
 
 🔑 AUTHENTICATION TOKENS EXTRACTED:
 ┌─────────────────────────────────────────────────────────────────────────────┐
 │ ACCESS TOKEN: eyJ0eXAiOiJKV1QiLCJhbGciOiJSUzI1NiJ9...
 │ BEARER TOKEN: Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJSUzI1NiJ9...
 │ USER ID: 0
 │ TOKEN TYPE: Bearer
 └─────────────────────────────────────────────────────────────────────────────┘
 
 TESTING WORKFLOW:
 ----------------
 
 1. Launch app → Get guest token from console
 2. Copy guest token to Postman environment
 3. Test user login with guest token
 4. Copy user token from login response
 5. Test other APIs with user token
 
 DEBUGGING TIPS:
 --------------
 
 - All authentication requests are marked with 🔐
 - Look for ✅ for successful responses
 - Look for ❌ for failed requests
 - Token information is clearly marked with 🔑
 - Use AuthenticationLogger.logCurrentAuthState() to check current tokens
 
 MANUAL LOGGING CALLS:
 --------------------
 
 You can add these calls anywhere in your code for debugging:
 
 // Log current authentication state
 AuthenticationLogger.logCurrentAuthState()
 
 // Generate Postman collection
 AuthenticationLogger.generatePostmanCollection()
 
 // Validate current tokens
 AuthenticationLogger.validateTokens()
 
 // Clear all tokens (for testing)
 AuthenticationLogger.clearAllTokens()
 
 // Log API endpoints
 AuthenticationLogger.logAPIEndpoints()
 
 */

/// Example usage class for testing
class APITestingExample {
    
    /// Example of how to test guest login manually
    static func testGuestLogin() {
        print("🧪 TESTING GUEST LOGIN")
        
        let deviceId = UIDevice.current.identifierForVendor?.uuidString ?? "test-device"
        let params = guestLoginParam(
            device_token: deviceId,
            country_code: "KW",
            clean_app: "1"
        )
        
        AutheticationRequest.guestLogin(params).response { (result: Result<GuestModelData, NError>) in
            switch result {
            case .success(let response):
                print("✅ Guest login test successful")
                print("Token: \(response.data.accessToken)")
            case .failure(let error):
                print("❌ Guest login test failed: \(error.localizedDescription)")
            }
        }
    }
    
    /// Example of how to test user login manually
    static func testUserLogin(username: String, password: String) {
        print("🧪 TESTING USER LOGIN")
        
        guard let guestData = GuestData.getUserInfo() else {
            print("❌ No guest token available for user login test")
            return
        }
        
        let loginData = LoginData(
            user_name: username,
            password: password,
            unique_id: nil,
            name: nil,
            type: 1
        )
        
        let token = "Bearer " + guestData.accessToken
        
        HomeRequest.login(loginData, token).response { (result: Result<LoginResponse, NError>) in
            switch result {
            case .success(let response):
                print("✅ User login test successful")
                if let userData = response.data {
                    print("User Token: \(userData.accessToken)")
                }
            case .failure(let error):
                print("❌ User login test failed: \(error.localizedDescription)")
            }
        }
    }
}
