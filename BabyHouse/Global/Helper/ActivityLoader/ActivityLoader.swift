//
//  ActivityLoader.swift
//  BabyHouse
//
//  Created by Apple on 02/09/23.
//

import Lottie
import SwiftUI

struct ActivityLoaderBinder: View {
    @Binding var isLoaderShow: Bool
    var body: some View {
        if isLoaderShow {
            ZStack {
                LottieView(name: "custom_loader")
                    .frame(width: UIScreen.main.bounds.width / 3.5,
                           height: UIScreen.main.bounds.height / 3.5)
            }.frame(width: UIScreen.main.bounds.width, height: UIScreen.main.bounds.height,
                    alignment: .center)
                .background(Color.black.opacity(0.25).edgesIgnoringSafeArea(.all))
        }
    }
}

struct ActivityLoader: View {
    @State var isAnimating = false // <1>
    var body: some View {
        
        ZStack {
            LottieView(name: "custom_loader")
                .frame(width: UIScreen.main.bounds.width / 3.5,
                       height: UIScreen.main.bounds.height / 3.5)
            Circle()
                .fill(.white)
                .overlay(alignment: .center) {
                    SwiftUI.ProgressView()
                }
                .frame(width: 60, height: 60, alignment: .center)
                .offset(y:isAnimating ? 100 : 150)
                .animation(.interpolatingSpring(stiffness: 350, damping: 5, initialVelocity: 10).repeatForever(),value: isAnimating)
                .onAppear {
                    self.isAnimating = true // <2>
                }
        }.frame(width: UIScreen.main.bounds.width, height: UIScreen.main.bounds.height,
                alignment: .center)
            .background(Color.black.opacity(0.25).edgesIgnoringSafeArea(.all))
    }
}

