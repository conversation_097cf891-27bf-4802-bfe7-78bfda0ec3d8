/* 
  Localizable.strings
  BabyHouse

  Created by <PERSON><PERSON> on 26/03/22.
  
*/
"register_title_text"= "Register";
"email_text" = "Email ID";
"name_text" = "First Name";
"mobile_text" = "Mobile";
"password_text" = "Password";
"register_titleC_text" = "REGISTER";
"conferm_password_text" = "Confirm Password";
"registerNow_txt" = "REGISTER NOW";
"alreadyhave" = "Already have an account?";
"Login_txt" = "Login";
"LOGIN_NOW" = "LOGIN NOW";
"dont_have_account_txt" = "Don't have an accout?";
"register_text" = "Register";
"Forget_Password" = "Forget Password?";
"Remember_Me" = "Remember Me";
"http_error_text" = "Host not reachable";
"second_name" = "Last Name";
"confirm_txt" = "CONFIRM";
"order_note_text" = "ORDER NOTES";
"your_message_txt" = "Your message (optional)";
"character_remaning_txt" = "character remaning";
"" = "";
"" = "";
"no_prpoducts_text" = "No produts found";
"search_currency_text" = "Search Currency";
"choose_currency_text" = "Choose Currency";
"choose_country_text"  = "Choose Country";
"search_country_text" = "Search Country";
"cancel_text" = "Cancel";

"Areyousurewanttologout?_title_text" = "Are you sure want to logout?";
"Logout_title_text" = "Logout";
"Ok_title_text" = "OK";
"no_area_text" = "No area found";
"search_area_text" = "Search Area";
"select_area_text" = "Please select an area";
"check_out" = "Check Out";
"delivery_text" = "Delivery";
"delivery_date_text" = "Delivery Date";
"schedule_text" = "Schedule";
"Shipaddress_text" = "Shipping Address";
"shipaddressmsg_text" = "Sorry, There is no shipping address";
"Add_address_text" = "Add Address";
"normal_delivery_text" = "Normal Delivery 24 hours";
"expres_delivery_text" = "Express Delivery in 4 hours";
"payment_method_text" = "Payment Method";
"knet_msg" = "You can pay with your Knet card";
"visa_card_msg" = "You can pay with Mater, Visa card";
"cod" = "Cash On Delivery";
"promocod_msg" = "Promo Code";
"apply_text" = "Apply";
"payment_summary_text" = "Payment Summary";
"paynow_text" = "Pay Now";
"bagtotal_text" = "Bag Total";
"items_text" = "items";
"address_select_txt" = "Please select a shipping address";
"order_placed_txt" = "Order Placed";
"order_pacled_txt" = "Order Packed";
"order_shipped_txt" = "Order Shipped";
"order_delivered_txt" = "Order Delivered";
"order_placed_msg" = "Your Order has been placed.";
"order_pacled_msg" = "Item Packed by Courier Partner.";
"order_shipped_msg" = "Your item is out for delivery.";
"order_delivered_msg" = "Your item has been delivered";
 "payment_success" = "Payment Successful !";
"payment_success_msg" = "You have successfully placed your order. A confirmation message has been sent to you via e mail.";
"amount_paid" = "Amount Paid";
"order_id" = "Order ID";
"no_address_text" = "No address is available";
