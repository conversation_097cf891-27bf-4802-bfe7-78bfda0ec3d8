//
//  AuthenticationLogger.swift
//  BabyHouse
//
//  Created for API Testing and Debugging
//

import Foundation

/// Utility class for comprehensive authentication API logging
class AuthenticationLogger {
    
    /// Log current authentication state
    static func logCurrentAuthState() {
        print("\n🔍 CURRENT AUTHENTICATION STATE")
        print(String(repeating: "=", count: 60))
        
        // Check guest token
        if let guestData = GuestData.getUserInfo() {
            print("👤 GUEST TOKEN:")
            print("  Access Token: \(guestData.accessToken)")
            print("  User ID: \(guestData.userID)")
            print("  Token Type: \(guestData.tokenType)")
            print("  Store ID: \(guestData.storeID)")
            print("  Country ID: \(guestData.countryID)")
            print("  Country Code: \(guestData.countryCode)")
            print("  Language: \(guestData.language)")
            print("  Currency ID: \(guestData.currencyID)")
        } else {
            print("❌ No guest token found")
        }
        
        // Check user token
        if let userData = UserData.getuserInfo() {
            print("\n🔐 USER TOKEN:")
            print("  Access Token: \(userData.accessToken)")
            print("  User ID: \(userData.userid)")
            print("  Token Type: \(userData.tokenType)")
            print("  Bearer Header: Bearer \(userData.accessToken)")
        } else {
            print("\n❌ No user token found")
        }
        
        print(String(repeating: "=", count: 60))
    }
    
    /// Generate Postman collection format for authentication endpoints
    static func generatePostmanCollection() {
        print("\n📋 POSTMAN COLLECTION FOR AUTHENTICATION")
        print(String(repeating: "=", count: 80))
        
        let baseURL = "https://babyhousetoys.com/api/"
        
        print("""
        
        1. GUEST TOKEN GENERATION:
        ┌─────────────────────────────────────────────────────────────────────────────┐
        │ Method: POST                                                                │
        │ URL: \(baseURL)generate-token                                          │
        │                                                                             │
        │ Headers:                                                                    │
        │   Content-Type: application/x-www-form-urlencoded                          │
        │                                                                             │
        │ Body (form-data):                                                          │
        │   device_token: {{$guid}}                                                  │
        │   country_code: KW                                                          │
        │   clean_app: 1                                                              │
        └─────────────────────────────────────────────────────────────────────────────┘
        
        2. USER LOGIN:
        ┌─────────────────────────────────────────────────────────────────────────────┐
        │ Method: POST                                                                │
        │ URL: \(baseURL)login                                                   │
        │                                                                             │
        │ Headers:                                                                    │
        │   Authorization: Bearer {{guest_token}}                                    │
        │   Content-Type: application/x-www-form-urlencoded                          │
        │                                                                             │
        │ Body (form-data):                                                          │
        │   user_name: <EMAIL>                                        │
        │   password: your_password                                                   │
        │   type: 1                                                                   │
        └─────────────────────────────────────────────────────────────────────────────┘
        
        3. USER REGISTRATION:
        ┌─────────────────────────────────────────────────────────────────────────────┐
        │ Method: POST                                                                │
        │ URL: \(baseURL)register                                                │
        │                                                                             │
        │ Headers:                                                                    │
        │   Authorization: Bearer {{guest_token}}                                    │
        │   Content-Type: application/x-www-form-urlencoded                          │
        │                                                                             │
        │ Body (form-data):                                                          │
        │   email: <EMAIL>                                                   │
        │   password: password123                                                     │
        │   confirm_password: password123                                             │
        │   first_name: John                                                          │
        │   last_name: Doe                                                            │
        │   phone: +96512345678                                                       │
        └─────────────────────────────────────────────────────────────────────────────┘
        
        ENVIRONMENT VARIABLES FOR POSTMAN:
        • base_url: https://babyhousetoys.com/api/
        • guest_token: (copy from guest login response)
        • user_token: (copy from user login response)
        
        """)
        
        print(String(repeating: "=", count: 80))
    }
    
    /// Log token validation status
    static func validateTokens() {
        print("\n🔍 TOKEN VALIDATION")
        print(String(repeating: "-", count: 50))
        
        // Validate guest token
        if let guestData = GuestData.getUserInfo() {
            let isValidFormat = guestData.accessToken.count > 10 && !guestData.accessToken.isEmpty
            print("👤 Guest Token: \(isValidFormat ? "✅ Valid format" : "❌ Invalid format")")
            print("   Length: \(guestData.accessToken.count) characters")
        } else {
            print("👤 Guest Token: ❌ Not found")
        }
        
        // Validate user token
        if let userData = UserData.getuserInfo() {
            let isValidFormat = userData.accessToken.count > 10 && !userData.accessToken.isEmpty
            print("🔐 User Token: \(isValidFormat ? "✅ Valid format" : "❌ Invalid format")")
            print("   Length: \(userData.accessToken.count) characters")
        } else {
            print("🔐 User Token: ❌ Not found")
        }
        
        print(String(repeating: "-", count: 50))
    }
    
    /// Clear all authentication tokens (for testing)
    static func clearAllTokens() {
        print("\n🗑️ CLEARING ALL AUTHENTICATION TOKENS")
        UserDefaults.standard.removeObject(forKey: "GustUserinfo")
        UserDefaults.standard.removeObject(forKey: "Userinfo")
        UserDefaults.standard.synchronize()
        print("✅ All tokens cleared from UserDefaults")
    }
    
    /// Log API endpoint information for testing
    static func logAPIEndpoints() {
        print("\n📡 AUTHENTICATION API ENDPOINTS")
        print(String(repeating: "=", count: 60))
        
        let endpoints = [
            ("Guest Token", "POST", "generate-token", "Get guest access token"),
            ("User Login", "POST", "login", "Authenticate user with credentials"),
            ("User Registration", "POST", "register", "Create new user account"),
            ("Send Email OTP", "POST", "send-email-otp", "Send OTP to email"),
            ("Verify Email OTP", "POST", "verify-email-otp", "Verify email OTP"),
            ("Reset Password", "POST", "reset-password", "Reset user password"),
            ("Logout", "POST", "logout", "Logout user and invalidate token")
        ]
        
        for (name, method, path, description) in endpoints {
            print("🔗 \(name):")
            print("   Method: \(method)")
            print("   Path: \(path)")
            print("   Description: \(description)")
            print("")
        }
        
        print(String(repeating: "=", count: 60))
    }
}

/// Extension to add authentication logging to view controllers
extension UIViewController {
    
    /// Quick method to log current auth state from any view controller
    func logAuthState() {
        AuthenticationLogger.logCurrentAuthState()
    }
    
    /// Quick method to generate Postman collection from any view controller
    func generatePostmanCollection() {
        AuthenticationLogger.generatePostmanCollection()
    }
}
