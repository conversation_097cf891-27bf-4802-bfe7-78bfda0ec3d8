//
//  Notification.swift
//  BabyHouse
//
//  Created by Apple on 31/08/23.
//

import Foundation

extension NSNotification.Name {
    static let reset = Notification.Name("reset")
    static let refreshHome = Notification.Name("refresh_home")
    static let updateWishlistBadge = Notification.Name("update_wishlist_badge")
    static let cartSummeryApiCall = Notification.Name("cart_summery_api_call")
//    static let cartCount = Notification.Name("cart_count")
    static let notificationCount = Notification.Name("notification_count")
}
