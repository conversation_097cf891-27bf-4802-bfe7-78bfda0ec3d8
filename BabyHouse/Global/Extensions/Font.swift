//
//  Font.swift
//  BabyHouse
//
//  Created by Apple on 13/09/23.
//

import SwiftUI

public extension Font {
    /// Create a font with the large title text style.
    static var largeTitle: Font {
        return Font.custom("Metropolis-Regular", size: UIFont.preferredFont(forTextStyle: .largeTitle).pointSize)
    }

    /// Create a font with the title text style.
    static var title: Font {
        return Font.custom("Metropolis-Regular", size: UIFont.preferredFont(forTextStyle: .title1).pointSize)
    }

    /// Create a font with the headline text style.
    static var headline: Font {
        return Font.custom("Metropolis-Regular", size: UIFont.preferredFont(forTextStyle: .headline).pointSize)
    }

    /// Create a font with the subheadline text style.
    static var subheadline: Font {
        return Font.custom("Metropolis-Light", size: UIFont.preferredFont(forTextStyle: .subheadline).pointSize)
    }

    /// Create a font with the body text style.
    static var body: Font {
        return Font.custom("Metropolis-Regular", size: UIFont.preferredFont(forTextStyle: .body).pointSize)
    }

    /// Create a font with the callout text style.
    static var callout: Font {
        return Font.custom("Metropolis-Regular", size: UIFont.preferredFont(forTextStyle: .callout).pointSize)
    }

    /// Create a font with the footnote text style.
    static var footnote: Font {
        return Font.custom("Metropolis-Regular", size: UIFont.preferredFont(forTextStyle: .footnote).pointSize)
    }

    /// Create a font with the caption text style.
    static var caption: Font {
        return Font.custom("Metropolis-Regular", size: UIFont.preferredFont(forTextStyle: .caption1).pointSize)
    }

    static func system(size: CGFloat, weight: Font.Weight = .regular, design: Font.Design = .default) -> Font {
        var font = "Metropolis-Regular"
        switch weight {
        case .bold: font = "Metropolis-Bold"
        case .heavy: font = "Metropolis-Black"
        case .light: font = "Metropolis-Light"
        case .regular: font = "Metropolis-Regular"
        case .medium: font = "Metropolis-Medium"
        case .semibold: font = "Metropolis-SemiBold"
        case .thin: font = "Metropolis-Light"
        case .ultraLight: font = "Metropolis-Light"
        default: break
        }
        return Font.custom(font, size: size)
    }
}
