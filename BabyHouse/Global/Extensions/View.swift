//
//  View.swift
//  BabyHouse
//
//  Created by Apple on 02/09/23.
//

import SwiftUI

/// View Extensions

//Define a custom SwiftUI Shape called "RoundedCorners".
struct RoundedCorners: Shape {
    //Provide variables to control the radius and which corners to round.
    var radius: CGFloat
    var corners: UIRectCorner
    
    //Implement the path(in:) method required for the Shape protocol.
    func path(in rect: CGRect) -> Path {
        //Create a UIBezierPath to represent the custom rounded rectangle path.
        let path = UIBezierPath(
            roundedRect: rect,
            byRoundingCorners: corners,
            cornerRadii: CGSize(width: radius, height: radius)
        )
        
        //Convert the UIBezierPath to a SwiftUI Path and return it.
        return Path(path.cgPath)
    }
}

struct CornerRadiusStyle: ViewModifier {
    var radius: CGFloat
    var corners: UIRectCorner
    
    struct CornerRadiusShape: Shape {

        var radius = CGFloat.infinity
        var corners = UIRectCorner.allCorners

        func path(in rect: CGRect) -> Path {
            let path = UIBezierPath(roundedRect: rect, byRoundingCorners: corners, cornerRadii: CGSize(width: radius, height: radius))
            return Path(path.cgPath)
        }
    }

    func body(content: Content) -> some View {
        content
            .clipShape(CornerRadiusShape(radius: radius, corners: corners))
    }
}

extension View {
    func cornerRadius(_ radius: CGFloat, corners: UIRectCorner) -> some View {
        ModifiedContent(content: self, modifier: CornerRadiusStyle(radius: radius, corners: corners))
    }
}

extension View {
    /// Navigate to a new view.
    /// - Parameters:
    ///   - view: View to navigate to.
    ///   - binding: Only navigates when this condition is `true`.
    ///   - title: Title for navigation bar

    
    
    func navigate<NewView: View>(to view: NewView, when binding: Binding<Bool>, title navigation: String? = nil) -> some View {
//        NavigationView {
        ZStack {
            self
                .navigationBarTitle(navigation ?? "")
//                .navigationBarHidden(true)

            NavigationLink(
                destination: view
                    .navigationBarTitle(navigation ?? "")
                    .navigationBarHidden(false),
                isActive: binding
            ) {
                EmptyView()
            }.isDetailLink(false)
        }
//        }
//        .navigationViewStyle(.stack)
    }
    
    /// Applies the given transform if the given condition evaluates to `true`.
    /// - Parameters:
    ///   - condition: The condition to evaluate.
    ///   - transform: The transform to apply to the source `View`.
    /// - Returns: Either the original `View` or the modified `View` if the condition is `true`.
    @ViewBuilder func `if`<Content: View>(_ condition: Bool, transform: (Self) -> Content) -> some View {
        if condition {
            withAnimation {
                transform(self)
            }

        } else {
            withAnimation {
                self
            }
        }
    }
    
}

extension View {
    /// Hide or show the view based on a boolean value.
    ///
    /// Example for visibility:
    ///
    ///     Text("Label")
    ///         .isHidden(true)
    ///
    /// Example for complete removal:
    ///
    ///     Text("Label")
    ///         .isHidden(true, remove: true)
    ///
    /// - Parameters:
    ///   - hidden: Set to `false` to show the view. Set to `true` to hide the view.
    ///   - remove: Boolean value indicating whether or not to remove the view.
    @ViewBuilder func removeView(_ hidden: Bool, remove: Bool = false) -> some View {
        if hidden {
            if !remove {
                self.hidden()
            }
        } else {
            self
        }
    }
    
    @ViewBuilder func showView(_ condition: Bool) -> some View {
        if condition {
            self
        }
    }

}

extension UIScreen {
    static let screenWidth = UIScreen.main.bounds.size.width
    static let screenHeight = UIScreen.main.bounds.size.height
    static let screenSize = UIScreen.main.bounds.size

    static var topSafeArea: CGFloat {
        let keyWindow = UIApplication.shared.connectedScenes
            .filter { $0.activationState == .foregroundActive }
            .map { $0 as? UIWindowScene }
            .compactMap { $0 }
            .first?.windows
            .filter { $0.isKeyWindow }.first

        return (keyWindow?.safeAreaInsets.top) ?? 0
    }

    static var bottomSafeArea: CGFloat {
        let keyWindow = UIApplication.shared.connectedScenes
            .filter { $0.activationState == .foregroundActive }
            .map { $0 as? UIWindowScene }
            .compactMap { $0 }
            .first?.windows
            .filter { $0.isKeyWindow }.first

        return (keyWindow?.safeAreaInsets.bottom) ?? 0
    }
}


extension String {
    
    func withoutHtmlTags() -> String {
        let str = replacingOccurrences(of: "<style>[^>]+</style>", with: "", options: .regularExpression, range: nil)
        return str.replacingOccurrences(of: "<[^>]+>", with: "", options: .regularExpression, range: nil)
    }
    
    func repeated(count: Int) -> String {
        return String(repeating: self + " ", count: count)
    }
}

extension Date {
    func format(with format: String = "yyyy-MM-dd") -> String {
        let dateFormatter = DateFormatter()
        dateFormatter.dateFormat = format
        return dateFormatter.string(from: self)
    }
    
    /// Returns the amount of days from another date
    func days(from date: Date) -> Int {
        Calendar.current.dateComponents([.day], from: date, to: self).day ?? 0
    }
    
    func add(day: Int) -> Date {
        Calendar.current.date(byAdding: DateComponents(day: day), to: self, wrappingComponents: false) ?? self
    }
    
    func add(hour: Int) -> Date {
        Calendar.current.date(byAdding: DateComponents(hour: hour), to: self, wrappingComponents: false) ?? self
    }
    
    func checkDate(date: Date) -> Bool {
        if date < Date() {
            //  print("date1 is earlier than date2")
            return true
        }
        return false
    }
    
    var thirdDay: Date {
        add(day: 3)
    }
}

extension String {
    func format(with format: String = "yyyy-MM-dd") -> Date {
        let dateFormatter = DateFormatter()
        dateFormatter.dateFormat = format
        return dateFormatter.date(from: self) ?? Date()
    }
}
