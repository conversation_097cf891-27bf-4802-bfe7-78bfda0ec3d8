//
//  CategoryVM.swift
//  BabyHouse
//
//  Created by <PERSON> on 23/03/22.
//

import Foundation

class CategoryVM: BaseViewModel {
    /// DoctorId
    var dataSource: Int?
  
    required init(with dataSource: Int?) {
        self.dataSource = dataSource
    }
    
    func categoryList(completion: ((CategoryResponse?) -> Void)?) {
        
    }
    
}



struct CategoryResponse: Codable {
    let status: Int
    let error: Bool
    let messages: String
    let data: [Categoryies]
}

// MARK: - Datum
struct Categoryies: Codable {
    let id, categoryName, parent: String
    let icon: String
    let status, deleteStat: String
    let children: [Categoryies]
    let level: Int

    enum CodingKeys: String, CodingKey {
        case id
        case categoryName = "category_name"
        case parent, icon, status
        case deleteStat = "delete_stat"
        case children, level
    }
}
