<?xml version="1.0" encoding="UTF-8"?>
<document type="com.apple.InterfaceBuilder3.CocoaTouch.XIB" version="3.0" toolsVersion="17701" targetRuntime="iOS.CocoaTouch" propertyAccessControl="none" useAutolayout="YES" useTraitCollections="YES" useSafeAreas="YES" colorMatched="YES">
    <device id="retina6_1" orientation="portrait" appearance="light"/>
    <dependencies>
        <deployment identifier="iOS"/>
        <plugIn identifier="com.apple.InterfaceBuilder.IBCocoaTouchPlugin" version="17703"/>
        <capability name="Safe area layout guides" minToolsVersion="9.0"/>
        <capability name="documents saved in the Xcode 8 format" minToolsVersion="8.0"/>
    </dependencies>
    <objects>
        <placeholder placeholderIdentifier="IBFilesOwner" id="-1" userLabel="File's Owner"/>
        <placeholder placeholderIdentifier="IBFirstResponder" id="-2" customClass="UIResponder"/>
        <tableViewCell contentMode="scaleToFill" selectionStyle="default" indentationWidth="10" rowHeight="72" id="KGk-i7-Jjw" customClass="CategoryTableViewCell" customModule="Saydality" customModuleProvider="target">
            <rect key="frame" x="0.0" y="0.0" width="472" height="72"/>
            <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMaxY="YES"/>
            <tableViewCellContentView key="contentView" opaque="NO" clipsSubviews="YES" multipleTouchEnabled="YES" contentMode="center" tableViewCell="KGk-i7-Jjw" id="H2p-sc-9uM">
                <rect key="frame" x="0.0" y="0.0" width="472" height="72"/>
                <autoresizingMask key="autoresizingMask"/>
                <subviews>
                    <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="fQV-MO-Vgd">
                        <rect key="frame" x="0.0" y="0.0" width="472" height="72"/>
                        <subviews>
                            <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="8KI-Xv-1gx">
                                <rect key="frame" x="0.0" y="0.0" width="472" height="62"/>
                                <subviews>
                                    <view clipsSubviews="YES" contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="Hw6-Jz-QQr">
                                        <rect key="frame" x="0.0" y="0.0" width="472" height="62"/>
                                        <subviews>
                                            <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="View All" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="Rs7-qd-fTr">
                                                <rect key="frame" x="25" y="15" width="404" height="27"/>
                                                <fontDescription key="fontDescription" type="system" pointSize="17"/>
                                                <color key="textColor" white="0.0" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                                <nil key="highlightedColor"/>
                                            </label>
                                            <imageView clipsSubviews="YES" userInteractionEnabled="NO" contentMode="scaleAspectFit" horizontalHuggingPriority="251" verticalHuggingPriority="251" image="rightB" translatesAutoresizingMaskIntoConstraints="NO" id="vco-3V-iHa">
                                                <rect key="frame" x="439" y="24.5" width="13" height="13"/>
                                                <constraints>
                                                    <constraint firstAttribute="height" constant="13" id="c4G-HU-7ej"/>
                                                    <constraint firstAttribute="width" constant="13" id="hU0-l0-zpv"/>
                                                </constraints>
                                            </imageView>
                                            <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="upE-f4-Dqv">
                                                <rect key="frame" x="-100" y="61" width="672" height="1"/>
                                                <color key="backgroundColor" white="0.66666666669999997" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                                <constraints>
                                                    <constraint firstAttribute="height" constant="0.80000000000000004" id="9Ef-Wg-bws"/>
                                                </constraints>
                                                <fontDescription key="fontDescription" type="system" pointSize="17"/>
                                                <nil key="textColor"/>
                                                <nil key="highlightedColor"/>
                                            </label>
                                        </subviews>
                                        <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                        <constraints>
                                            <constraint firstItem="Rs7-qd-fTr" firstAttribute="leading" secondItem="Hw6-Jz-QQr" secondAttribute="leading" constant="25" id="BkF-Q2-aap"/>
                                            <constraint firstItem="vco-3V-iHa" firstAttribute="centerY" secondItem="Hw6-Jz-QQr" secondAttribute="centerY" id="Dkz-yZ-GZe"/>
                                            <constraint firstAttribute="bottom" secondItem="upE-f4-Dqv" secondAttribute="bottom" id="GRY-ue-dda"/>
                                            <constraint firstItem="vco-3V-iHa" firstAttribute="leading" secondItem="Rs7-qd-fTr" secondAttribute="trailing" constant="10" id="Tar-pL-GcP"/>
                                            <constraint firstAttribute="bottom" secondItem="Rs7-qd-fTr" secondAttribute="bottom" constant="20" id="dTN-Lc-gUF"/>
                                            <constraint firstAttribute="trailing" secondItem="upE-f4-Dqv" secondAttribute="trailing" constant="-100" id="e9J-fi-NFj"/>
                                            <constraint firstItem="upE-f4-Dqv" firstAttribute="leading" secondItem="Hw6-Jz-QQr" secondAttribute="leading" constant="-100" id="fId-Xp-Vpo"/>
                                            <constraint firstAttribute="trailing" secondItem="vco-3V-iHa" secondAttribute="trailing" constant="20" id="nIq-tr-A84"/>
                                            <constraint firstItem="Rs7-qd-fTr" firstAttribute="top" secondItem="Hw6-Jz-QQr" secondAttribute="top" constant="15" id="uMz-qJ-bWd"/>
                                        </constraints>
                                    </view>
                                </subviews>
                                <color key="backgroundColor" red="0.90588235289999997" green="0.93333333330000001" blue="0.99215686270000003" alpha="1" colorSpace="calibratedRGB"/>
                                <constraints>
                                    <constraint firstItem="Hw6-Jz-QQr" firstAttribute="leading" secondItem="8KI-Xv-1gx" secondAttribute="leading" id="EOe-VS-AV8"/>
                                    <constraint firstItem="Hw6-Jz-QQr" firstAttribute="top" secondItem="8KI-Xv-1gx" secondAttribute="top" id="JaA-0w-1Ay"/>
                                    <constraint firstAttribute="trailing" secondItem="Hw6-Jz-QQr" secondAttribute="trailing" id="Qmt-Rn-cfG"/>
                                    <constraint firstAttribute="bottom" secondItem="Hw6-Jz-QQr" secondAttribute="bottom" id="dQY-du-2Hu"/>
                                </constraints>
                            </view>
                        </subviews>
                        <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                        <constraints>
                            <constraint firstItem="8KI-Xv-1gx" firstAttribute="leading" secondItem="fQV-MO-Vgd" secondAttribute="leading" id="7hC-kV-SSp"/>
                            <constraint firstAttribute="bottom" secondItem="8KI-Xv-1gx" secondAttribute="bottom" constant="10" id="Ntx-Gp-jJV"/>
                            <constraint firstItem="8KI-Xv-1gx" firstAttribute="top" secondItem="fQV-MO-Vgd" secondAttribute="top" id="PrV-8C-PPE"/>
                            <constraint firstAttribute="trailing" secondItem="8KI-Xv-1gx" secondAttribute="trailing" id="so1-lC-rhZ"/>
                        </constraints>
                    </view>
                </subviews>
                <constraints>
                    <constraint firstAttribute="trailing" secondItem="fQV-MO-Vgd" secondAttribute="trailing" id="AOx-tR-yY3"/>
                    <constraint firstItem="fQV-MO-Vgd" firstAttribute="top" secondItem="H2p-sc-9uM" secondAttribute="top" id="Sfp-cx-7Xp"/>
                    <constraint firstItem="fQV-MO-Vgd" firstAttribute="leading" secondItem="H2p-sc-9uM" secondAttribute="leading" id="o23-o7-B7n"/>
                    <constraint firstAttribute="bottom" secondItem="fQV-MO-Vgd" secondAttribute="bottom" id="pOJ-kB-dpv"/>
                </constraints>
            </tableViewCellContentView>
            <viewLayoutGuide key="safeArea" id="njF-e1-oar"/>
            <color key="backgroundColor" red="0.90588235294117647" green="0.93333333333333335" blue="0.99215686274509807" alpha="1" colorSpace="calibratedRGB"/>
            <connections>
                <outlet property="containerView" destination="Hw6-Jz-QQr" id="oXu-fJ-SSf"/>
                <outlet property="parentView" destination="8KI-Xv-1gx" id="svy-ID-jHB"/>
                <outlet property="viewall" destination="Rs7-qd-fTr" id="5QW-nK-5YT"/>
            </connections>
            <point key="canvasLocation" x="26.086956521739133" y="32.142857142857139"/>
        </tableViewCell>
    </objects>
    <resources>
        <image name="rightB" width="512" height="512"/>
    </resources>
</document>
