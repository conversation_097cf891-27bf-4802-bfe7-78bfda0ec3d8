<?xml version="1.0" encoding="UTF-8"?>
<document type="com.apple.InterfaceBuilder3.CocoaTouch.Storyboard.XIB" version="3.0" toolsVersion="19529" targetRuntime="iOS.CocoaTouch" propertyAccessControl="none" useAutolayout="YES" useTraitCollections="YES" useSafeAreas="YES" colorMatched="YES">
    <device id="retina6_1" orientation="portrait" appearance="light"/>
    <dependencies>
        <deployment identifier="iOS"/>
        <plugIn identifier="com.apple.InterfaceBuilder.IBCocoaTouchPlugin" version="19519"/>
        <capability name="Safe area layout guides" minToolsVersion="9.0"/>
        <capability name="System colors in document resources" minToolsVersion="11.0"/>
        <capability name="documents saved in the Xcode 8 format" minToolsVersion="8.0"/>
    </dependencies>
    <scenes>
        <!--View Controller-->
        <scene sceneID="s0d-6b-0kx">
            <objects>
                <viewController id="Y6W-OH-hqX" sceneMemberID="viewController">
                    <view key="view" contentMode="scaleToFill" id="5EZ-qb-Rvc">
                        <rect key="frame" x="0.0" y="0.0" width="414" height="896"/>
                        <autoresizingMask key="autoresizingMask" widthSizable="YES" heightSizable="YES"/>
                        <viewLayoutGuide key="safeArea" id="vDu-zF-Fre"/>
                        <color key="backgroundColor" systemColor="systemBackgroundColor"/>
                    </view>
                </viewController>
                <placeholder placeholderIdentifier="IBFirstResponder" id="Ief-a0-LHa" userLabel="First Responder" customClass="UIResponder" sceneMemberID="firstResponder"/>
            </objects>
            <point key="canvasLocation" x="-226" y="110"/>
        </scene>
        <!--CategoryVC-->
        <scene sceneID="Hgd-Ru-5IG">
            <objects>
                <viewController storyboardIdentifier="CategoryVC" id="aJS-Ah-pbw" customClass="CategoryVC" customModule="BabyHouse" customModuleProvider="target" sceneMemberID="viewController">
                    <view key="view" contentMode="scaleToFill" id="abv-p6-p1A">
                        <rect key="frame" x="0.0" y="0.0" width="414" height="896"/>
                        <autoresizingMask key="autoresizingMask" widthSizable="YES" heightSizable="YES"/>
                        <subviews>
                            <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="p1K-F0-wKh">
                                <rect key="frame" x="0.0" y="44" width="414" height="64"/>
                                <subviews>
                                    <imageView clipsSubviews="YES" userInteractionEnabled="NO" contentMode="scaleAspectFit" horizontalHuggingPriority="251" verticalHuggingPriority="251" image="arrow" translatesAutoresizingMaskIntoConstraints="NO" id="zFb-wP-JP7">
                                        <rect key="frame" x="10" y="23" width="18" height="18"/>
                                        <constraints>
                                            <constraint firstAttribute="width" constant="18" id="2XO-oz-Bk1"/>
                                            <constraint firstAttribute="height" constant="18" id="8Wa-L5-zW1"/>
                                        </constraints>
                                    </imageView>
                                    <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Categories" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="gFX-PJ-Wch">
                                        <rect key="frame" x="48" y="8" width="229" height="48"/>
                                        <fontDescription key="fontDescription" type="system" pointSize="17"/>
                                        <color key="textColor" red="0.0" green="0.058823529409999999" blue="0.50196078430000002" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                                        <nil key="highlightedColor"/>
                                    </label>
                                    <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" buttonType="system" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="nd4-kp-sQ3">
                                        <rect key="frame" x="0.0" y="0.0" width="100" height="64"/>
                                        <constraints>
                                            <constraint firstAttribute="width" constant="100" id="fY7-r6-Ap8"/>
                                        </constraints>
                                    </button>
                                    <stackView opaque="NO" contentMode="scaleToFill" spacing="15" translatesAutoresizingMaskIntoConstraints="NO" id="cCu-rT-ywv">
                                        <rect key="frame" x="287" y="23" width="117" height="18"/>
                                        <subviews>
                                            <imageView clipsSubviews="YES" userInteractionEnabled="NO" contentMode="scaleAspectFit" horizontalHuggingPriority="251" verticalHuggingPriority="251" image="share" translatesAutoresizingMaskIntoConstraints="NO" id="wPe-NQ-CbH">
                                                <rect key="frame" x="0.0" y="0.0" width="18" height="18"/>
                                                <constraints>
                                                    <constraint firstAttribute="width" constant="18" id="IlC-Y2-O96"/>
                                                    <constraint firstAttribute="height" constant="18" id="aCT-D0-gZW"/>
                                                </constraints>
                                            </imageView>
                                            <imageView clipsSubviews="YES" userInteractionEnabled="NO" contentMode="scaleAspectFit" horizontalHuggingPriority="251" verticalHuggingPriority="251" image="Group 17652" translatesAutoresizingMaskIntoConstraints="NO" id="Fge-Py-Vh1">
                                                <rect key="frame" x="33" y="0.0" width="18" height="18"/>
                                                <constraints>
                                                    <constraint firstAttribute="width" constant="18" id="Ghr-B3-orV"/>
                                                    <constraint firstAttribute="height" constant="18" id="PvN-BX-h6m"/>
                                                </constraints>
                                            </imageView>
                                            <imageView clipsSubviews="YES" userInteractionEnabled="NO" contentMode="scaleAspectFit" horizontalHuggingPriority="251" verticalHuggingPriority="251" image="heart-line" translatesAutoresizingMaskIntoConstraints="NO" id="kWx-K8-GII">
                                                <rect key="frame" x="66" y="0.0" width="18" height="18"/>
                                                <constraints>
                                                    <constraint firstAttribute="width" constant="18" id="eoe-6D-0tu"/>
                                                    <constraint firstAttribute="height" constant="18" id="lHz-Nt-wAe"/>
                                                </constraints>
                                            </imageView>
                                            <imageView clipsSubviews="YES" userInteractionEnabled="NO" contentMode="scaleAspectFit" horizontalHuggingPriority="251" verticalHuggingPriority="251" image="Group 45" translatesAutoresizingMaskIntoConstraints="NO" id="xdH-AB-2e4">
                                                <rect key="frame" x="99" y="0.0" width="18" height="18"/>
                                                <constraints>
                                                    <constraint firstAttribute="width" constant="18" id="MZ7-fK-kcY"/>
                                                    <constraint firstAttribute="height" constant="18" id="yad-Ht-CqE"/>
                                                </constraints>
                                            </imageView>
                                        </subviews>
                                    </stackView>
                                </subviews>
                                <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                <constraints>
                                    <constraint firstItem="nd4-kp-sQ3" firstAttribute="top" secondItem="p1K-F0-wKh" secondAttribute="top" id="3IW-7i-twj"/>
                                    <constraint firstAttribute="bottom" secondItem="nd4-kp-sQ3" secondAttribute="bottom" id="4Vz-PN-qrs"/>
                                    <constraint firstAttribute="height" constant="64" id="72s-Mv-enl"/>
                                    <constraint firstItem="cCu-rT-ywv" firstAttribute="leading" secondItem="gFX-PJ-Wch" secondAttribute="trailing" constant="10" id="9kH-Hu-tlN"/>
                                    <constraint firstItem="zFb-wP-JP7" firstAttribute="centerY" secondItem="p1K-F0-wKh" secondAttribute="centerY" id="DsV-Tl-iRQ"/>
                                    <constraint firstAttribute="bottom" secondItem="gFX-PJ-Wch" secondAttribute="bottom" constant="8" id="Ekr-Lr-PWH"/>
                                    <constraint firstAttribute="trailing" secondItem="cCu-rT-ywv" secondAttribute="trailing" constant="10" id="OrI-0Q-NMS"/>
                                    <constraint firstItem="gFX-PJ-Wch" firstAttribute="leading" secondItem="zFb-wP-JP7" secondAttribute="trailing" constant="20" id="c9X-BD-ftv"/>
                                    <constraint firstItem="cCu-rT-ywv" firstAttribute="centerY" secondItem="gFX-PJ-Wch" secondAttribute="centerY" id="d56-YG-VsH"/>
                                    <constraint firstItem="zFb-wP-JP7" firstAttribute="leading" secondItem="p1K-F0-wKh" secondAttribute="leading" constant="10" id="oRM-ZE-fWp"/>
                                    <constraint firstItem="gFX-PJ-Wch" firstAttribute="top" secondItem="p1K-F0-wKh" secondAttribute="top" constant="8" id="pbn-wn-p7H"/>
                                    <constraint firstItem="nd4-kp-sQ3" firstAttribute="leading" secondItem="p1K-F0-wKh" secondAttribute="leading" id="tFw-SZ-Oah"/>
                                </constraints>
                            </view>
                            <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="UsO-ro-QJq">
                                <rect key="frame" x="0.0" y="118" width="414" height="778"/>
                                <subviews>
                                    <tableView clipsSubviews="YES" contentMode="scaleToFill" alwaysBounceVertical="YES" showsHorizontalScrollIndicator="NO" showsVerticalScrollIndicator="NO" dataMode="prototypes" style="plain" separatorStyle="none" rowHeight="-1" estimatedRowHeight="-1" sectionHeaderHeight="28" sectionFooterHeight="28" translatesAutoresizingMaskIntoConstraints="NO" id="VA7-ff-ucU">
                                        <rect key="frame" x="0.0" y="0.0" width="414" height="778"/>
                                        <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                        <connections>
                                            <outlet property="dataSource" destination="aJS-Ah-pbw" id="owS-ay-uvC"/>
                                            <outlet property="delegate" destination="aJS-Ah-pbw" id="Jiu-Cs-spN"/>
                                        </connections>
                                    </tableView>
                                </subviews>
                                <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                <constraints>
                                    <constraint firstAttribute="bottom" secondItem="VA7-ff-ucU" secondAttribute="bottom" id="Ocg-dF-yMS"/>
                                    <constraint firstItem="VA7-ff-ucU" firstAttribute="leading" secondItem="UsO-ro-QJq" secondAttribute="leading" id="ToF-Sd-QGY"/>
                                    <constraint firstAttribute="trailing" secondItem="VA7-ff-ucU" secondAttribute="trailing" id="gGP-rx-GZ4"/>
                                    <constraint firstItem="VA7-ff-ucU" firstAttribute="top" secondItem="UsO-ro-QJq" secondAttribute="top" id="yzD-Jn-StJ"/>
                                </constraints>
                            </view>
                        </subviews>
                        <viewLayoutGuide key="safeArea" id="eab-uz-FeO"/>
                        <color key="backgroundColor" systemColor="systemBackgroundColor"/>
                        <constraints>
                            <constraint firstItem="eab-uz-FeO" firstAttribute="trailing" secondItem="UsO-ro-QJq" secondAttribute="trailing" id="50J-Pm-5qM"/>
                            <constraint firstItem="p1K-F0-wKh" firstAttribute="trailing" secondItem="eab-uz-FeO" secondAttribute="trailing" id="8aT-Hi-YxC"/>
                            <constraint firstAttribute="bottom" secondItem="UsO-ro-QJq" secondAttribute="bottom" id="IMM-te-D1S"/>
                            <constraint firstItem="UsO-ro-QJq" firstAttribute="top" secondItem="p1K-F0-wKh" secondAttribute="bottom" constant="10" id="Man-6q-5XZ"/>
                            <constraint firstItem="UsO-ro-QJq" firstAttribute="leading" secondItem="eab-uz-FeO" secondAttribute="leading" id="W64-ra-jVk"/>
                            <constraint firstItem="p1K-F0-wKh" firstAttribute="top" secondItem="eab-uz-FeO" secondAttribute="top" id="bTQ-9a-g5z"/>
                            <constraint firstItem="p1K-F0-wKh" firstAttribute="leading" secondItem="eab-uz-FeO" secondAttribute="leading" id="neE-iJ-IS5"/>
                        </constraints>
                    </view>
                    <connections>
                        <outlet property="tableView" destination="VA7-ff-ucU" id="peG-9b-h9D"/>
                    </connections>
                </viewController>
                <placeholder placeholderIdentifier="IBFirstResponder" id="qGd-ki-TMk" userLabel="First Responder" customClass="UIResponder" sceneMemberID="firstResponder"/>
            </objects>
            <point key="canvasLocation" x="-1422" y="10"/>
        </scene>
    </scenes>
    <resources>
        <image name="Group 17652" width="21" height="31"/>
        <image name="Group 45" width="16" height="18"/>
        <image name="arrow" width="170.66667175292969" height="170.66667175292969"/>
        <image name="heart-line" width="21" height="19"/>
        <image name="share" width="78" height="78"/>
        <systemColor name="systemBackgroundColor">
            <color white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
        </systemColor>
    </resources>
</document>
