//
//  TitleTableViewCell.swift
//  shopping
//
//  Created by <PERSON><PERSON> on 13/10/20.
//  Copyright © 2020 macpot. All rights reserved.
//

import UIKit

class TitleTableViewCell: UITableViewCell {

   
    
    @IBOutlet weak var iconImg: UIImageView!
    @IBOutlet weak var bgView: UIView!
    @IBOutlet weak var bottomConstraint: NSLayoutConstraint!
    @IBOutlet weak var tittleLbl: UILabel!
    
    @IBOutlet weak var plusminusLbl: UILabel!
    override func awakeFromNib() {
        super.awakeFromNib()
        // Initialization code
        
        tittleLbl.font = UIFont.with(size:13,.bold)
        plusminusLbl.font = UIFont.with(size:35,.regular)
        selectionStyle = .none
        DispatchQueue.main.async {
            let l1 = UIColor(colorWithHexValue:0xE7E8E8)
            let l2 = UIColor(colorWithHexValue:0xEDEDEB)
            
            self.bgView.gradient(colours: [l1,l2,UIColor.white])
            self.bgView.addShadow()
            //self.bgView.setShadow()
        }
    }
    func select(){
        plusminusLbl.text = "-"
    }
    func deselect(){
        plusminusLbl.text = "+"
        
    }
    override func layoutSubviews() {
       
        
    }
    override func setSelected(_ selected: Bool, animated: Bool) {
        super.setSelected(selected, animated: animated)

        // Configure the view for the selected state
    }
   
}
extension TitleTableViewCell:ReuseIdentifying,NibLoadable {}
