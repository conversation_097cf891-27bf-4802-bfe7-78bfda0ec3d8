//
//  CategoryVC.swift
//  BabyHouse
//
//  Created by <PERSON> on 23/03/22.
//

import UIKit


class CategoryVC: UIViewController {

    var viewModel: CategoryVM?
    var isFrommenu:Bool = false
    var backtoHome:((Bool)-> Void)?
    @IBOutlet weak var headerLbl: UILabel!
    @IBOutlet weak var tableView: UITableView!
    var tableviewData = ["0","1","2"]
    override func viewDidLoad() {
        super.viewDidLoad()
        setFont()
        tableView.register(CategoryTableViewCell.self)
        tableView.register(TitleTableViewCell.self)
        tableView.delegate = self
        tableView.dataSource = self
      //  self.view.backgroundColor = saylightblue
        getData()
        hideNavigationBar()
    }
    
    fileprivate func setFont() {
        headerLbl.font = UIFont.with(size:14, .bold)
       
    }
    @IBAction func back(_ sender: Any) {
        if isFrommenu {
            self.navigationController?.popViewController(animated:true)
            return
        }
        backtoHome?(true)
    }



}


extension CategoryVC {
    fileprivate func getData() {
        showActivity()
       
        self.viewModel?.categoryList(completion: { (response) in
            
           
         })
    }
}
extension CategoryVC:UITableViewDataSource,UITableViewDelegate {
    
    func numberOfSections(in tableView: UITableView) -> Int {
        tableviewData.count
    }
    
    func tableView(_ tableView: UITableView, numberOfRowsInSection section: Int) -> Int {
        
//        if tableviewData[section].open == true {
//            return tableviewData[section].sectionData.count + 1
//        }else {
            return 1
//        }
//
    }
    
    func tableView(_ tableView: UITableView, heightForFooterInSection section: Int) -> CGFloat {
        return 0
    }
    func tableView(_ tableView: UITableView, heightForHeaderInSection section: Int) -> CGFloat {
        return CGFloat.leastNonzeroMagnitude
    }
    func tableView(_ tableView: UITableView, heightForRowAt indexPath: IndexPath) -> CGFloat {
        if indexPath.row == 0 {
            return 140
        }
        return UITableView.automaticDimension
    }
    
    func tableView(_ tableView: UITableView, cellForRowAt indexPath: IndexPath) -> UITableViewCell {
        if indexPath.row == 0 {
            
            let cell:TitleTableViewCell = tableView.dequeueReusableCell(for:indexPath)
//            cell.tittleLbl.text = tableviewData[indexPath.section].title.categoryName
//            let url = tableviewData[indexPath.section].title.icon
//            cell.iconImg.sd_setImage(with:URL(string:url), placeholderImage: UIImage(named: "dbg"))
//            cell.plusminusLbl.isHidden = true
//            if tableviewData[indexPath.section].title.children.count > 0 {
//                cell.plusminusLbl.isHidden = false
//            }
//            if tableviewData[indexPath.section].open ==  true {
//                cell.select()
//                cell.bottomConstraint.constant = 10
//            }
//            else {
//                cell.deselect()
//                cell.bottomConstraint.constant = 10
//            }
            return cell
        }
        else {
            let cell:CategoryTableViewCell = tableView.dequeueReusableCell(for:indexPath)
//            cell.category = tableviewData[indexPath.section].sectionData[indexPath.row-1]
            
            return cell
            
        }
}
//    func tableView(_ tableView: UITableView, didSelectRowAt indexPath: IndexPath) {
//        if indexPath.row == 0 {
//            if tableviewData[indexPath.section].open == true {
//                tableviewData[indexPath.section].open = false
//                let sections =  IndexSet.init(integer:indexPath.section)
//                UIView.performWithoutAnimation {
//                      self.tableView.reloadSections(sections, with:.none)
//                }
//            }
//            else
//            {
//                if tableviewData[indexPath.section].title.children.count > 0 {
//                    tableviewData[indexPath.section].open = true
//                    let sections =  IndexSet.init(integer:indexPath.section)
//                    UIView.performWithoutAnimation {
//                          self.tableView.reloadSections(sections, with:.none)
//                    }
//
//                }
//
//
//            }
//        }
//        else {
//            print(indexPath.row)
//            let  current = tableviewData[indexPath.section].sectionData[indexPath.row-1]
//            print(current)
//            let vc = CategoryproductsVC.instantiate()
//            vc.category = current
//            vc.viewModel = HomeVM(with: 0)
//            self.navigationController?.pushViewController(vc, animated: true)
//        }
//        //updateTableHeight()
//    }
}

extension CategoryVC:Storyboarded {
    static var storyboard: Storyboard { .category }
}
