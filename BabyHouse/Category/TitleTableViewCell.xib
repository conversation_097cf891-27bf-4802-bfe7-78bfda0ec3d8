<?xml version="1.0" encoding="UTF-8"?>
<document type="com.apple.InterfaceBuilder3.CocoaTouch.XIB" version="3.0" toolsVersion="17701" targetRuntime="iOS.CocoaTouch" propertyAccessControl="none" useAutolayout="YES" useTraitCollections="YES" useSafeAreas="YES" colorMatched="YES">
    <device id="retina6_1" orientation="portrait" appearance="light"/>
    <dependencies>
        <deployment identifier="iOS"/>
        <plugIn identifier="com.apple.InterfaceBuilder.IBCocoaTouchPlugin" version="17703"/>
        <capability name="Safe area layout guides" minToolsVersion="9.0"/>
        <capability name="System colors in document resources" minToolsVersion="11.0"/>
        <capability name="documents saved in the Xcode 8 format" minToolsVersion="8.0"/>
    </dependencies>
    <objects>
        <placeholder placeholderIdentifier="IBFilesOwner" id="-1" userLabel="File's Owner"/>
        <placeholder placeholderIdentifier="IBFirstResponder" id="-2" customClass="UIResponder"/>
        <tableViewCell contentMode="scaleToFill" selectionStyle="default" indentationWidth="10" rowHeight="140" id="KGk-i7-Jjw" customClass="TitleTableViewCell" customModule="Saydality" customModuleProvider="target">
            <rect key="frame" x="0.0" y="0.0" width="542" height="138"/>
            <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMaxY="YES"/>
            <tableViewCellContentView key="contentView" opaque="NO" clipsSubviews="YES" multipleTouchEnabled="YES" contentMode="center" tableViewCell="KGk-i7-Jjw" id="H2p-sc-9uM">
                <rect key="frame" x="0.0" y="0.0" width="542" height="138"/>
                <autoresizingMask key="autoresizingMask"/>
                <subviews>
                    <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="G4t-MV-3bm">
                        <rect key="frame" x="0.0" y="0.0" width="542" height="138"/>
                        <subviews>
                            <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="sFy-1a-Rzl">
                                <rect key="frame" x="0.0" y="0.0" width="542" height="128"/>
                                <subviews>
                                    <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="NzH-4g-ncF">
                                        <rect key="frame" x="237" y="0.0" width="325" height="128"/>
                                        <subviews>
                                            <imageView clipsSubviews="YES" userInteractionEnabled="NO" contentMode="scaleToFill" horizontalHuggingPriority="251" verticalHuggingPriority="251" image="dbg" translatesAutoresizingMaskIntoConstraints="NO" id="tkm-Zb-Azy">
                                                <rect key="frame" x="0.0" y="0.0" width="325" height="128"/>
                                                <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                            </imageView>
                                        </subviews>
                                        <color key="backgroundColor" systemColor="systemBackgroundColor"/>
                                        <constraints>
                                            <constraint firstAttribute="trailing" secondItem="tkm-Zb-Azy" secondAttribute="trailing" id="E2i-JW-ZZ9"/>
                                            <constraint firstItem="tkm-Zb-Azy" firstAttribute="top" secondItem="NzH-4g-ncF" secondAttribute="top" id="K4g-an-bcy"/>
                                            <constraint firstAttribute="bottom" secondItem="tkm-Zb-Azy" secondAttribute="bottom" id="lor-c5-ev1"/>
                                            <constraint firstItem="tkm-Zb-Azy" firstAttribute="leading" secondItem="NzH-4g-ncF" secondAttribute="leading" id="mKC-W5-ScS"/>
                                        </constraints>
                                    </view>
                                    <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Kids Care" textAlignment="natural" lineBreakMode="tailTruncation" numberOfLines="3" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="6y0-Rs-urP">
                                        <rect key="frame" x="48" y="10" width="73.5" height="108"/>
                                        <fontDescription key="fontDescription" type="system" pointSize="17"/>
                                        <color key="textColor" red="0.0" green="0.0" blue="0.50196078431372548" alpha="1" colorSpace="calibratedRGB"/>
                                        <nil key="highlightedColor"/>
                                    </label>
                                    <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="+" textAlignment="center" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="XA7-Uh-hlU">
                                        <rect key="frame" x="10" y="55" width="18" height="18"/>
                                        <constraints>
                                            <constraint firstAttribute="height" constant="18" id="2cL-Ff-y7d"/>
                                            <constraint firstAttribute="width" constant="18" id="Li5-gm-IWo"/>
                                        </constraints>
                                        <fontDescription key="fontDescription" type="system" pointSize="17"/>
                                        <color key="textColor" red="0.68235294117647061" green="0.63921568627450975" blue="0.5607843137254902" alpha="1" colorSpace="calibratedRGB"/>
                                        <nil key="highlightedColor"/>
                                    </label>
                                </subviews>
                                <color key="backgroundColor" systemColor="systemBackgroundColor"/>
                                <constraints>
                                    <constraint firstAttribute="bottom" secondItem="NzH-4g-ncF" secondAttribute="bottom" id="51L-Yb-7lR"/>
                                    <constraint firstItem="6y0-Rs-urP" firstAttribute="leading" secondItem="XA7-Uh-hlU" secondAttribute="trailing" constant="20" id="AWa-8Z-qTq"/>
                                    <constraint firstAttribute="trailing" secondItem="NzH-4g-ncF" secondAttribute="trailing" constant="-20" id="Gax-q0-GKk"/>
                                    <constraint firstItem="NzH-4g-ncF" firstAttribute="leading" relation="greaterThanOrEqual" secondItem="6y0-Rs-urP" secondAttribute="trailing" constant="10" id="Rlo-jO-728"/>
                                    <constraint firstItem="XA7-Uh-hlU" firstAttribute="centerY" secondItem="6y0-Rs-urP" secondAttribute="centerY" id="agl-LR-0wD"/>
                                    <constraint firstItem="NzH-4g-ncF" firstAttribute="top" secondItem="sFy-1a-Rzl" secondAttribute="top" id="eec-Rq-fhN"/>
                                    <constraint firstAttribute="bottom" secondItem="6y0-Rs-urP" secondAttribute="bottom" constant="10" id="fx6-Fd-6ud"/>
                                    <constraint firstItem="6y0-Rs-urP" firstAttribute="top" secondItem="sFy-1a-Rzl" secondAttribute="top" constant="10" id="iD2-LG-Y0d"/>
                                    <constraint firstItem="NzH-4g-ncF" firstAttribute="width" secondItem="sFy-1a-Rzl" secondAttribute="width" multiplier="0.6" id="yI6-NM-S90"/>
                                    <constraint firstItem="XA7-Uh-hlU" firstAttribute="leading" secondItem="sFy-1a-Rzl" secondAttribute="leading" constant="10" id="yqm-Xc-tDd"/>
                                </constraints>
                            </view>
                        </subviews>
                        <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                        <constraints>
                            <constraint firstAttribute="trailing" secondItem="sFy-1a-Rzl" secondAttribute="trailing" id="3od-C6-I7m"/>
                            <constraint firstItem="sFy-1a-Rzl" firstAttribute="leading" secondItem="G4t-MV-3bm" secondAttribute="leading" id="Ig3-vh-fo5"/>
                            <constraint firstAttribute="bottom" secondItem="sFy-1a-Rzl" secondAttribute="bottom" constant="10" id="qSH-YB-A4h"/>
                            <constraint firstItem="sFy-1a-Rzl" firstAttribute="top" secondItem="G4t-MV-3bm" secondAttribute="top" id="vS1-QQ-Q1d"/>
                        </constraints>
                    </view>
                </subviews>
                <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                <constraints>
                    <constraint firstAttribute="bottom" secondItem="G4t-MV-3bm" secondAttribute="bottom" id="OcC-3e-PnB"/>
                    <constraint firstAttribute="trailing" secondItem="G4t-MV-3bm" secondAttribute="trailing" id="ShJ-B3-eVm"/>
                    <constraint firstItem="G4t-MV-3bm" firstAttribute="top" secondItem="H2p-sc-9uM" secondAttribute="top" id="j45-WP-h4L"/>
                    <constraint firstItem="G4t-MV-3bm" firstAttribute="leading" secondItem="H2p-sc-9uM" secondAttribute="leading" id="u2o-VH-qZq"/>
                </constraints>
            </tableViewCellContentView>
            <viewLayoutGuide key="safeArea" id="njF-e1-oar"/>
            <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
            <connections>
                <outlet property="bgView" destination="sFy-1a-Rzl" id="Ycm-ys-D6B"/>
                <outlet property="bottomConstraint" destination="qSH-YB-A4h" id="rCY-OX-whu"/>
                <outlet property="iconImg" destination="tkm-Zb-Azy" id="3To-6s-CyL"/>
                <outlet property="plusminusLbl" destination="XA7-Uh-hlU" id="0iO-t5-Wdc"/>
                <outlet property="tittleLbl" destination="6y0-Rs-urP" id="5r2-oo-bRM"/>
            </connections>
            <point key="canvasLocation" x="117" y="76"/>
        </tableViewCell>
    </objects>
    <resources>
        <image name="dbg" width="512" height="512"/>
        <systemColor name="systemBackgroundColor">
            <color white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
        </systemColor>
    </resources>
</document>
