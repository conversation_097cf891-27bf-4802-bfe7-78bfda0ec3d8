//
//  AddressRequest.swift
//  BabyHouse
//
//  Created by <PERSON> on 19/06/22.
//

import Foundation

enum AddressRequest {
    case addressList(String)
   case orderDetails(OrderDetailsData,String)
  //  case removeCart(removeCartData, String)
   
}
extension AddressRequest: ZEndpoint {
var path: String {
    switch self {
    case .addressList: return "address-list"
    case .orderDetails: return "order-details"
  //  case .removeCart: return "remove-cart"
   
    }
}


var httpMethod: HTTPMethod {
    switch self {
    case .addressList: return .get
    case .orderDetails: return .post
   
    }
}
var httpBody: HTTPBody? {
    switch self {
    case .addressList: return nil
//    case .removeCart(let params, _):
//        let dict = params.dictionary?.formattedString
//        return dict?.data
    case .orderDetails(let params, _):
        let dict = params.dictionary?.formattedString
        return dict?.data
   
     
    }
}
var httpHeaders: LightHTTPHeaders {
    
    switch self {
   
    case .addressList(let token): return ["Authorization":token]
    case .orderDetails(_, let token): return ["Authorization":token]
  //  case .removeCart(_, let token): return ["Authorization":token]
   
        
    }
    
}



}



