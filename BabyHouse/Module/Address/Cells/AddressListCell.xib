<?xml version="1.0" encoding="UTF-8"?>
<document type="com.apple.InterfaceBuilder3.CocoaTouch.XIB" version="3.0" toolsVersion="20037" targetRuntime="iOS.CocoaTouch" propertyAccessControl="none" useAutolayout="YES" useTraitCollections="YES" useSafeAreas="YES" colorMatched="YES">
    <device id="retina6_1" orientation="portrait" appearance="light"/>
    <dependencies>
        <deployment identifier="iOS"/>
        <plugIn identifier="com.apple.InterfaceBuilder.IBCocoaTouchPlugin" version="20020"/>
        <capability name="Safe area layout guides" minToolsVersion="9.0"/>
        <capability name="documents saved in the Xcode 8 format" minToolsVersion="8.0"/>
    </dependencies>
    <objects>
        <placeholder placeholderIdentifier="IBFilesOwner" id="-1" userLabel="File's Owner"/>
        <placeholder placeholderIdentifier="IBFirstResponder" id="-2" customClass="UIResponder"/>
        <tableViewCell contentMode="scaleToFill" restorationIdentifier="AddressListCell" selectionStyle="default" indentationWidth="10" rowHeight="82" id="KGk-i7-Jjw" customClass="AddressListCell" customModule="BabyHouse" customModuleProvider="target">
            <rect key="frame" x="0.0" y="0.0" width="412" height="106"/>
            <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMaxY="YES"/>
            <tableViewCellContentView key="contentView" opaque="NO" clipsSubviews="YES" multipleTouchEnabled="YES" contentMode="center" tableViewCell="KGk-i7-Jjw" id="H2p-sc-9uM">
                <rect key="frame" x="0.0" y="0.0" width="412" height="106"/>
                <autoresizingMask key="autoresizingMask"/>
                <subviews>
                    <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="Hby-ny-ddg">
                        <rect key="frame" x="10" y="10" width="392" height="86"/>
                        <subviews>
                            <stackView opaque="NO" contentMode="scaleToFill" axis="vertical" alignment="top" spacing="8" translatesAutoresizingMaskIntoConstraints="NO" id="i2f-cc-xtC">
                                <rect key="frame" x="50" y="20" width="292" height="46"/>
                                <subviews>
                                    <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" verticalCompressionResistancePriority="749" text="WORK" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="of5-Lt-NcZ">
                                        <rect key="frame" x="0.0" y="0.0" width="50" height="18.5"/>
                                        <fontDescription key="fontDescription" type="system" pointSize="17"/>
                                        <nil key="textColor"/>
                                        <nil key="highlightedColor"/>
                                    </label>
                                    <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="San Francisco new js" textAlignment="natural" lineBreakMode="tailTruncation" numberOfLines="0" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="8hE-Oh-ECp">
                                        <rect key="frame" x="0.0" y="26.5" width="152" height="19.5"/>
                                        <fontDescription key="fontDescription" type="system" pointSize="16"/>
                                        <color key="textColor" white="0.33333333333333331" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                        <nil key="highlightedColor"/>
                                    </label>
                                </subviews>
                            </stackView>
                            <imageView clipsSubviews="YES" userInteractionEnabled="NO" contentMode="scaleAspectFit" horizontalHuggingPriority="251" verticalHuggingPriority="251" image="browser" translatesAutoresizingMaskIntoConstraints="NO" id="kXC-ts-Oj0">
                                <rect key="frame" x="15" y="33" width="20" height="20"/>
                                <constraints>
                                    <constraint firstAttribute="width" constant="20" id="G87-YI-27X"/>
                                    <constraint firstAttribute="height" constant="20" id="gyv-VE-LWf"/>
                                </constraints>
                            </imageView>
                            <imageView clipsSubviews="YES" userInteractionEnabled="NO" contentMode="scaleAspectFit" horizontalHuggingPriority="251" verticalHuggingPriority="251" image="more" translatesAutoresizingMaskIntoConstraints="NO" id="cc6-h8-gWA">
                                <rect key="frame" x="357" y="33" width="20" height="20"/>
                                <constraints>
                                    <constraint firstAttribute="height" constant="20" id="0Yv-eS-zM1"/>
                                    <constraint firstAttribute="width" constant="20" id="LBD-wc-efH"/>
                                </constraints>
                            </imageView>
                            <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" buttonType="system" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="ud4-gv-ra0">
                                <rect key="frame" x="330" y="0.0" width="62" height="86"/>
                                <connections>
                                    <action selector="sidemenuClick:" destination="KGk-i7-Jjw" eventType="touchUpInside" id="etr-NG-Xca"/>
                                </connections>
                            </button>
                        </subviews>
                        <color key="backgroundColor" red="0.98852866890000002" green="0.91909533740000005" blue="0.93207210299999999" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                        <constraints>
                            <constraint firstAttribute="trailing" secondItem="cc6-h8-gWA" secondAttribute="trailing" constant="15" id="0Hc-Qn-2nh"/>
                            <constraint firstItem="cc6-h8-gWA" firstAttribute="centerY" secondItem="kXC-ts-Oj0" secondAttribute="centerY" id="19y-bF-YAP"/>
                            <constraint firstItem="cc6-h8-gWA" firstAttribute="leading" secondItem="i2f-cc-xtC" secondAttribute="trailing" constant="15" id="Iep-SU-9hK"/>
                            <constraint firstAttribute="trailing" secondItem="ud4-gv-ra0" secondAttribute="trailing" id="ImW-xI-ZrH"/>
                            <constraint firstAttribute="bottom" secondItem="i2f-cc-xtC" secondAttribute="bottom" constant="20" id="JHR-Ew-CYC"/>
                            <constraint firstAttribute="bottom" secondItem="ud4-gv-ra0" secondAttribute="bottom" id="JOx-Ek-cyD"/>
                            <constraint firstItem="i2f-cc-xtC" firstAttribute="leading" secondItem="kXC-ts-Oj0" secondAttribute="trailing" constant="15" id="Khb-ae-y1f"/>
                            <constraint firstItem="kXC-ts-Oj0" firstAttribute="centerY" secondItem="i2f-cc-xtC" secondAttribute="centerY" id="Ovq-36-gCE"/>
                            <constraint firstItem="kXC-ts-Oj0" firstAttribute="leading" secondItem="Hby-ny-ddg" secondAttribute="leading" constant="15" id="n9l-e5-cYq"/>
                            <constraint firstItem="i2f-cc-xtC" firstAttribute="top" secondItem="Hby-ny-ddg" secondAttribute="top" constant="20" id="ndZ-8Z-oyR"/>
                            <constraint firstItem="ud4-gv-ra0" firstAttribute="top" secondItem="Hby-ny-ddg" secondAttribute="top" id="w73-Dt-dUl"/>
                        </constraints>
                        <userDefinedRuntimeAttributes>
                            <userDefinedRuntimeAttribute type="number" keyPath="layer.cornerRadius">
                                <integer key="value" value="5"/>
                            </userDefinedRuntimeAttribute>
                        </userDefinedRuntimeAttributes>
                    </view>
                </subviews>
                <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                <constraints>
                    <constraint firstAttribute="bottom" secondItem="Hby-ny-ddg" secondAttribute="bottom" constant="10" id="Avz-Pt-mVa"/>
                    <constraint firstItem="ud4-gv-ra0" firstAttribute="width" secondItem="H2p-sc-9uM" secondAttribute="width" multiplier="0.15" id="SlK-wv-DRZ"/>
                    <constraint firstAttribute="trailing" secondItem="Hby-ny-ddg" secondAttribute="trailing" constant="10" id="iMv-uo-aSt"/>
                    <constraint firstItem="Hby-ny-ddg" firstAttribute="top" secondItem="H2p-sc-9uM" secondAttribute="top" constant="10" id="iT5-b4-LwU"/>
                    <constraint firstItem="Hby-ny-ddg" firstAttribute="leading" secondItem="H2p-sc-9uM" secondAttribute="leading" constant="10" id="nwg-6J-DBF"/>
                </constraints>
            </tableViewCellContentView>
            <viewLayoutGuide key="safeArea" id="njF-e1-oar"/>
            <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
            <connections>
                <outlet property="imgView" destination="kXC-ts-Oj0" id="qB2-nL-cdf"/>
                <outlet property="mainTitle" destination="of5-Lt-NcZ" id="F8A-Ss-aNp"/>
                <outlet property="mainView" destination="Hby-ny-ddg" id="Zuv-78-6Qt"/>
                <outlet property="sidemenuOult" destination="ud4-gv-ra0" id="Awc-ZF-4fX"/>
                <outlet property="subTitle" destination="8hE-Oh-ECp" id="AwW-gZ-geK"/>
            </connections>
            <point key="canvasLocation" x="-14.492753623188406" y="105.80357142857143"/>
        </tableViewCell>
    </objects>
    <resources>
        <image name="browser" width="18" height="17"/>
        <image name="more" width="3.5" height="14.5"/>
    </resources>
</document>
