//
//  AddressListCell.swift
//  Saydality
//
//  Created by <PERSON> on 09/12/21.
//

import UIKit

class AddressListCell: UITableViewCell {
    var clickedSidemeu:((Int)->Void)?
    @IBOutlet weak var sidemenuOult: UIButton!
    @IBOutlet weak var imgView: UIImageView!
    @IBOutlet weak var mainView: UIView!
    @IBOutlet weak var subTitle: UILabel!
    @IBOutlet weak var mainTitle: UILabel!
    var address:AddressData? {
        didSet {
            let cityname = address?.area_name ?? ""
            let name = address?.street ?? ""
            subTitle.text = cityname + "," + name
            mainTitle.text = address?.type
            switch address?.type {
                
            case "Home":imgView.image = UIImage(named:"browser")
                
            case "Apartment":imgView.image = UIImage(named:"apartment")
             
            case "Work":imgView.image = UIImage(named:"office")
                
            case .none:
                imgView.image = UIImage(named:"browser")
            case .some(_):
                imgView.image = UIImage(named:"browser")
            }
        }
    }
    override func awakeFromNib() {
        super.awakeFromNib()
        // Initialization code
        mainTitle.font = UIFont.with(size:14, .bold)
        subTitle.font = UIFont.with(size:12, .regular)
        mainView.cornerRadius = 20
        mainView.setShadow(color:Babyred.withAlphaComponent(0.3))
        selectionStyle = .none
    }

    override func setSelected(_ selected: Bool, animated: Bool) {
        super.setSelected(selected, animated: animated)

       
    }
    @IBAction func sidemenuClick(_ sender: UIButton) {
        let tag = sender.tag
        clickedSidemeu?(tag)
    }
}
extension  AddressListCell: NibLoadable, ReuseIdentifying {}
