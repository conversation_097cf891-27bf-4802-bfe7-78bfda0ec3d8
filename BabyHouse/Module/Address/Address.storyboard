<?xml version="1.0" encoding="UTF-8"?>
<document type="com.apple.InterfaceBuilder3.CocoaTouch.Storyboard.XIB" version="3.0" toolsVersion="20037" targetRuntime="iOS.CocoaTouch" propertyAccessControl="none" useAutolayout="YES" useTraitCollections="YES" useSafeAreas="YES" colorMatched="YES">
    <device id="retina6_1" orientation="portrait" appearance="light"/>
    <dependencies>
        <deployment identifier="iOS"/>
        <plugIn identifier="com.apple.InterfaceBuilder.IBCocoaTouchPlugin" version="20020"/>
        <capability name="Safe area layout guides" minToolsVersion="9.0"/>
        <capability name="System colors in document resources" minToolsVersion="11.0"/>
        <capability name="documents saved in the Xcode 8 format" minToolsVersion="8.0"/>
    </dependencies>
    <scenes>
        <!--AddressVC-->
        <scene sceneID="hrZ-2n-NBc">
            <objects>
                <viewController storyboardIdentifier="AddressVC" id="bbr-Vm-Nch" customClass="AddressVC" customModule="BabyHouse" customModuleProvider="target" sceneMemberID="viewController">
                    <view key="view" contentMode="scaleToFill" id="EBn-gl-Mwi">
                        <rect key="frame" x="0.0" y="0.0" width="414" height="896"/>
                        <autoresizingMask key="autoresizingMask" widthSizable="YES" heightSizable="YES"/>
                        <subviews>
                            <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="eiZ-Oh-415">
                                <rect key="frame" x="0.0" y="-30" width="414" height="138"/>
                                <color key="backgroundColor" red="0.71372549019999998" green="0.21176470589999999" blue="0.30588235289999999" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                            </view>
                            <view clipsSubviews="YES" contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="aE7-et-hwo">
                                <rect key="frame" x="0.0" y="44" width="414" height="64"/>
                                <subviews>
                                    <imageView clipsSubviews="YES" userInteractionEnabled="NO" contentMode="scaleAspectFit" horizontalHuggingPriority="251" verticalHuggingPriority="251" image="leftarrowB" translatesAutoresizingMaskIntoConstraints="NO" id="XXv-yN-obI">
                                        <rect key="frame" x="12" y="23" width="18" height="18"/>
                                        <constraints>
                                            <constraint firstAttribute="width" constant="18" id="RgS-O1-zcn"/>
                                        </constraints>
                                    </imageView>
                                    <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Saved Addresses" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="O0S-jl-s7D">
                                        <rect key="frame" x="60" y="8" width="131.5" height="48"/>
                                        <fontDescription key="fontDescription" type="system" pointSize="17"/>
                                        <color key="textColor" red="1" green="1" blue="1" alpha="1" colorSpace="calibratedRGB"/>
                                        <nil key="highlightedColor"/>
                                    </label>
                                    <stackView opaque="NO" contentMode="scaleToFill" distribution="fillEqually" spacing="10" translatesAutoresizingMaskIntoConstraints="NO" id="4kr-dz-I7r">
                                        <rect key="frame" x="338" y="19" width="61" height="26"/>
                                        <subviews>
                                            <imageView clipsSubviews="YES" userInteractionEnabled="NO" contentMode="scaleAspectFit" horizontalHuggingPriority="251" verticalHuggingPriority="251" image="cart" translatesAutoresizingMaskIntoConstraints="NO" id="8K0-C8-R4q">
                                                <rect key="frame" x="0.0" y="0.0" width="25.5" height="26"/>
                                                <constraints>
                                                    <constraint firstAttribute="width" secondItem="8K0-C8-R4q" secondAttribute="height" multiplier="1:1" id="pbN-cC-FKv"/>
                                                </constraints>
                                            </imageView>
                                            <imageView clipsSubviews="YES" userInteractionEnabled="NO" contentMode="scaleAspectFit" horizontalHuggingPriority="251" verticalHuggingPriority="251" image="notification-status" translatesAutoresizingMaskIntoConstraints="NO" id="P8F-sa-Cnj">
                                                <rect key="frame" x="35.5" y="0.0" width="25.5" height="26"/>
                                            </imageView>
                                        </subviews>
                                    </stackView>
                                    <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="2fd-Ne-SSM">
                                        <rect key="frame" x="353.5" y="14" width="15" height="15"/>
                                        <subviews>
                                            <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="0" textAlignment="center" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" minimumScaleFactor="0.69999998807907104" translatesAutoresizingMaskIntoConstraints="NO" id="v14-Pa-av6">
                                                <rect key="frame" x="2" y="2" width="11" height="11"/>
                                                <fontDescription key="fontDescription" type="system" pointSize="9"/>
                                                <color key="textColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                                <nil key="highlightedColor"/>
                                            </label>
                                        </subviews>
                                        <color key="backgroundColor" red="0.86274509799999999" green="0.035294117649999998" blue="0.19215686269999999" alpha="1" colorSpace="calibratedRGB"/>
                                        <constraints>
                                            <constraint firstItem="v14-Pa-av6" firstAttribute="top" secondItem="2fd-Ne-SSM" secondAttribute="top" constant="2" id="7WE-hg-CS9"/>
                                            <constraint firstAttribute="width" constant="15" id="DRy-kO-xfE"/>
                                            <constraint firstAttribute="bottom" secondItem="v14-Pa-av6" secondAttribute="bottom" constant="2" id="Hnd-mi-nF8"/>
                                            <constraint firstAttribute="height" constant="15" id="Zop-g0-tSL"/>
                                            <constraint firstItem="v14-Pa-av6" firstAttribute="leading" secondItem="2fd-Ne-SSM" secondAttribute="leading" constant="2" id="jPg-xy-Vbh"/>
                                            <constraint firstAttribute="trailing" secondItem="v14-Pa-av6" secondAttribute="trailing" constant="2" id="vox-CX-WYj"/>
                                        </constraints>
                                    </view>
                                    <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="FEp-Kc-Va5">
                                        <rect key="frame" x="389" y="14" width="15" height="15"/>
                                        <subviews>
                                            <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="0" textAlignment="center" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" minimumScaleFactor="0.69999998807907104" translatesAutoresizingMaskIntoConstraints="NO" id="HsU-if-MJ6">
                                                <rect key="frame" x="2" y="2" width="6" height="11"/>
                                                <fontDescription key="fontDescription" type="system" pointSize="9"/>
                                                <color key="textColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                                <nil key="highlightedColor"/>
                                            </label>
                                        </subviews>
                                        <color key="backgroundColor" red="0.86274509799999999" green="0.035294117649999998" blue="0.19215686269999999" alpha="1" colorSpace="calibratedRGB"/>
                                        <constraints>
                                            <constraint firstAttribute="bottom" secondItem="HsU-if-MJ6" secondAttribute="bottom" constant="2" id="W4O-Ys-I3f"/>
                                            <constraint firstItem="HsU-if-MJ6" firstAttribute="leading" secondItem="FEp-Kc-Va5" secondAttribute="leading" constant="2" id="X0r-8U-cnO"/>
                                            <constraint firstAttribute="width" constant="15" id="aCa-Wh-NKg"/>
                                            <constraint firstAttribute="height" constant="15" id="rNG-5M-l95"/>
                                        </constraints>
                                    </view>
                                    <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" buttonType="system" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="ZYG-Go-hg2">
                                        <rect key="frame" x="0.0" y="0.0" width="60" height="64"/>
                                        <constraints>
                                            <constraint firstAttribute="width" constant="60" id="Jvr-OJ-0t6"/>
                                        </constraints>
                                        <connections>
                                            <action selector="back:" destination="bbr-Vm-Nch" eventType="touchUpInside" id="Vib-9Y-8a1"/>
                                        </connections>
                                    </button>
                                    <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" buttonType="system" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="aUS-wS-jEq">
                                        <rect key="frame" x="338" y="19" width="25.5" height="26"/>
                                        <state key="normal" title="Button"/>
                                        <buttonConfiguration key="configuration" style="plain" title=" "/>
                                        <connections>
                                            <action selector="showCart:" destination="bbr-Vm-Nch" eventType="touchUpInside" id="i88-x6-3bT"/>
                                        </connections>
                                    </button>
                                    <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" buttonType="system" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="E4I-MY-5dE">
                                        <rect key="frame" x="373.5" y="19" width="25.5" height="26"/>
                                        <state key="normal" title="Button"/>
                                        <buttonConfiguration key="configuration" style="plain" title=" "/>
                                        <connections>
                                            <action selector="showNotification:" destination="bbr-Vm-Nch" eventType="touchUpInside" id="Myi-kJ-QIL"/>
                                        </connections>
                                    </button>
                                </subviews>
                                <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                <constraints>
                                    <constraint firstItem="ZYG-Go-hg2" firstAttribute="leading" secondItem="aE7-et-hwo" secondAttribute="leading" id="3Or-gt-2xB"/>
                                    <constraint firstAttribute="bottom" secondItem="ZYG-Go-hg2" secondAttribute="bottom" id="4cs-te-abz"/>
                                    <constraint firstItem="aUS-wS-jEq" firstAttribute="trailing" secondItem="8K0-C8-R4q" secondAttribute="trailing" id="55F-d0-dPV"/>
                                    <constraint firstAttribute="bottom" secondItem="O0S-jl-s7D" secondAttribute="bottom" constant="8" id="5Ch-v5-rbH"/>
                                    <constraint firstItem="E4I-MY-5dE" firstAttribute="top" secondItem="P8F-sa-Cnj" secondAttribute="top" id="7t1-62-LO8"/>
                                    <constraint firstItem="FEp-Kc-Va5" firstAttribute="trailing" secondItem="P8F-sa-Cnj" secondAttribute="trailing" constant="5" id="AB7-dP-SSk"/>
                                    <constraint firstItem="aUS-wS-jEq" firstAttribute="top" secondItem="8K0-C8-R4q" secondAttribute="top" id="As9-P5-So1"/>
                                    <constraint firstItem="2fd-Ne-SSM" firstAttribute="top" secondItem="8K0-C8-R4q" secondAttribute="top" constant="-5" id="BYg-9c-bwe"/>
                                    <constraint firstAttribute="trailing" secondItem="4kr-dz-I7r" secondAttribute="trailing" constant="15" id="BlQ-3c-zp7"/>
                                    <constraint firstItem="E4I-MY-5dE" firstAttribute="bottom" secondItem="P8F-sa-Cnj" secondAttribute="bottom" id="Dae-mg-G4C"/>
                                    <constraint firstItem="XXv-yN-obI" firstAttribute="centerY" secondItem="ZYG-Go-hg2" secondAttribute="centerY" id="FT7-k5-5Cz"/>
                                    <constraint firstItem="XXv-yN-obI" firstAttribute="top" secondItem="aE7-et-hwo" secondAttribute="top" constant="23" id="HpS-tl-dIy"/>
                                    <constraint firstItem="O0S-jl-s7D" firstAttribute="leading" secondItem="ZYG-Go-hg2" secondAttribute="trailing" id="IqO-IR-7VW"/>
                                    <constraint firstItem="O0S-jl-s7D" firstAttribute="top" secondItem="aE7-et-hwo" secondAttribute="top" constant="8" id="J1K-Y5-CO1"/>
                                    <constraint firstAttribute="height" constant="64" id="KJD-dd-zeT"/>
                                    <constraint firstItem="8K0-C8-R4q" firstAttribute="height" secondItem="aE7-et-hwo" secondAttribute="height" multiplier="0.4" id="VPn-8W-bv5"/>
                                    <constraint firstItem="E4I-MY-5dE" firstAttribute="leading" secondItem="P8F-sa-Cnj" secondAttribute="leading" id="YEg-fG-Z0f"/>
                                    <constraint firstItem="O0S-jl-s7D" firstAttribute="leading" secondItem="XXv-yN-obI" secondAttribute="trailing" constant="30" id="g1d-QJ-bJ3"/>
                                    <constraint firstItem="4kr-dz-I7r" firstAttribute="centerY" secondItem="O0S-jl-s7D" secondAttribute="centerY" id="no3-TN-fn4"/>
                                    <constraint firstItem="ZYG-Go-hg2" firstAttribute="top" secondItem="aE7-et-hwo" secondAttribute="top" id="r6g-uB-19I"/>
                                    <constraint firstItem="2fd-Ne-SSM" firstAttribute="trailing" secondItem="8K0-C8-R4q" secondAttribute="trailing" constant="5" id="smB-44-iGf"/>
                                    <constraint firstItem="FEp-Kc-Va5" firstAttribute="top" secondItem="P8F-sa-Cnj" secondAttribute="top" constant="-5" id="x91-4n-hPs"/>
                                    <constraint firstItem="E4I-MY-5dE" firstAttribute="trailing" secondItem="P8F-sa-Cnj" secondAttribute="trailing" id="xln-nv-m0q"/>
                                    <constraint firstItem="aUS-wS-jEq" firstAttribute="bottom" secondItem="8K0-C8-R4q" secondAttribute="bottom" id="xq7-3e-dr1"/>
                                    <constraint firstItem="aUS-wS-jEq" firstAttribute="leading" secondItem="8K0-C8-R4q" secondAttribute="leading" id="yNB-Bi-6sq"/>
                                </constraints>
                            </view>
                            <tableView clipsSubviews="YES" contentMode="scaleToFill" alwaysBounceVertical="YES" dataMode="prototypes" style="plain" separatorStyle="default" rowHeight="-1" estimatedRowHeight="-1" sectionHeaderHeight="28" sectionFooterHeight="28" translatesAutoresizingMaskIntoConstraints="NO" id="UAS-pC-zeN">
                                <rect key="frame" x="0.0" y="108" width="414" height="788"/>
                                <color key="backgroundColor" systemColor="systemBackgroundColor"/>
                            </tableView>
                            <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="dVn-9a-QIy">
                                <rect key="frame" x="62" y="787" width="290" height="60"/>
                                <subviews>
                                    <view clipsSubviews="YES" contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="nbI-g7-iP6">
                                        <rect key="frame" x="0.0" y="0.0" width="290" height="60"/>
                                        <subviews>
                                            <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Add New Address" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="l8c-ot-UbH">
                                                <rect key="frame" x="77" y="20" width="136" height="20.5"/>
                                                <fontDescription key="fontDescription" type="system" pointSize="17"/>
                                                <color key="textColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                                <nil key="highlightedColor"/>
                                            </label>
                                            <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" buttonType="system" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="Yqf-ez-iX2">
                                                <rect key="frame" x="0.0" y="0.0" width="290" height="60"/>
                                                <connections>
                                                    <action selector="addressAdd:" destination="bbr-Vm-Nch" eventType="touchUpInside" id="G63-Tw-eMt"/>
                                                </connections>
                                            </button>
                                        </subviews>
                                        <color key="backgroundColor" red="0.76862745099999996" green="0.57647058819999997" blue="0.3921568627" alpha="1" colorSpace="calibratedRGB"/>
                                        <constraints>
                                            <constraint firstItem="Yqf-ez-iX2" firstAttribute="leading" secondItem="nbI-g7-iP6" secondAttribute="leading" id="7oE-1n-moR"/>
                                            <constraint firstAttribute="trailing" secondItem="Yqf-ez-iX2" secondAttribute="trailing" id="DoB-mM-1e0"/>
                                            <constraint firstItem="l8c-ot-UbH" firstAttribute="centerX" secondItem="nbI-g7-iP6" secondAttribute="centerX" id="EN4-tz-jeu"/>
                                            <constraint firstItem="l8c-ot-UbH" firstAttribute="centerY" secondItem="nbI-g7-iP6" secondAttribute="centerY" id="Jee-d1-u3g"/>
                                            <constraint firstAttribute="height" constant="60" id="ZIq-7W-OwO"/>
                                            <constraint firstItem="Yqf-ez-iX2" firstAttribute="top" secondItem="nbI-g7-iP6" secondAttribute="top" id="ZPG-gg-bNf"/>
                                            <constraint firstAttribute="bottom" secondItem="Yqf-ez-iX2" secondAttribute="bottom" id="nXd-LD-Q1d"/>
                                        </constraints>
                                    </view>
                                </subviews>
                                <color key="backgroundColor" systemColor="systemBackgroundColor"/>
                                <constraints>
                                    <constraint firstItem="nbI-g7-iP6" firstAttribute="leading" secondItem="dVn-9a-QIy" secondAttribute="leading" id="A6r-Mo-6qB"/>
                                    <constraint firstItem="nbI-g7-iP6" firstAttribute="centerY" secondItem="dVn-9a-QIy" secondAttribute="centerY" id="Tqe-TL-9CA"/>
                                    <constraint firstAttribute="height" constant="60" id="cnc-X8-LiO"/>
                                    <constraint firstItem="nbI-g7-iP6" firstAttribute="top" secondItem="dVn-9a-QIy" secondAttribute="top" id="hRB-5M-Thu"/>
                                    <constraint firstItem="nbI-g7-iP6" firstAttribute="centerX" secondItem="dVn-9a-QIy" secondAttribute="centerX" id="hcz-7q-Mhn"/>
                                    <constraint firstAttribute="trailing" secondItem="nbI-g7-iP6" secondAttribute="trailing" id="iRn-mf-oZn"/>
                                    <constraint firstAttribute="bottom" secondItem="nbI-g7-iP6" secondAttribute="bottom" id="w01-0r-Jaz"/>
                                </constraints>
                            </view>
                        </subviews>
                        <viewLayoutGuide key="safeArea" id="6Ej-Lj-4Lm"/>
                        <color key="backgroundColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                        <constraints>
                            <constraint firstItem="dVn-9a-QIy" firstAttribute="centerX" secondItem="6Ej-Lj-4Lm" secondAttribute="centerX" id="0oH-nU-ksh"/>
                            <constraint firstItem="UAS-pC-zeN" firstAttribute="top" secondItem="eiZ-Oh-415" secondAttribute="bottom" id="2IW-nd-PuE"/>
                            <constraint firstItem="dVn-9a-QIy" firstAttribute="width" secondItem="EBn-gl-Mwi" secondAttribute="width" multiplier="0.7" id="2rJ-ez-14w"/>
                            <constraint firstItem="aE7-et-hwo" firstAttribute="trailing" secondItem="6Ej-Lj-4Lm" secondAttribute="trailing" id="RLS-ce-BHJ"/>
                            <constraint firstItem="6Ej-Lj-4Lm" firstAttribute="trailing" secondItem="UAS-pC-zeN" secondAttribute="trailing" id="eMP-BL-zlq"/>
                            <constraint firstItem="6Ej-Lj-4Lm" firstAttribute="bottom" secondItem="dVn-9a-QIy" secondAttribute="bottom" constant="15" id="eds-8i-uz2"/>
                            <constraint firstItem="UAS-pC-zeN" firstAttribute="leading" secondItem="6Ej-Lj-4Lm" secondAttribute="leading" id="fP4-yi-Laj"/>
                            <constraint firstAttribute="bottom" secondItem="UAS-pC-zeN" secondAttribute="bottom" id="gi5-1S-PwV"/>
                            <constraint firstItem="aE7-et-hwo" firstAttribute="leading" secondItem="6Ej-Lj-4Lm" secondAttribute="leading" id="kzi-4f-lhc"/>
                            <constraint firstItem="eiZ-Oh-415" firstAttribute="top" secondItem="EBn-gl-Mwi" secondAttribute="top" constant="-30" id="lDY-Po-eOv"/>
                            <constraint firstItem="aE7-et-hwo" firstAttribute="top" secondItem="6Ej-Lj-4Lm" secondAttribute="top" id="r6w-7u-t7a"/>
                            <constraint firstItem="6Ej-Lj-4Lm" firstAttribute="trailing" secondItem="eiZ-Oh-415" secondAttribute="trailing" id="vf0-UT-J8m"/>
                            <constraint firstItem="eiZ-Oh-415" firstAttribute="bottom" secondItem="aE7-et-hwo" secondAttribute="bottom" id="wfZ-n2-Vyv"/>
                            <constraint firstItem="eiZ-Oh-415" firstAttribute="leading" secondItem="6Ej-Lj-4Lm" secondAttribute="leading" id="ysU-rS-LGM"/>
                        </constraints>
                    </view>
                    <connections>
                        <outlet property="addressMain" destination="dVn-9a-QIy" id="8Sm-DS-hua"/>
                        <outlet property="addressSubview" destination="nbI-g7-iP6" id="hdh-TC-xDN"/>
                        <outlet property="cartLbl" destination="v14-Pa-av6" id="tRa-cZ-hKC"/>
                        <outlet property="cartView" destination="2fd-Ne-SSM" id="My4-FF-Hhe"/>
                        <outlet property="headerLbl" destination="O0S-jl-s7D" id="CJn-dr-qmx"/>
                        <outlet property="notiLbl" destination="HsU-if-MJ6" id="s6h-35-axO"/>
                        <outlet property="notiView" destination="FEp-Kc-Va5" id="i3e-hx-0lL"/>
                        <outlet property="tableView" destination="UAS-pC-zeN" id="eM8-0j-f4U"/>
                        <outlet property="top2View" destination="aE7-et-hwo" id="mlt-t2-Up0"/>
                        <outlet property="topZView" destination="eiZ-Oh-415" id="OIQ-Yc-Hnk"/>
                    </connections>
                </viewController>
                <placeholder placeholderIdentifier="IBFirstResponder" id="zL1-qe-6vm" userLabel="First Responder" customClass="UIResponder" sceneMemberID="firstResponder"/>
            </objects>
            <point key="canvasLocation" x="388" y="101"/>
        </scene>
    </scenes>
    <resources>
        <image name="cart" width="128" height="128"/>
        <image name="leftarrowB" width="512" height="512"/>
        <image name="notification-status" width="96" height="96"/>
        <systemColor name="systemBackgroundColor">
            <color white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
        </systemColor>
    </resources>
</document>
