//

//  MyOrderVM.swift
//  BabyHouse
//
//  Created by <PERSON> on 19/06/22.
//

import Foundation

class MyAddressViewModel: BaseViewModel {
  
    var dataSource: Int?
    var addressListData : AddressListData?
    var trackingData : orderTrackingData?
  
    required init(with dataSource: Int?) {
        self.dataSource = dataSource
    }
    func getData(completion: ((AddressListData?) -> Void)?) {
        guard let tokenData = GuestData.getUserInfo() else{ return}
        var token =  "Bearer " + tokenData.accessToken
        if  let  tokenValue = UserData.getuserInfo()?.accessToken {
            token = "Bearer " + tokenValue
        }
       
        AddressRequest.addressList(token).response(){ (result: Result<AddressListData, NError>)
            in
            hideActivity()
            switch result {
            case .success(let response):
                self.addressListData = response
                completion?(response)
                
            case .failure(let error):
                DispatchQueue.main.async {
                  hideActivity()
                }
                completion?(nil)
                print(error.localizedDescription)
                

            }
            
       }
    }
    
    func getDetails(data: OrderDetailsData,completion: ((orderTrackingData?) -> Void)?) {
        guard let tokenData = GuestData.getUserInfo() else{ return}
        var token =  "Bearer " + tokenData.accessToken
        if  let  tokenValue = UserData.getuserInfo()?.accessToken {
            token = "Bearer " + tokenValue
        }
       
        OrederRequest.orderDetails(data, token).response(){ (result: Result<orderTrackingData, NError>)
            in
            
            switch result {
            case .success(let response):
                self.trackingData = response
                completion?(response)
                
            case .failure(let error):
                DispatchQueue.main.async {
                  hideActivity()
                }
                completion?(nil)
                print(error.localizedDescription)
                

            }
            
       }
    }
    
  
}



struct AddressListData: Codable {
    let status: Int
    let error: Bool
    let messages: String
    let data: [ALDatum]
}

// MARK: - Datum
struct ALDatum: Codable {
    let id, userID, name, type: String
    let email, mobile: String
  //  let alternateMobile: JSONNull?
    let address, street, floor, block: String
   // let avenue: JSONNull?
    let houseNo: String
   // let building: JSONNull?
    let areaID, countryID, country: String
    let latitude, longitude: String?
    let isDefault, areaName, countryName: String

    enum CodingKeys: String, CodingKey {
        case id
        case userID = "user_id"
        case name, type, email, mobile
      //  case alternateMobile = "alternate_mobile"
        case address, street, floor, block
        case houseNo = "house_no"
        //case building
        case areaID = "area_id"
        case countryID = "country_id"
        case country, latitude, longitude
        case isDefault = "is_default"
        case areaName = "area_name"
        case countryName = "country_name"
    }
}
