//
//  AddressVC.swift
//  Saydality
//
//  Created by <PERSON> on 09/12/21.
//

import UIKit

class AddressVC: UIViewController {
    var viewModel: HomeVM?
    var isFromCheckout = false
    @IBOutlet var cartLbl: UILabel!
    @IBOutlet var cartView: UIView!
    @IBOutlet var notiLbl: UILabel!
    @IBOutlet var notiView: UIView!
    var addressSelected: ((AddressData) -> Void)?
    @IBOutlet var addressSubview: UIView!
    @IBOutlet var addressMain: UIView!
    @IBOutlet var topZView: UIView!
    @IBOutlet var top2View: UIView!
    var isFrommenu = false
    var backtoHome: ((Bool) -> Void)?
    var addresses = [AddressData]()
    @IBOutlet var headerLbl: UILabel!
    @IBOutlet var tableView: UITableView!

    override func viewDidLoad() {
        super.viewDidLoad()

        cartLbl.addCartCountObserver()
        notiLbl.addNotificationCountObserver()

        // Do any additional setup after loading the view.
        tableView.register(AddressListCell.self)
        tableView.delegate = self
        tableView.dataSource = self
        tableView.separatorStyle = .none
        tableView.contentInset = .init(top: 10, left: 0, bottom: 80, right: 0)
        headerLbl.font = UIFont.with(size: 14, .bold)
    }

    override func viewWillAppear(_ animated: Bool) {
        getData()
    }

    override func viewDidLayoutSubviews() {
        DispatchQueue.main.async {
            self.top2View.round(corners: [.bottomLeft, .bottomRight], radius: 20)
            self.topZView.round(corners: [.bottomLeft, .bottomRight], radius: 20)
            self.topZView.gradient(colours: [Babyrose, Babyred])
            self.topZView.setShadow()

            self.addressSubview.cornerRadius = self.addressSubview.frame.height / 2
            self.addressMain.cornerRadius = self.addressMain.frame.height / 2
            self.addressSubview.gradient(colours: [G1, G2, G3])
            // self.addressMain.setShadow()
            self.cartView.cornerRadius = self.cartView.frame.height / 2
            self.notiView.cornerRadius = self.notiView.frame.height / 2
        }
    }

    @IBAction func showNotification(_ sender: Any) {
        let vc = NotificationVC.instantiate()
        vc.isFrommenu = true
        vc.viewModel = NotificationViewModel(with: 0)
        navigationController?.pushViewController(vc, animated: true)
    }

    @IBAction func showCart(_ sender: Any) {
        let vc = ShoppingBagVC.instantiate()
        // vc.backtoHome = true
        vc.viewModel = HomeVM(with: 0)
        navigationController?.pushViewController(vc, animated: true)
    }

    @IBAction func back(_ sender: Any) {
        if isFrommenu {
            navigationController?.popViewController(animated: true)
            return
        } else if isFromCheckout {
            dismiss(animated: true)
        }
        backtoHome?(true)
    }

    @IBAction func addressAdd(_ sender: Any) {
        let vc = AddAddressVC.instantiate()
        vc.addressAdded = { _, _ in
            DispatchQueue.main.async {
                self.getData()
            }
        }

        vc.viewModel = HomeVM(with: 0)
        vc.viewModel1 = EditProfileViewModel(with: 0)
        let nvc = UINavigationController(rootViewController: vc)
        present(nvc, animated: true, completion: nil)
    }
}

extension AddressVC {
    fileprivate func editAddress(data: AddressData) {
        let vc = AddAddressVC.instantiate()
        vc.addressAdded = { _, _ in
            DispatchQueue.main.async {
                self.getData()
            }
        }
        vc.editAddresse = data
        vc.viewModel = HomeVM(with: 0)
        vc.viewModel1 = EditProfileViewModel(with: 0)
        let nvc = UINavigationController(rootViewController: vc)
        present(nvc, animated: true, completion: nil)
    }

    fileprivate func deleteAddress(data: Deleteaddressparam) {
        showActivity()
        viewModel?.deleteAddress(data: data, completion: { homeResponse in

            DispatchQueue.main.async {
                hideActivity()
                guard let home = homeResponse else {
                    let msg = LocalizationSystem.sharedInstance.localizedStringForKey(key: "http_error_text", comment: "")
                    NotificationCenter.default.post(name: .messageAlert, object: msg, userInfo: nil)
                    return
                }
                if home.status == 200 {
                    self.getData()
                }
                NotificationCenter.default.post(name: .messageAlert, object: home.messages, userInfo: nil)
            }
        })
    }

    func openSheet(id: AddressData) {
        let optionMenuController = UIAlertController(title: nil, message: "Choose Option from Action Sheet", preferredStyle: .actionSheet)

        // Create UIAlertAction for UIAlertController

        let addAction = UIAlertAction(title: "Delete", style: .default, handler: {
            (_: UIAlertAction!) in
            if let id = id.id {
                self.deleteAddress(data: Deleteaddressparam(address_id: id))
            }

        })
        let saveAction = UIAlertAction(title: "Edit", style: .default, handler: {
            (_: UIAlertAction!) in
            self.editAddress(data: id)
        })

        let cancelAction = UIAlertAction(title: "Cancel", style: .cancel, handler: {
            (_: UIAlertAction!) in
            print("Cancel")
        })

        // Add UIAlertAction in UIAlertController

        optionMenuController.addAction(addAction)
        optionMenuController.addAction(saveAction)
        optionMenuController.addAction(cancelAction)
        present(optionMenuController, animated: true, completion: nil)
    }

    fileprivate func getData() {
        showActivity()

        viewModel?.addressList(completion: { homeResponse in

            DispatchQueue.main.async {
                hideActivity()
                guard let home = homeResponse, let address = home.data else {
                    let msg = LocalizationSystem.sharedInstance.localizedStringForKey(key: "http_error_text", comment: "")
                    let alert = LocalizationSystem.sharedInstance.localizedStringForKey(key: "no_address_text", comment: "")
                    // self.showMsg?(msg)
                    self.addresses = [AddressData]()
                    self.tableView.reloadData()
                    self.tableView.backgroundText = alert
                    return
                }
                if home.status == 200 {
                    self.tableView.backgroundText = nil
                    self.addresses = address
                    if self.addresses.count == 0 {
                        self.addresses = [AddressData]()
                        self.tableView.backgroundText = LocalizationSystem.sharedInstance.localizedStringForKey(key: "no_address_text", comment: "")
                    }
                    self.tableView.reloadData()
                    return
                }
                self.tableView.backgroundText = LocalizationSystem.sharedInstance.localizedStringForKey(key: "no_address_text", comment: "")
                // self.showMsg?(home.messages)
            }
        })
    }
}

extension AddressVC: UITableViewDataSource, UITableViewDelegate {
    func tableView(_ tableView: UITableView, numberOfRowsInSection section: Int) -> Int {
        addresses.count
    }

    func tableView(_ tableView: UITableView, cellForRowAt indexPath: IndexPath) -> UITableViewCell {
        let cell: AddressListCell = tableView.dequeueReusableCell(for: indexPath)
        cell.address = addresses[indexPath.row]
        cell.sidemenuOult.tag = indexPath.row
        cell.clickedSidemeu = { index in
            let id = self.addresses[index]
            self.openSheet(id: id)
        }
        return cell
    }

    func tableView(_ tableView: UITableView, didSelectRowAt indexPath: IndexPath) {
        if isFromCheckout {
            let address = addresses[indexPath.row]
            addressSelected?(address)
            dismiss(animated: true)
            return
        }
    }
}

extension AddressVC: Storyboarded {
    static var storyboard: Storyboard { .address }
}
