//
//  SplashView.swift
//  Zebeel
//
//  Created by <PERSON><PERSON> on 26/09/20.
//

// import Toast_Swift
import AVFoundation
import Lottie
import UIKit
class SplashView: UIViewController {
    @IBOutlet var splashAnimationView: LottieAnimationView!

    fileprivate func setupAnimation() {
//        splashAnimationView.animation = LottieAnimation.named("login")
        self.splashAnimationView.contentMode = .scaleAspectFit
        self.splashAnimationView.loopMode = .playOnce
        self.splashAnimationView.animationSpeed = 1.0
        self.splashAnimationView.play { finished in
            let countryCode = Locale.current.language.region?.identifier ?? "KW"
            print("🌍 Country Code: \(countryCode)")

            // Log current authentication state
            AuthenticationLogger.logCurrentAuthState()

            // Generate Postman collection for easy testing
            AuthenticationLogger.generatePostmanCollection()

            if let _ = GuestData.getUserInfo() {
                print("✅ Guest token exists, proceeding to dropdown list")
                self.alldropdownlist()
            } else {
                print("❌ No guest token found, initiating guest login")
                let deviceId = UIDevice.current.identifierForVendor?.uuidString
                self.guestLogin(data: guestLoginParam(device_token: deviceId ?? "0", country_code: countryCode, clean_app: "1"))
            }
        }
        
        
    }

    override func viewDidLoad() {
        super.viewDidLoad()
        self.setupAnimation()
        hideNavigationBar()
        
        
    }

    override func viewDidLayoutSubviews() {
        let G1 = UIColor(colorWithHexValue: 0xE55F7C)
        let G2 = UIColor(colorWithHexValue: 0xE55F7B)
        let G3 = UIColor(colorWithHexValue: 0xB92C4B)

        let gradientLayer = CAGradientLayer()
        gradientLayer.frame = self.view.frame
        gradientLayer.colors = [G1, G2, G3]

        gradientLayer.startPoint = CGPoint(x: 0.0, y: 0.95)
        gradientLayer.endPoint = CGPoint(x: 1.0, y: 0.05)
        view.layer.addSublayer(gradientLayer)
        // Add Gradient Layer to Test View
    }

    func alldropdownlist() {
        showActivity()
        guard let tokenData = GuestData.getUserInfo() else { return }
        var token = "Bearer " + tokenData.accessToken
        if let tokenValue = UserData.getuserInfo()?.accessToken {
            token = "Bearer " + tokenValue
        }

        HomeRequest.alldropdownlist(token).response { (result: Result<Alldropdownresponse, NError>)
            in
                hideActivity()
                switch result {
                case .success(let response):
                    DispatchQueue.main.async {
                        UserDefaults.standard.set(try? PropertyListEncoder().encode(response.data), forKey: "Alldropdowninfo")
                        UserDefaults.standard.synchronize()

                        self.navigateDeciderview()
                    }

                case .failure:

                    DispatchQueue.main.async {
                        let msg = LocalizationSystem.sharedInstance.localizedStringForKey(key: "http_error_text", comment: "")
                        self.view.makeToast(msg)
                    }
                }
        }
    }

    func guestLogin(data: guestLoginParam) {
        print("\n🚀 INITIATING GUEST LOGIN")
        print("Device Token: \(data.device_token)")
        print("Country Code: \(data.country_code)")
        print("Clean App: \(data.clean_app)")

        showActivity()
        AutheticationRequest.guestLogin(data).response { (result: Result<GuestModelData, NError>)
            in
                hideActivity()

                switch result {
                case .success(let response):
                    print("\n✅ GUEST LOGIN SUCCESS")
                    print("Status: \(response.status)")
                    print("Error: \(response.error)")
                    print("Message: \(response.messages)")
                    print("Guest Token: \(response.data.accessToken)")
                    print("User ID: \(response.data.userID)")
                    print("Token Type: \(response.data.tokenType)")

                    DispatchQueue.main.async {
                        if response.status == 200 {
                            UserDefaults.standard.set(try? PropertyListEncoder().encode(response.data), forKey: "GustUserinfo")
                            UserDefaults.standard.synchronize()
                            print("✅ Guest token saved to UserDefaults")
                            self.alldropdownlist()
                        }
                    }

                case .failure(let error):
                    print("\n❌ GUEST LOGIN FAILED")
                    print("Error: \(error.localizedDescription)")

                    DispatchQueue.main.async {
                        // hideActivity()
                    }
                }
        }
    }
}




private extension SplashView {
    func navigateDeciderview() {
        var vc: UIViewController
        if SplashView.isOnboardingCompleted {
            vc = HomecontainerVC.instantiate()
        } else {
            vc = GetstartVC.instantiate()
        }

        self.navigationController?.setViewControllers([vc], animated: true)
    }
}

extension SplashView: Storyboarded {
    static var storyboard: Storyboard { .splash }
}

extension SplashView {
    static var isOnboardingCompleted: Bool {
        get { UserDefaults.isOnboardingCompleted }
        set { UserDefaults.isOnboardingCompleted = newValue }
    }
}

extension UserDefaults {
    
    static var fcmToken:String? {
        get { standard.string(forKey: #function) }
        set {
            standard.setValue(newValue, forKey: #function)
            standard.synchronize()
        }
    }
    
    static var isOnboardingCompleted: Bool {
        get { standard.bool(forKey: #function) }
        set {
            standard.set(newValue, forKey: #function)
            standard.synchronize()
        }
    }
}

extension UIApplication {
    var keyWindowPresentedController: UIViewController? {
        var viewController = self.keyWindow?.rootViewController

        // If root `UIViewController` is a `UITabBarController`
        if let presentedController = viewController as? UITabBarController {
            // Move to selected `UIViewController`
            viewController = presentedController.selectedViewController
        }

        // Go deeper to find the last presented `UIViewController`
        while let presentedController = viewController?.presentedViewController {
            // If root `UIViewController` is a `UITabBarController`
            if let presentedController = presentedController as? UITabBarController {
                // Move to selected `UIViewController`
                viewController = presentedController.selectedViewController
            } else {
                // Otherwise, go deeper
                viewController = presentedController
            }
        }

        return viewController
    }
}

extension UIViewController {
    func presentInKeyWindow(animated: Bool = true, completion: (() -> Void)? = nil) {
        DispatchQueue.main.async {
            UIApplication.shared.keyWindow?.rootViewController?
                .present(self, animated: animated, completion: completion)
        }
    }

    func presentInKeyWindowPresentedController(animated: Bool = true, completion: (() -> Void)? = nil) {
        DispatchQueue.main.async {
            UIApplication.shared.keyWindowPresentedController?
                .present(self, animated: animated, completion: completion)
        }
    }
}
