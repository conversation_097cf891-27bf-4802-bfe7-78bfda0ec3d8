//
//  TermsVC.swift
//  BabyHouse
//
//  Created by <PERSON> on 03/04/22.
//

import UIKit

class TermsVC: UIViewController {
    
    @IBOutlet weak var top2View: UIView!
    @IBOutlet weak var topZView: UIView!
    @IBOutlet weak var headerLbl: UILabel!

    override func viewDidLoad() {
        super.viewDidLoad()

        // Do any additional setup after loading the view.
    }
    

    /*
    // MARK: - Navigation

    // In a storyboard-based application, you will often want to do a little preparation before navigation
    override func prepare(for segue: UIStoryboardSegue, sender: Any?) {
        // Get the new view controller using segue.destination.
        // Pass the selected object to the new view controller.
    }
    */
    
    @IBAction func back(_ sender: Any) {
        self.navigationController?.popViewController(animated:true)
    }

}

extension TermsVC:Storyboarded {
    static var storyboard: Storyboard { .cart }
}


