<?xml version="1.0" encoding="UTF-8"?>
<document type="com.apple.InterfaceBuilder3.CocoaTouch.XIB" version="3.0" toolsVersion="22154" targetRuntime="iOS.CocoaTouch" propertyAccessControl="none" useAutolayout="YES" useTraitCollections="YES" useSafeAreas="YES" colorMatched="YES">
    <device id="retina6_1" orientation="portrait" appearance="light"/>
    <dependencies>
        <plugIn identifier="com.apple.InterfaceBuilder.IBCocoaTouchPlugin" version="22130"/>
        <capability name="Safe area layout guides" minToolsVersion="9.0"/>
        <capability name="System colors in document resources" minToolsVersion="11.0"/>
        <capability name="documents saved in the Xcode 8 format" minToolsVersion="8.0"/>
    </dependencies>
    <objects>
        <placeholder placeholderIdentifier="IBFilesOwner" id="-1" userLabel="File's Owner"/>
        <placeholder placeholderIdentifier="IBFirstResponder" id="-2" customClass="UIResponder"/>
        <tableViewCell contentMode="scaleToFill" selectionStyle="default" indentationWidth="10" rowHeight="180" id="KGk-i7-Jjw" customClass="CartTableViewCell" customModule="BabyHouse" customModuleProvider="target">
            <rect key="frame" x="0.0" y="0.0" width="566" height="170"/>
            <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMaxY="YES"/>
            <tableViewCellContentView key="contentView" opaque="NO" clipsSubviews="YES" multipleTouchEnabled="YES" contentMode="center" tableViewCell="KGk-i7-Jjw" id="H2p-sc-9uM">
                <rect key="frame" x="0.0" y="0.0" width="566" height="170"/>
                <autoresizingMask key="autoresizingMask"/>
                <subviews>
                    <view clipsSubviews="YES" contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="CuS-lA-HHt">
                        <rect key="frame" x="20" y="40" width="90" height="90"/>
                        <subviews>
                            <imageView clipsSubviews="YES" userInteractionEnabled="NO" contentMode="scaleAspectFill" horizontalHuggingPriority="251" verticalHuggingPriority="251" image="logo-head-white" translatesAutoresizingMaskIntoConstraints="NO" id="Xw5-vi-qXJ">
                                <rect key="frame" x="0.0" y="0.0" width="90" height="90"/>
                                <userDefinedRuntimeAttributes>
                                    <userDefinedRuntimeAttribute type="number" keyPath="cornerRadiusIB">
                                        <real key="value" value="16"/>
                                    </userDefinedRuntimeAttribute>
                                </userDefinedRuntimeAttributes>
                            </imageView>
                            <view hidden="YES" alpha="0.80000000000000004" contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="6lt-lS-FhK">
                                <rect key="frame" x="0.0" y="32.5" width="90" height="25"/>
                                <subviews>
                                    <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Sold Out" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="cSS-ZW-hGx">
                                        <rect key="frame" x="17" y="4" width="56.5" height="17"/>
                                        <fontDescription key="fontDescription" type="system" pointSize="14"/>
                                        <color key="textColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                        <nil key="highlightedColor"/>
                                    </label>
                                </subviews>
                                <color key="backgroundColor" red="0.76470588240000004" green="0.2156862745" blue="0.33725490200000002" alpha="1" colorSpace="calibratedRGB"/>
                                <constraints>
                                    <constraint firstAttribute="height" constant="25" id="brg-EP-PVx"/>
                                    <constraint firstItem="cSS-ZW-hGx" firstAttribute="centerX" secondItem="6lt-lS-FhK" secondAttribute="centerX" id="jvY-oU-g1Q"/>
                                    <constraint firstItem="cSS-ZW-hGx" firstAttribute="centerY" secondItem="6lt-lS-FhK" secondAttribute="centerY" id="tLr-JZ-7fr"/>
                                </constraints>
                                <userDefinedRuntimeAttributes>
                                    <userDefinedRuntimeAttribute type="number" keyPath="cornerRadiusIB">
                                        <real key="value" value="0.0"/>
                                    </userDefinedRuntimeAttribute>
                                </userDefinedRuntimeAttributes>
                            </view>
                        </subviews>
                        <color key="backgroundColor" red="0.99215686274509807" green="0.95294117647058818" blue="0.96078431372549022" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                        <constraints>
                            <constraint firstAttribute="trailing" secondItem="Xw5-vi-qXJ" secondAttribute="trailing" id="5eB-ex-EKa"/>
                            <constraint firstAttribute="trailing" secondItem="6lt-lS-FhK" secondAttribute="trailing" id="9X2-Hf-ugQ"/>
                            <constraint firstItem="6lt-lS-FhK" firstAttribute="centerY" secondItem="Xw5-vi-qXJ" secondAttribute="centerY" id="Bgb-uU-64H"/>
                            <constraint firstAttribute="height" constant="90" id="Twa-Jp-dCV"/>
                            <constraint firstAttribute="width" secondItem="CuS-lA-HHt" secondAttribute="height" multiplier="1:1" id="bNf-2a-Zgf"/>
                            <constraint firstItem="Xw5-vi-qXJ" firstAttribute="leading" secondItem="CuS-lA-HHt" secondAttribute="leading" id="bn7-6D-1zn"/>
                            <constraint firstItem="6lt-lS-FhK" firstAttribute="leading" secondItem="CuS-lA-HHt" secondAttribute="leading" id="cMc-ad-aK7"/>
                            <constraint firstAttribute="bottom" secondItem="Xw5-vi-qXJ" secondAttribute="bottom" id="mQj-is-guR"/>
                            <constraint firstItem="Xw5-vi-qXJ" firstAttribute="top" secondItem="CuS-lA-HHt" secondAttribute="top" id="rQH-oJ-CtM"/>
                        </constraints>
                        <userDefinedRuntimeAttributes>
                            <userDefinedRuntimeAttribute type="number" keyPath="layer.cornerRadius">
                                <integer key="value" value="20"/>
                            </userDefinedRuntimeAttribute>
                        </userDefinedRuntimeAttributes>
                    </view>
                    <stackView opaque="NO" contentMode="scaleToFill" axis="vertical" spacing="15" translatesAutoresizingMaskIntoConstraints="NO" id="5kP-RH-ifl">
                        <rect key="frame" x="130" y="15" width="426" height="135"/>
                        <subviews>
                            <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="vVg-YW-iSU">
                                <rect key="frame" x="0.0" y="0.0" width="426" height="20"/>
                                <subviews>
                                    <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Metalic Tougue " textAlignment="natural" lineBreakMode="tailTruncation" numberOfLines="2" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="UUa-5V-uoY">
                                        <rect key="frame" x="0.0" y="8" width="378" height="12"/>
                                        <fontDescription key="fontDescription" type="system" pointSize="17"/>
                                        <color key="textColor" red="0.0" green="0.0" blue="0.0" alpha="1" colorSpace="calibratedRGB"/>
                                        <nil key="highlightedColor"/>
                                    </label>
                                    <imageView clipsSubviews="YES" userInteractionEnabled="NO" contentMode="scaleAspectFit" horizontalHuggingPriority="251" verticalHuggingPriority="251" image="vuesax-outline-trash" translatesAutoresizingMaskIntoConstraints="NO" id="ti2-Vd-ymc">
                                        <rect key="frame" x="386" y="1.5" width="25" height="25"/>
                                        <color key="tintColor" systemColor="labelColor"/>
                                        <constraints>
                                            <constraint firstAttribute="width" constant="25" id="N88-vK-7eG"/>
                                            <constraint firstAttribute="height" constant="25" id="ozg-iE-WPC"/>
                                        </constraints>
                                    </imageView>
                                    <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" buttonType="system" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="THi-HP-yHy">
                                        <rect key="frame" x="381" y="0.0" width="45" height="20"/>
                                        <constraints>
                                            <constraint firstAttribute="width" constant="45" id="vDH-Mr-oaY"/>
                                        </constraints>
                                        <state key="normal" title="  "/>
                                        <connections>
                                            <action selector="deleteAction:" destination="KGk-i7-Jjw" eventType="touchUpInside" id="sYJ-PD-qAj"/>
                                        </connections>
                                    </button>
                                </subviews>
                                <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                <constraints>
                                    <constraint firstAttribute="trailing" secondItem="THi-HP-yHy" secondAttribute="trailing" id="3Ol-yi-xZU"/>
                                    <constraint firstItem="THi-HP-yHy" firstAttribute="top" secondItem="vVg-YW-iSU" secondAttribute="top" id="4RJ-1D-LFs"/>
                                    <constraint firstAttribute="bottom" secondItem="UUa-5V-uoY" secondAttribute="bottom" id="B6C-Lx-uxd"/>
                                    <constraint firstItem="ti2-Vd-ymc" firstAttribute="leading" secondItem="UUa-5V-uoY" secondAttribute="trailing" constant="8" id="QUc-rc-Itw"/>
                                    <constraint firstAttribute="trailing" secondItem="ti2-Vd-ymc" secondAttribute="trailing" constant="15" id="STI-WK-cLL"/>
                                    <constraint firstItem="UUa-5V-uoY" firstAttribute="leading" secondItem="vVg-YW-iSU" secondAttribute="leading" id="WmK-A4-faA"/>
                                    <constraint firstItem="ti2-Vd-ymc" firstAttribute="centerY" secondItem="UUa-5V-uoY" secondAttribute="centerY" id="mNg-QR-hIo"/>
                                    <constraint firstItem="UUa-5V-uoY" firstAttribute="top" secondItem="vVg-YW-iSU" secondAttribute="top" constant="8" id="nVh-XG-sJ8"/>
                                    <constraint firstAttribute="bottom" secondItem="THi-HP-yHy" secondAttribute="bottom" id="pt3-PN-AGj"/>
                                </constraints>
                            </view>
                            <stackView opaque="NO" contentMode="scaleToFill" axis="vertical" spacing="4" translatesAutoresizingMaskIntoConstraints="NO" id="wJf-F1-UmH">
                                <rect key="frame" x="0.0" y="35" width="426" height="100"/>
                                <subviews>
                                    <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="AcZ-lB-mgT">
                                        <rect key="frame" x="0.0" y="0.0" width="426" height="100"/>
                                        <subviews>
                                            <stackView opaque="NO" contentMode="scaleToFill" distribution="equalSpacing" spacing="5" translatesAutoresizingMaskIntoConstraints="NO" id="9Yg-o1-ios">
                                                <rect key="frame" x="0.0" y="0.0" width="426" height="100"/>
                                                <subviews>
                                                    <stackView opaque="NO" contentMode="scaleToFill" axis="vertical" spacing="8" translatesAutoresizingMaskIntoConstraints="NO" id="QAl-pn-90N">
                                                        <rect key="frame" x="0.0" y="0.0" width="69.5" height="100"/>
                                                        <subviews>
                                                            <stackView opaque="NO" contentMode="scaleToFill" spacing="8" translatesAutoresizingMaskIntoConstraints="NO" id="4hI-i3-e4y">
                                                                <rect key="frame" x="0.0" y="0.0" width="69.5" height="20.5"/>
                                                                <subviews>
                                                                    <stackView opaque="NO" contentMode="scaleToFill" spacing="8" translatesAutoresizingMaskIntoConstraints="NO" id="GaS-dZ-ZWI">
                                                                        <rect key="frame" x="0.0" y="0.0" width="69.5" height="20.5"/>
                                                                        <subviews>
                                                                            <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="SKU" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="nQb-hi-KDT">
                                                                                <rect key="frame" x="0.0" y="0.0" width="33.5" height="20.5"/>
                                                                                <fontDescription key="fontDescription" type="system" pointSize="17"/>
                                                                                <color key="textColor" white="0.66666666669999997" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                                                                <nil key="highlightedColor"/>
                                                                            </label>
                                                                            <label opaque="NO" userInteractionEnabled="NO" contentMode="left" verticalHuggingPriority="251" text="55" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="WAc-Hf-rn6">
                                                                                <rect key="frame" x="41.5" y="0.0" width="28" height="20.5"/>
                                                                                <fontDescription key="fontDescription" type="system" pointSize="17"/>
                                                                                <color key="textColor" white="0.66666666669999997" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                                                                <nil key="highlightedColor"/>
                                                                            </label>
                                                                        </subviews>
                                                                    </stackView>
                                                                    <stackView hidden="YES" opaque="NO" contentMode="scaleToFill" spacing="8" translatesAutoresizingMaskIntoConstraints="NO" id="94D-Oc-me2">
                                                                        <rect key="frame" x="69.5" y="0.0" width="8" height="20.5"/>
                                                                        <subviews>
                                                                            <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Colour: " textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="zBs-sv-lmd">
                                                                                <rect key="frame" x="0.0" y="0.0" width="0.0" height="20.5"/>
                                                                                <fontDescription key="fontDescription" type="system" pointSize="17"/>
                                                                                <color key="textColor" red="0.7843137255" green="0.78039215689999997" blue="0.78039215689999997" alpha="1" colorSpace="calibratedRGB"/>
                                                                                <nil key="highlightedColor"/>
                                                                            </label>
                                                                            <label opaque="NO" userInteractionEnabled="NO" contentMode="left" verticalHuggingPriority="251" text="blue" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="uXH-HT-m3x">
                                                                                <rect key="frame" x="8" y="0.0" width="0.0" height="20.5"/>
                                                                                <fontDescription key="fontDescription" type="system" pointSize="17"/>
                                                                                <color key="textColor" red="0.7843137255" green="0.78039215689999997" blue="0.78039215689999997" alpha="1" colorSpace="calibratedRGB"/>
                                                                                <nil key="highlightedColor"/>
                                                                            </label>
                                                                        </subviews>
                                                                    </stackView>
                                                                </subviews>
                                                            </stackView>
                                                            <stackView opaque="NO" contentMode="scaleToFill" spacing="8" translatesAutoresizingMaskIntoConstraints="NO" id="0xr-F3-Cen">
                                                                <rect key="frame" x="0.0" y="28.5" width="69.5" height="20.5"/>
                                                                <subviews>
                                                                    <stackView opaque="NO" contentMode="scaleToFill" spacing="8" translatesAutoresizingMaskIntoConstraints="NO" id="hhf-94-7Zd">
                                                                        <rect key="frame" x="0.0" y="0.0" width="69.5" height="20.5"/>
                                                                        <subviews>
                                                                            <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Size: " textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="TxQ-dz-yvS">
                                                                                <rect key="frame" x="0.0" y="0.0" width="41" height="20.5"/>
                                                                                <fontDescription key="fontDescription" type="system" pointSize="17"/>
                                                                                <color key="textColor" white="0.66666666666666663" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                                                                <nil key="highlightedColor"/>
                                                                            </label>
                                                                            <label opaque="NO" userInteractionEnabled="NO" contentMode="left" verticalHuggingPriority="251" text="55" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="xpb-SA-IHh">
                                                                                <rect key="frame" x="49" y="0.0" width="20.5" height="20.5"/>
                                                                                <fontDescription key="fontDescription" type="system" pointSize="17"/>
                                                                                <color key="textColor" white="0.66666666666666663" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                                                                <nil key="highlightedColor"/>
                                                                            </label>
                                                                        </subviews>
                                                                    </stackView>
                                                                    <stackView hidden="YES" opaque="NO" contentMode="scaleToFill" spacing="8" translatesAutoresizingMaskIntoConstraints="NO" id="RKs-I6-ejA">
                                                                        <rect key="frame" x="69.5" y="0.0" width="8" height="20.5"/>
                                                                        <subviews>
                                                                            <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Colour: " textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="4sq-Rp-JBy">
                                                                                <rect key="frame" x="0.0" y="0.0" width="0.0" height="20.5"/>
                                                                                <fontDescription key="fontDescription" type="system" pointSize="17"/>
                                                                                <color key="textColor" red="0.7843137255" green="0.78039215689999997" blue="0.78039215689999997" alpha="1" colorSpace="calibratedRGB"/>
                                                                                <nil key="highlightedColor"/>
                                                                            </label>
                                                                            <label opaque="NO" userInteractionEnabled="NO" contentMode="left" verticalHuggingPriority="251" text="blue" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="dbe-Y5-gsm">
                                                                                <rect key="frame" x="8" y="0.0" width="0.0" height="20.5"/>
                                                                                <fontDescription key="fontDescription" type="system" pointSize="17"/>
                                                                                <color key="textColor" red="0.7843137255" green="0.78039215689999997" blue="0.78039215689999997" alpha="1" colorSpace="calibratedRGB"/>
                                                                                <nil key="highlightedColor"/>
                                                                            </label>
                                                                        </subviews>
                                                                    </stackView>
                                                                </subviews>
                                                            </stackView>
                                                            <stackView opaque="NO" contentMode="scaleToFill" spacing="8" translatesAutoresizingMaskIntoConstraints="NO" id="VLD-kX-84u">
                                                                <rect key="frame" x="0.0" y="57" width="69.5" height="17"/>
                                                                <subviews>
                                                                    <stackView opaque="NO" contentMode="scaleToFill" spacing="8" translatesAutoresizingMaskIntoConstraints="NO" id="3Pq-JT-B4l">
                                                                        <rect key="frame" x="0.0" y="0.0" width="69.5" height="17"/>
                                                                        <subviews>
                                                                            <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Stock" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="SB1-1a-MMQ">
                                                                                <rect key="frame" x="0.0" y="0.0" width="40" height="17"/>
                                                                                <fontDescription key="fontDescription" type="boldSystem" pointSize="14"/>
                                                                                <color key="textColor" white="0.66666666669999997" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                                                                <nil key="highlightedColor"/>
                                                                            </label>
                                                                            <label opaque="NO" userInteractionEnabled="NO" contentMode="left" verticalHuggingPriority="251" text="55" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="Cqg-vN-AQH">
                                                                                <rect key="frame" x="48" y="0.0" width="21.5" height="17"/>
                                                                                <fontDescription key="fontDescription" type="boldSystem" pointSize="14"/>
                                                                                <color key="textColor" white="0.66666666669999997" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                                                                <nil key="highlightedColor"/>
                                                                            </label>
                                                                        </subviews>
                                                                    </stackView>
                                                                </subviews>
                                                            </stackView>
                                                            <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="KD 0.00" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="TUb-zy-6R4">
                                                                <rect key="frame" x="0.0" y="82" width="69.5" height="18"/>
                                                                <fontDescription key="fontDescription" type="system" pointSize="17"/>
                                                                <color key="textColor" red="0.74901960784313726" green="0.20392156862745098" blue="0.32156862745098036" alpha="1" colorSpace="calibratedRGB"/>
                                                                <nil key="highlightedColor"/>
                                                            </label>
                                                        </subviews>
                                                    </stackView>
                                                    <stackView opaque="NO" contentMode="scaleToFill" distribution="fillEqually" spacing="15" translatesAutoresizingMaskIntoConstraints="NO" id="piR-mb-Bd1">
                                                        <rect key="frame" x="306" y="0.0" width="120" height="100"/>
                                                        <subviews>
                                                            <view clipsSubviews="YES" contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="8d5-i3-xa1">
                                                                <rect key="frame" x="0.0" y="0.0" width="120" height="100"/>
                                                                <subviews>
                                                                    <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="2js-Zf-4Iz">
                                                                        <rect key="frame" x="0.0" y="4" width="120" height="50"/>
                                                                        <subviews>
                                                                            <stackView opaque="NO" contentMode="scaleToFill" distribution="fillEqually" translatesAutoresizingMaskIntoConstraints="NO" id="MnU-N9-Pdn">
                                                                                <rect key="frame" x="0.0" y="0.0" width="120" height="50"/>
                                                                                <subviews>
                                                                                    <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" buttonType="system" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="Wiw-iB-GyE">
                                                                                        <rect key="frame" x="0.0" y="0.0" width="40" height="50"/>
                                                                                        <fontDescription key="fontDescription" type="system" pointSize="20"/>
                                                                                        <state key="normal" title="-">
                                                                                            <color key="titleColor" systemColor="labelColor"/>
                                                                                        </state>
                                                                                        <connections>
                                                                                            <action selector="minusAction:" destination="KGk-i7-Jjw" eventType="touchUpInside" id="zp3-cH-CKP"/>
                                                                                        </connections>
                                                                                    </button>
                                                                                    <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="1" textAlignment="center" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="gfx-y7-4bm">
                                                                                        <rect key="frame" x="40" y="0.0" width="40" height="50"/>
                                                                                        <fontDescription key="fontDescription" type="system" pointSize="20"/>
                                                                                        <color key="textColor" red="0.74901960779999999" green="0.20392156859999999" blue="0.32156862749999998" alpha="1" colorSpace="calibratedRGB"/>
                                                                                        <nil key="highlightedColor"/>
                                                                                    </label>
                                                                                    <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" buttonType="system" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="G2X-o4-gAM">
                                                                                        <rect key="frame" x="80" y="0.0" width="40" height="50"/>
                                                                                        <fontDescription key="fontDescription" type="system" pointSize="20"/>
                                                                                        <state key="normal" title="+">
                                                                                            <color key="titleColor" systemColor="labelColor"/>
                                                                                        </state>
                                                                                        <connections>
                                                                                            <action selector="plusAction:" destination="KGk-i7-Jjw" eventType="touchUpInside" id="BhD-ZZ-W0U"/>
                                                                                        </connections>
                                                                                    </button>
                                                                                </subviews>
                                                                            </stackView>
                                                                        </subviews>
                                                                        <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                                                        <constraints>
                                                                            <constraint firstAttribute="bottom" secondItem="MnU-N9-Pdn" secondAttribute="bottom" id="9P2-XG-Z46"/>
                                                                            <constraint firstAttribute="width" constant="120" id="LXQ-YG-t1q"/>
                                                                            <constraint firstItem="MnU-N9-Pdn" firstAttribute="leading" secondItem="2js-Zf-4Iz" secondAttribute="leading" id="i7A-8q-Whf"/>
                                                                            <constraint firstAttribute="height" constant="50" id="q1g-jK-lO7"/>
                                                                            <constraint firstItem="MnU-N9-Pdn" firstAttribute="top" secondItem="2js-Zf-4Iz" secondAttribute="top" id="v9W-dd-HtZ"/>
                                                                            <constraint firstAttribute="trailing" secondItem="MnU-N9-Pdn" secondAttribute="trailing" id="zgY-kI-8Bv"/>
                                                                        </constraints>
                                                                        <userDefinedRuntimeAttributes>
                                                                            <userDefinedRuntimeAttribute type="number" keyPath="layer.cornerRadius">
                                                                                <integer key="value" value="10"/>
                                                                            </userDefinedRuntimeAttribute>
                                                                        </userDefinedRuntimeAttributes>
                                                                    </view>
                                                                </subviews>
                                                                <color key="backgroundColor" systemColor="systemBackgroundColor"/>
                                                                <constraints>
                                                                    <constraint firstItem="2js-Zf-4Iz" firstAttribute="top" secondItem="8d5-i3-xa1" secondAttribute="top" constant="4" id="MkO-Wj-EfK"/>
                                                                    <constraint firstAttribute="trailing" secondItem="2js-Zf-4Iz" secondAttribute="trailing" id="Nvw-cK-epo"/>
                                                                    <constraint firstItem="2js-Zf-4Iz" firstAttribute="leading" secondItem="8d5-i3-xa1" secondAttribute="leading" id="j9y-g4-H4A"/>
                                                                </constraints>
                                                            </view>
                                                        </subviews>
                                                    </stackView>
                                                </subviews>
                                            </stackView>
                                        </subviews>
                                        <color key="backgroundColor" systemColor="systemBackgroundColor"/>
                                        <constraints>
                                            <constraint firstItem="9Yg-o1-ios" firstAttribute="leading" secondItem="AcZ-lB-mgT" secondAttribute="leading" id="4gv-rR-bLM"/>
                                            <constraint firstAttribute="height" constant="100" id="7LX-H0-ZAa"/>
                                            <constraint firstItem="9Yg-o1-ios" firstAttribute="top" secondItem="AcZ-lB-mgT" secondAttribute="top" id="Q9z-uZ-0uy"/>
                                            <constraint firstAttribute="trailing" secondItem="9Yg-o1-ios" secondAttribute="trailing" id="gjQ-nu-ube"/>
                                            <constraint firstAttribute="bottom" secondItem="9Yg-o1-ios" secondAttribute="bottom" id="khV-32-wHn"/>
                                        </constraints>
                                    </view>
                                </subviews>
                            </stackView>
                        </subviews>
                    </stackView>
                </subviews>
                <color key="backgroundColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                <constraints>
                    <constraint firstAttribute="bottom" relation="greaterThanOrEqual" secondItem="CuS-lA-HHt" secondAttribute="bottom" constant="20" id="0fA-b3-rmM"/>
                    <constraint firstAttribute="trailing" secondItem="5kP-RH-ifl" secondAttribute="trailing" constant="10" id="38S-de-kJ2"/>
                    <constraint firstItem="CuS-lA-HHt" firstAttribute="centerY" secondItem="H2p-sc-9uM" secondAttribute="centerY" id="8dY-rI-9tU"/>
                    <constraint firstAttribute="bottom" secondItem="5kP-RH-ifl" secondAttribute="bottom" constant="20" id="IF7-qk-drB"/>
                    <constraint firstItem="5kP-RH-ifl" firstAttribute="top" secondItem="H2p-sc-9uM" secondAttribute="top" constant="15" id="OsR-Tr-epP"/>
                    <constraint firstItem="CuS-lA-HHt" firstAttribute="leading" secondItem="H2p-sc-9uM" secondAttribute="leading" constant="20" id="PTn-gL-ljZ"/>
                    <constraint firstItem="5kP-RH-ifl" firstAttribute="leading" secondItem="CuS-lA-HHt" secondAttribute="trailing" constant="20" id="sb2-AG-0Oh"/>
                    <constraint firstItem="CuS-lA-HHt" firstAttribute="top" relation="greaterThanOrEqual" secondItem="H2p-sc-9uM" secondAttribute="top" constant="20" id="yC5-DW-flo"/>
                </constraints>
            </tableViewCellContentView>
            <viewLayoutGuide key="safeArea" id="njF-e1-oar"/>
            <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
            <connections>
                <outlet property="colourLbl" destination="4sq-Rp-JBy" id="OK7-0J-puG"/>
                <outlet property="colurAmtLbl" destination="dbe-Y5-gsm" id="1eo-gZ-B1F"/>
                <outlet property="countLbl" destination="gfx-y7-4bm" id="DR1-3O-YyX"/>
                <outlet property="deleteBtn" destination="THi-HP-yHy" id="hMD-zp-BfX"/>
                <outlet property="imageProf" destination="Xw5-vi-qXJ" id="7hN-3S-2U0"/>
                <outlet property="imgView" destination="Xw5-vi-qXJ" id="ewY-pU-zgp"/>
                <outlet property="itemView" destination="CuS-lA-HHt" id="QSz-30-lEa"/>
                <outlet property="minusBtn" destination="Wiw-iB-GyE" id="vzI-eL-pnh"/>
                <outlet property="nameLbl" destination="UUa-5V-uoY" id="tdv-77-Ogc"/>
                <outlet property="paymentView" destination="2js-Zf-4Iz" id="dZT-iN-mXL"/>
                <outlet property="plusBtn" destination="G2X-o4-gAM" id="WGy-2V-GZ0"/>
                <outlet property="priceLbl" destination="TUb-zy-6R4" id="dAV-Jg-aNT"/>
                <outlet property="sizeAmtLbl" destination="xpb-SA-IHh" id="RnT-ed-Tb6"/>
                <outlet property="sizeLbl" destination="TxQ-dz-yvS" id="uKR-H4-sFm"/>
                <outlet property="skuLbl" destination="nQb-hi-KDT" id="3UT-qD-jYp"/>
                <outlet property="skuValueLbl" destination="WAc-Hf-rn6" id="8Yq-DF-8PQ"/>
                <outlet property="soldOutView" destination="6lt-lS-FhK" id="Pgd-cZ-r0j"/>
                <outlet property="stockLbl" destination="SB1-1a-MMQ" id="Uws-aU-zge"/>
                <outlet property="stockValue" destination="Cqg-vN-AQH" id="gEN-nT-Dq7"/>
            </connections>
            <point key="canvasLocation" x="-372.46376811594206" y="121.875"/>
        </tableViewCell>
    </objects>
    <resources>
        <image name="logo-head-white" width="140" height="98"/>
        <image name="vuesax-outline-trash" width="24" height="24"/>
        <systemColor name="labelColor">
            <color white="0.0" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
        </systemColor>
        <systemColor name="systemBackgroundColor">
            <color white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
        </systemColor>
    </resources>
</document>
