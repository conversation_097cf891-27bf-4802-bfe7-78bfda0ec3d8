//
//  ShoppingBagVC.swift
//  BabyHouse
//
//  Created by <PERSON> on 03/04/22.
//

import UIKit

class ShoppingBagVC: UIViewController, UIViewControllerTransitioningDelegate {

    @IBOutlet weak var contineuBtn: UIButton!
    @IBOutlet weak var tetelLbl: UILabel!
    @IBOutlet weak var tableView: UITableView!
    @IBOutlet weak var noteSectionView:UIStackView!
    @IBOutlet weak var continueSectionView:UIView!
   
    var cartData: CartData?
    var viewModel: HomeVM?
    var backtoHome:((Bool)-> Void)?
    @IBOutlet weak var headerLbl: UILabel!
    @IBOutlet weak var top2View: UIView!
    @IBOutlet weak var topZView: UIView!
    var itemsArray = [Items]()
    override func viewDidLoad() {
        super.viewDidLoad()

        cartSumarry()
        tableView.register(CartTableViewCell.self)
        
        orderTXt.font = UIFont.with(size:12,.regular)
    }
    override func viewDidLayoutSubviews() {
        self.contineuBtn.cornerRadius = self.contineuBtn.frame.height / 2
        self.contineuBtn.horizontalgradient(colours:[G1,G2,G3])
        self.top2View.round(corners:[.bottomLeft,.bottomRight], radius: 20)
        self.topZView.round(corners:[.bottomLeft,.bottomRight], radius: 20)
        self.topZView.gradient(colours:[Babyrose,Babyred])
        self.topZView.setShadow()
        //self.cartView.cornerRadius = self.cartView.frame.height / 2
        //self.notiView.cornerRadius = self.notiView.frame.height / 2
    }
    
    var orderNotes:String = ""
    @IBOutlet private weak var orderTXt:UILabel!
    @IBAction func orderNote(_ sender: Any) {
        let view = OrderNoteVC.instantiate()
        view.modalPresentationStyle = .overCurrentContext
        view.currentNote = { (note) in
            self.orderTXt.text = "Order Note: " + note
            self.orderNotes =  note
        }
        present(view, animated:true, completion:nil)
    }
    

    @IBAction func checkout(_ sender: Any) {
        let view = CheckOutTotelVC.instantiate()
        view.modalPresentationStyle = .popover
        view.transitioningDelegate = self
        view.price = self.tetelLbl.text!
        view.checkoutPlease = { (status) in
            
            
//            let vc = PaymentVC.instantiate()
//            vc.viewModel = HomeVM(with: 0)
//            vc.price = self.tetelLbl.text!
//            vc.orderNotes = self.orderNotes
//            vc.itemCount = String(self.itemsArray.count)
//           self.navigationController?.pushViewController(vc, animated:true)
            
            let order = OrderModel(price: self.tetelLbl.text ?? "", orderNote: self.orderNotes, codAvailableStatus: self.cartData?.codAvailableStatus ?? "0", itemCount: self.itemsArray.count)
            let viewModel = PaymentViewModel(order: order, viewModel: HomeVM(with: 0))
            let swiftUIView = PaymentView(viewModel: viewModel)
            self.navigationController?.pushSwiftUIView(swiftUIView: swiftUIView)
            
            
            
        }
        present(view, animated:true, completion:nil)
    }
    @IBAction func back(_ sender: Any) {
        self.navigationController?.popViewController(animated:true)
    }
    
}
extension ShoppingBagVC {
    fileprivate func deletecartItem(data:Deletecartitem) {
        
        showActivity()
        self.viewModel?.cartDeleteItem(data:data,completion: { (data) in
            hideActivity()
            
            DispatchQueue.main.async {
                hideActivity()
                guard let response = data else {
                    let msg = LocalizationSystem.sharedInstance.localizedStringForKey(key: "http_error_text", comment: "")
                    self.view.makeToast(msg)
                    return
                }
                
                if response.status == 200 && response.error == false {
                    self.cartSumarry()
//                    NotificationCenter.refreshHome()
                 return
                }
               
            }
        })
    }
    fileprivate func cartQuanty(data:Changequanty) {
        
        showActivity()
        self.viewModel?.cartQtyChange(data:data,completion: { (data,error)  in
            hideActivity()
            
            DispatchQueue.main.async {
                hideActivity()
                
                if let error = error {
                    self.view.makeToast(error)
                }else{
                    guard let response = data else {
                        let msg = LocalizationSystem.sharedInstance.localizedStringForKey(key: "http_error_text", comment: "")
                        self.view.makeToast(msg)
                        return
                    }
                    
                    if response.status == 200 && response.error == false {
                        self.cartSumarry()
                     return
                    }else{
                        self.view.makeToast(response.messages)
                    }
                }
                
               
               
            }
        })
    }
    fileprivate func cartSumarry() {
        
        showActivity()
        self.viewModel?.cartSummary(completion: { data, error  in
            hideActivity()
            
            DispatchQueue.main.async {
                hideActivity()
                guard let response = data else {
                    let msg = LocalizationSystem.sharedInstance.localizedStringForKey(key: "http_error_text", comment: "")
                    self.view.makeToast(msg)
                    self.itemsArray = [Items]()
                    self.tableView.reloadData()
                    self.tableView.backgroundText = "No items found"
                    self.continueSectionView.isHidden = true
                    self.noteSectionView.isHidden = true
                    return
                }
                
                if response.status == 200 && response.error == false {
                    guard let items = response.data?.items,items.count > 0  else {
                        self.tableView.backgroundText = "No items found"
                        self.continueSectionView.isHidden = true
                        self.noteSectionView.isHidden = true
                        self.itemsArray = [Items]()
                        self.tableView.reloadData()
                        return
                    }
                    self.itemsArray = items
                    self.tableView.reloadData()
                    self.cartData = response.data
                    self.tetelLbl.text = "\(response.data?.subTotal ?? "0.0")"
                    self.continueSectionView.isHidden = false
                    self.noteSectionView.isHidden = false
                 return
                }
                self.tableView.backgroundText = "No items found"
                self.continueSectionView.isHidden = true
                self.noteSectionView.isHidden = true
                self.tableView.reloadData()
            }
        })
    }
}
extension ShoppingBagVC: UITableViewDelegate,UITableViewDataSource {
    func tableView(_ tableView: UITableView, numberOfRowsInSection section: Int) -> Int {
        return itemsArray.count
    }
    
    func tableView(_ tableView: UITableView, cellForRowAt indexPath: IndexPath) -> UITableViewCell {
        let cell: CartTableViewCell = tableView.dequeueReusableCell(for: indexPath)
        cell.items = itemsArray[indexPath.row]
        cell.minusBtn.tag = indexPath.row
        cell.plusBtn.tag  = indexPath.row
        cell.deleteBtn.tag = indexPath.row
        cell.cartAction = { (index,action) in
            switch action {
                
            case .minus:print("minus")
                guard let qty = self.itemsArray[index].qty ,var count = Int(qty),let varid = self.itemsArray[index].productVariantID  else { return}
                count = count - 1
                let lastCount = count == 0 ? "1" : String(count)
                self.cartQuanty(data: Changequanty(product_variant_id:varid, quantity: lastCount))
            case .plus: print("plus")
                guard let qty = self.itemsArray[index].qty ,var count = Int(qty),let varid = self.itemsArray[index].productVariantID else { return}
                count = count + 1
                let lastCount = count == 0 ? "1" : String(count)
                self.cartQuanty(data: Changequanty(product_variant_id:varid, quantity: lastCount))
            case .delete: print("delete")
                guard let varid = self.itemsArray[index].productVariantID,!varid.isEmpty else { return}
                self.deletecartItem(data:Deletecartitem(product_variant_id:varid))
            }
        }
        return cell
    }
    
    
}

extension ShoppingBagVC:Storyboarded {
    static var storyboard: Storyboard { .cart }
}
