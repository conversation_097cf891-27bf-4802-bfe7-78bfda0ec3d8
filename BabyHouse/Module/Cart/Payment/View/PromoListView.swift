//
//  PromoListView.swift
//  BabyHouse
//
//  Created by Apple on 27/10/23.
//

import SwiftUI

struct PromoListView: View {
    let availableOffers: [AvailableOffer]
    let onOfferSelect: (AvailableOffer?) -> Void
    @State private var selectedOption: AvailableOffer?
    var body: some View {
        promocodeSection
    }

    var promocodeSection: some View {
        HStack {
            Menu {
                ForEach(availableOffers) { option in
                    Button {
                        // do something

                        selectedOption = option
                        onOfferSelect(option)

                    } label: {
                        Text(option.promotionText)
                        Image(.icons8Discount)
                    }
                }
            }
            label: {
                HStack {
                    if availableOffers.isEmpty {
                        HStack {
                            Text("No offers are currently available")
                            Image(systemName: "chevron.down")
                        }.opacity(0.6)

                    } else {
                        if let selectedOption = selectedOption {
                            HStack {
                                Text(selectedOption.promotionText)
                                    .scaledToFill()
                                    .minimumScaleFactor(0.5)
                                    .lineLimit(1)
                            }
                        } else {
                            Text("Select an Offer")
                                .opacity(0.6)
                        }

                        Image(systemName: "chevron.down")
                    }
                    if let _ = selectedOption {
                        Spacer()
                    }
                }
                .bold()
                .foregroundColor(.black.opacity(0.7))
                .padding()
            }
            .animation(.spring, value: selectedOption)
            .frame(maxWidth: .infinity, maxHeight: 30)

            if let _ = selectedOption {
                Spacer()

                Button(action: {
                    self.selectedOption = nil
                    self.onOfferSelect(nil)
                }, label: {
                    Image(systemName: "xmark.circle.fill")
                        .tint(Color(hex: "#BE3251"))
                })
            }
        }
        .padding(.horizontal)
        .padding(.vertical, 10)
        .background(.white)
        .cornerRadius(12)
        .padding(1)
        .background(Color(hex: "#BE3251"))
        .cornerRadius(12)
    }
}

#Preview {
    PromoListView(availableOffers: [.init(promotionID: "", promotionTitle: "", promotionText: "")], onOfferSelect: { _ in })
}
