//
//  PaymentView.swift
//  BabyHouse
//
//  Created by Apple on 11/09/23.
//

import SwiftUI

struct PaymentView: View {
    @StateObject var viewModel: PaymentViewModel
    @Namespace private var animation

    var body: some View {
        VStack(spacing: 0) {
            CommonNavigationBar(title: "Checkout", showActions: true)
                .shadow(radius: 2)

            ScrollView {
                VStack(spacing: 28) {
                    deliverySection
                    paymentMethodSection
//                    loyaltyPointsSection

                    if let _ = UserData.getuserInfo() {
                        promocodeSection
                    }

                    giftWrapSection

                    paymentSummerySection
                    termsAndConditionSection

                    payNowButtonSection
                }
                .padding(20.0.relativeWidth)
            }

            .frame(maxWidth: .infinity, alignment: .leading)
        }
        .frame(maxWidth: .infinity, maxHeight: .infinity, alignment: .topLeading)
    }

    var deliverySection: some View {
        VStack(alignment: .leading, spacing: 28) {
            Text("Delivery")
                .font(.title3.bold())

            HStack(spacing: 16) {
                Image("location")
                    .resizable()
                    .aspectRatio(contentMode: .fit)
                    .frame(width: 16.0.relativeHeight, height: 21.0.relativeHeight)
                    .frame(width: 36.0.relativeHeight, height: 36.0.relativeHeight)
                    .padding(4)
                    .background(Color(hex: "#FCEAEE"))
                    .cornerRadius(10)
                    .shadow(color: .init(hex: "#C034524F").opacity(0.5), radius: 4, x: 2, y: 2)

                VStack(alignment: .leading) {
                    Text("Shipping Address")
                        .fixedSize()
                    Text(viewModel.shippingAddress ?? "Sorry, There is no shipping address")
                        .font(.footnote).foregroundColor(.gray)
                }
                Spacer()
                Button {
                    viewModel.onAddAddress()
                } label: {
                    Text("Add Address")
                        .fixedSize()
                        .foregroundColor(.white)
                        .padding(.horizontal)
                        .padding(.vertical, 12)
                        //                            .padding()
                        .background(Color(hex: "#B92C4B").gradient)
                        .cornerRadius(12)
                }
            }
        }
    }

    var paymentMethodSection: some View {
        VStack(alignment: .leading, spacing: 16) {
            Text("Payment Method").font(.title3.bold())

            VStack(alignment: .leading, spacing: 28) {
                ForEach(viewModel.paymentMethodList) { method in
                    Button {
                        Utilities.withHeroAnimation {
                            viewModel.selectedPaymentMethod = method
                        }
                    } label: {
                        HStack(spacing: 16) {
                            Circle()
                                .stroke(viewModel.selectedPaymentMethod == method ? Color(hex: "#BE3251") : Color(hex: "#CCCCCC"), lineWidth: 4)
                                .if(viewModel.selectedPaymentMethod == method) {
                                    $0.matchedGeometryEffect(id: "circle_border", in: animation)
                                }
                                .frame(width: 25, height: 25)

                            HStack(spacing: 10) {
                                HStack(spacing: 2) {
                                    ForEach(method.images, id: \.self) {
                                        Image($0)
                                            .resizable()
                                            .aspectRatio(contentMode: .fit)
                                            .frame(width: 36, height: 30)
                                    }
                                }
                                Text(method.title)
                            }
                            .foregroundColor(.init(hex: "#191919"))
                        }
                    }
                }

            }.padding()
        }
        .frame(maxWidth: .infinity, alignment: .leading)
    }

    var loyaltyPointsSection: some View {
        VStack(alignment: .leading, spacing: 28) {
            Text("Loyalty Points").font(.title3.bold())

            Toggle("Redeem Loyalty Cash (Available: 0)", isOn: $viewModel.isLoyaltyChecked).toggleStyle(CheckBoxToggleStyle())
                .foregroundColor(Color(hex: "#191919"))

        }.frame(maxWidth: .infinity, alignment: .leading)
    }

    var giftWrapSection: some View {
        HStack {
            HStack {
                Toggle(isOn: $viewModel.isGiftWrapChecked) {
                    Text("Gift Wrap (KWD: 2.000)")
                        .foregroundColor(.init(hex: "#B92C4B"))
                        .bold()
                }.toggleStyle(CheckBoxToggleStyle())
                Spacer()
                Image("gift")
                    .padding(.trailing, -12)
            }
        }
        .frame(maxWidth: .infinity)
        .padding(.horizontal, 32)
        .padding(.vertical, 10)
        .background(.white)
        .clipShape(Capsule())
        .padding(1)
        .background(Color(hex: "#B92C4B"))
        .clipShape(Capsule())
        .onTapGesture {
            viewModel.isGiftWrapChecked.toggle()
        }

        .onChange(of: viewModel.isGiftWrapChecked, perform: viewModel.onGiftWrap)
    }

    var promocodeSection: some View {
//        HStack {
//            TextField("Promo Code", text: $viewModel.promoCode)
//                .hideWithOpacity(viewModel.promoApplied)
//            Button {
//                Utilities.withHeroAnimation {
//                    viewModel.onApplyPromoCode()
//                }
//            } label: {
//                Text(viewModel.promoApplied ? "Remove" : "Apply")
//                    .foregroundColor(.white)
//                    .bold()
//                    .padding(.horizontal)
//                    .padding(.vertical, 10)
//                    .background(Color(hex: "#BE3251").gradient)
//                    .cornerRadius(8)
//                    .padding(.trailing)
//            }
//            .hideWithOpacity(viewModel.promoCode.isEmpty)
//        }
//        .padding(.horizontal)
//        .padding(.vertical, 10)
//        .background(.white)
//        .clipShape(Capsule())
//        .padding(1)
//        .background(Color(hex: "#BE3251"))
//        .clipShape(Capsule())
        PromoListView(availableOffers: viewModel.cartSummeryData?.availableOffers ?? [], onOfferSelect: { offer in
            viewModel.cartParams.promotionID = offer?.promotionID
            viewModel.getCartSummary()
        })
    }

    var paymentSummerySection: some View {
        VStack(alignment: .leading, spacing: 16) {
            Text("Payment Summery").font(.title3.bold())

            let order = viewModel.order

            VStack(spacing: 16) {
                
                if let subTotal = viewModel.cartSummeryData?.subTotal {
                    HStack(spacing: 16) {
                        Text("Sub Total")
                        Line()
                            .stroke(style: StrokeStyle(lineWidth: 1, dash: [5]))
                            .foregroundColor(.init(hex: "#BB2F4E"))
                            .frame(height: 1)
                        Text(subTotal)
                    }
                }
                
               
                if let deliveryCharge = viewModel.cartSummeryData?.deliveryCharge {
                    HStack(spacing: 16) {
                        Text("Delivery")
                        Line()
                            .stroke(style: StrokeStyle(lineWidth: 1, dash: [5]))
                            .foregroundColor(.init(hex: "#BB2F4E"))
                            .frame(height: 1)
                        Text(deliveryCharge)
                    }
                }
               

                if let giftWrapCharge =  viewModel.cartSummeryData?.giftWrapCharge, viewModel.isGiftWrapChecked {
                    HStack(spacing: 16) {
                        Text("Gift Wrap")
                        Line()
                            .stroke(style: StrokeStyle(lineWidth: 1, dash: [5]))
                            .foregroundColor(.init(hex: "#BB2F4E"))
                            .frame(height: 1)
                        Text(giftWrapCharge)
                    }.animation(.hero, value: viewModel.isGiftWrapChecked)
                }
                
                if let discount = viewModel.cartSummeryData?.promotionDiscount, discount != "0"  {
                    HStack(spacing: 16) {
                        Text("Discount")
                        Line()
                            .stroke(style: StrokeStyle(lineWidth: 1, dash: [5]))
                            .foregroundColor(.init(hex: "#BB2F4E"))
                            .frame(height: 1)
                        Text("-" + discount.toDouble.toCurrencyString)
                    }
                }

                if let cartTotal = viewModel.cartSummeryData?.amountInclusiveTax {
                    VStack {
                        HStack(spacing: 16) {
                            Text("Bag Total")
                            Line()
                                .stroke(style: StrokeStyle(lineWidth: 1, dash: [5]))
                                .foregroundColor(.init(hex: "#BB2F4E"))
                                .frame(height: 1)
                            VStack {
                                Text(cartTotal)
                            }
                        }
                        HStack {
                            Spacer()
                            Text("(\(order.itemCount) item)")
                                .font(.footnote)
                                .foregroundColor(.gray)
                        }
                    }
                }
            }.fontWeight(.bold)

        }.frame(maxWidth: .infinity, alignment: .leading)
    }

    var termsAndConditionSection: some View {
        Toggle(isOn: $viewModel.isTermsAndConditionChecked) {
            Text("I have read and agree to the term and conditions")
                .bold()
                + Text("*")
                .font(.custom("Metropolis", size: 16.0.relativeFontSize))
                .baselineOffset(1.0)
        }
        .toggleStyle(CheckBoxToggleStyle())
        .foregroundColor(Color(hex: "#191919"))
    }

    var payNowButtonSection: some View {
        Button {
            viewModel.onPayNow()
        } label: {
            Text("Pay Now")
                .foregroundColor(.white)
                .font(.title3.bold())
                .padding()
                .frame(maxWidth: .infinity)
                .background(Color(hex: "#B92C4B").gradient)
                .clipShape(Capsule())
                .shadow(color: Color(hex: "#B92C4B").opacity(0.5), radius: 4, y: 4)
        }
        .padding(.horizontal, 64)
        .hideWithOpacity(!viewModel.isTermsAndConditionChecked)
        .alert(item: $viewModel.alertModel, content: { model in
            Alert(title: Text(model.title), message: Text(model.message), dismissButton: .default(Text("Ok")))
        })
    }
}

struct HideWithOpacityModifier: ViewModifier {
    let condition: Bool
    func body(content: Content) -> some View {
        content
            .disabled(condition)
            .opacity(condition ? 0.4 : 1.0)
    }
}

extension View {
    func hideWithOpacity(_ condition: Bool) -> some View {
        modifier(HideWithOpacityModifier(condition: condition))
    }
}

//
// struct PaymentView_Previews: PreviewProvider {
//    static var previews: some View {
//        PromoCodeView()
//    }
// }

extension ToggleStyle where Self == CheckBoxToggleStyle {
    static var checkbox: CheckBoxToggleStyle {
        return CheckBoxToggleStyle()
    }
}

// Custom Toggle Style
struct CheckBoxToggleStyle: ToggleStyle {
    func makeBody(configuration: Configuration) -> some View {
        Button {
            configuration.isOn.toggle()
        } label: {
            Label {
                configuration.label
            } icon: {
                Image(systemName: configuration.isOn ? "checkmark.square.fill" : "square")
                    .foregroundColor(.init(hex: "#B92C4B"))
                    .accessibility(label: Text(configuration.isOn ? "Checked" : "Unchecked"))
                    .imageScale(.large)
                    .if(configuration.isOn, transform: { $0.shadow(color: .gray.opacity(0.2), radius: 1, y: 3) })
            }
        }
        .buttonStyle(PlainButtonStyle())
    }
}

struct Line: Shape {
    func path(in rect: CGRect) -> Path {
        var path = Path()
        path.move(to: CGPoint(x: 0, y: 0))
        path.addLine(to: CGPoint(x: rect.width, y: 0))
        return path
    }
}
