//
//  PaymentModel.swift
//  BabyHouse
//
//  Created by Apple on 12/09/23.
//

import Foundation

struct PaymentMethodModel: Identifiable, Equatable {
    let id: String = UUID().uuidString
    let images: [String]
    let title, type: String
    
}


struct OrderModel {
    var price,orderNote, codAvailableStatus: String
    let itemCount:Int
}


//vc.viewModel = HomeVM(with: 0)
//vc.price = self.tetelLbl.text!
//vc.orderNotes = self.orderNotes
//vc.itemCount = String(self.itemsArray.count)
