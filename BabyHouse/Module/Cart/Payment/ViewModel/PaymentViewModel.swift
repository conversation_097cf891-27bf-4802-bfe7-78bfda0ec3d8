//
//  PaymentViewModel.swift
//  BabyHouse
//
//  Created by Apple on 12/09/23.
//

import Foundation
import SwiftUI
import UIKit

class PaymentViewModel: ObservableObject {
    @Published var grandTotal: Double = 0.000
    @Published var isLoyaltyChecked: Bool = false
    @Published var isGiftWrapChecked: Bool = false
    @Published var isTermsAndConditionChecked: Bool = false
    @Published var selectedPaymentMethod: PaymentMethodModel?
    @Published var promoCode: String = .init()
    @Published var promoCodeModel: APDataClass?
    @Published var shippingAddress: String?
    @Published var alertModel: AlertModel?
    @Published var deliveryCharge: Double = 0
    @Published var cartSummeryData: CartData?
    @Published var cartParams: CartParams

    var giftWrapCharge: Double = 2.0

    var paymentMethodList: [PaymentMethodModel] = []

    var bagTotal: Double {
        var value: Double = grandTotal
        value += deliveryCharge
        return value
    }

    var addaddressData: AddaddressData?
    var addressId: String = ""
    var promoApplied: Bool { promoCodeModel != nil }

    let order: OrderModel
    let viewModel: HomeVM

    init(order: OrderModel, viewModel: HomeVM) {
        self.order = order
        self.viewModel = viewModel
        cartParams = .init()
        onInit()
        handleCodUpate()
        getCartSummary()
    }

    func handleCodUpate() {
        if order.codAvailableStatus == "1" {
            paymentMethodList.append(.init(images: ["delivery-truck"], title: "Cash On Delivery", type: "COD"))
        }
        paymentMethodList.append(.init(images: ["visa", "knet", "MasterCard_logo"], title: "", type: "knet"))
        selectedPaymentMethod = paymentMethodList.first
    }

    func onInit() { grandTotal += order.price.toDouble }

    func onGiftWrap(value: Bool) {
        cartParams.giftwrap = value.convertToInt
        getCartSummary()
    }

    func updateCartSummeryData(data: CartData?) {
        cartSummeryData = data
    }

    func updatePromoCodeModel(model: APDataClass?) {
        if let model = model {
            grandTotal -= model.finalDiscount.toDouble
        } else {
            grandTotal += model?.finalDiscount.toDouble ?? 0.000
        }
        promoCodeModel = model
    }

    func onAddAddress() {
        if let _ = UserData.getuserInfo() {
            onUserAddressSelect()
        } else {
            onGuestAddressSelect()
        }
    }

    func onUserAddressSelect() {
        let viewController = AddressVC.instantiate()
        viewController.viewModel = HomeVM(with: 0)
        viewController.isFromCheckout = true
        viewController.addressSelected = onUserAddressSelect
        let navigationViewController = UINavigationController(rootViewController: viewController)
        UIApplication.presentViewController(navigationViewController, animated: true)
    }

    func onGuestAddressSelect() {
        let vc = AddAddressVC.instantiate()
        vc.addressAdded = { _, data in
            self.addaddressData = data
            self.onGuestAddressSelect(data)
        }

        vc.viewModel = HomeVM(with: 0)
        vc.viewModel1 = EditProfileViewModel(with: 0)
        let nvc = UINavigationController(rootViewController: vc)
        UIApplication.presentViewController(nvc, animated: true)
    }

    func onGuestAddressSelect(_ addressData: AddaddressData) {
        onUpdateAddress(areaID: addressData.area_id)

        let address = addressData.address
//        deliveryCharge = addressData.
        if !address.isEmpty {
            let name: String = addressData.name
            shippingAddress = "\(name), \(address)"
        } else {
            let area = addressData.area
            let governate = addressData.governorate
            shippingAddress = area + " ," + governate
        }
    }

    func onUserAddressSelect(_ addressData: AddressData) {
        guard let id = addressData.id else { return }
        onUpdateAddress(id: id)
        addressId = id
        let address = addressData.address ?? ""
//        deliveryCharge = addressData.
        if !address.isEmpty {
            let name: String = addressData.name ?? ""
            shippingAddress = "\(name), \(address)"
        } else {
            let area = addressData.area_name ?? ""
            let governorate = addressData.governorate ?? ""
            shippingAddress = area + " ," + governorate
        }
    }

    func onUpdateAddress(id: String? = nil, areaID: String? = nil) {
        cartParams.addressid = id
        cartParams.areaID = areaID
        getCartSummary()
    }

    func onPayNow() { checkoutApiCall() }

    func onApplyPromoCode() { promoCodeApiCall() }
}

extension PaymentViewModel {
    func getCartSummary() {
        showActivity()
        viewModel.cartSummary(params: cartParams, completion: { data, error in

            Utilities.enQueue {
                hideActivity()

                if let error = error {
                    Utilities.showToast(error)
                } else {
                    if data?.error == false {
                        self.updateCartSummeryData(data: data?.data)
                    } else {
                        Utilities.showToast(data?.messages ?? "Something went wrong")
                    }
                }
            }

        })
    }

    func promoCodeApiCall() {
        showActivity()
        viewModel.applyPromotions(data: promoCodeApply(promo_code: promoCode), completion: { data, error in
            hideActivity()

            if let error = error {
                Utilities.showToast(error)
            } else {
                if data?.error == false {
                    self.updatePromoCodeModel(model: data?.data)
                } else {
                    Utilities.showToast(data?.messages ?? "Something went wrong")
                }
            }
        })
    }

    func checkoutApiCall() {
        if addressId.isEmpty, addaddressData == nil {
            let msg = LocalizationSystem.sharedInstance.localizedStringForKey(key: "address_select_txt", comment: "")

            alertModel = AlertModel(title: "Error", message: msg)

            return
        }
        showActivity()

        var data: placeOrderData

        if let _ = UserData.getuserInfo() {
            data = placeOrderData(payment_method: selectedPaymentMethod?.type, promo_code: cartParams.promotionID, applying_promo: cartParams.promotionID, address_id: addressId, giftwrap: isGiftWrapChecked.convertToInt, order_notes: order.orderNote, is_loyalty_checked: isLoyaltyChecked.convertToInt)
        } else {
            data = placeOrderData(payment_method: selectedPaymentMethod?.type, promo_code: cartParams.promotionID, applying_promo: nil, address_id: nil, giftwrap: isGiftWrapChecked.convertToInt, order_notes: order.orderNote, is_loyalty_checked: nil, name: addaddressData?.name, email: addaddressData?.email, mobile: addaddressData?.mobile, type: addaddressData?.type, alternate_mobile: addaddressData?.alternate_mobile, address: addaddressData?.address, street: addaddressData?.street, block: addaddressData?.block, floor: nil, avenue: addaddressData?.avenue, house_no: addaddressData?.house_no, building: nil, area_id: addaddressData?.area_id, country_id: "117", country: "Kuwait", latitude: nil, longitude: nil, appartment: addaddressData?.apartment, buynow: nil, governorate_id: addaddressData?.governorate_id)
        }

        viewModel.payFinalCheckout(data: data, completion: { data in
            hideActivity()
            Utilities.enQueue {
                guard let data = data, data.status == 200, data.error == false else {
                    Utilities.showToast(data?.messages ?? "")
                    return
                }

                let poDataClass = data.data

                if let paymentLink = poDataClass?.paymentLink, self.selectedPaymentMethod?.type == "knet" {
                    self.onPaymentLink(with: paymentLink)
                } else {
                    let orderCount: Int = poDataClass?.orderItemData?.count ?? 0

                    if orderCount > 0 {
                        
                        if let _ = UserData.getuserInfo() {
                            self.navigateToPaymentStatusPage(poDataClass: poDataClass)
                        }else{
                            self.navigateToPaymentStatusPage(poDataClass: poDataClass,isGuestCheckout: true)
                        }
                        
                    }
                }
            }
        })
    }

    func navigateToPaymentStatusPage(failureReason: String? = nil, poDataClass: PODataClass?, isGuestCheckout: Bool = false) {
        let vc = PaymentsuccessVC.instantiate()
        vc.orderid = poDataClass?.orderID?.toString ?? ""
        vc.amout = poDataClass?.payableAmount?.toCurrencyString ?? ""
        vc.failureReason = failureReason
        vc.isGuestCheckout = isGuestCheckout
        UIApplication.pushViewController(vc, animated: true, firstPopRoot: failureReason == nil)
    }

    func onPaymentCompletion(_ status: PaymentStatusType) {
        switch status {
        case let .success(poDataClass):
            if let _ = UserData.getuserInfo() {
                navigateToPaymentStatusPage(poDataClass: poDataClass)
            } else {
                navigateToPaymentStatusPage(poDataClass: poDataClass, isGuestCheckout: true)
            }
        case let .failure(reasone: reasone):
            navigateToPaymentStatusPage(failureReason: reasone, poDataClass: nil)
        case .canceled:
            print("Caneceled")
        case .none:
            break
        }
    }

    func onPaymentLink(with link: String) {
        UIApplication.pushViewController(UIHostingController(rootView: WebContainer(urlString: link, html: nil, onCompletion: onPaymentCompletion)), animated: true)
    }
}

extension Bool {
    var convertToInt: Int { self ? 1 : 0 }
}

extension Double {
    var toString: String {
        String(format: "%.3f", self)
    }

    var toCurrencyString: String {
        String(format: "KWD %.3f", self)
    }
}

extension String {
    var toDouble: Double { Double(stripCurrencySymbol) ?? 0.000 }

    var stripCurrencySymbol: String {
        var value: String = self
        if value.contains("KWD") {
            value = value.replacingOccurrences(of: "KWD", with: "")
        }
        value = value.replacingOccurrences(of: " ", with: "")
        return value
    }
}

extension String {
    static let kuwaitCurrencyCode = "KWD"
}
