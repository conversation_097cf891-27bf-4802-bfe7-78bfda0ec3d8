//
//  WebView.swift
//  BabyHouse
//
//  Created by Apple on 27/09/23.
//

import SwiftUI

struct WebContainer: View {
    var title:String = "Payment Gateway"
    let urlString:String
    let html:String?
    let onCompletion: (PaymentStatusType) -> Void
    @StateObject private var webViewModel:WebViewModel = WebViewModel()
    var body: some View {
            WithNavigationBarView(title: title,showActions: false,scrollable: false) {
                LoadingView(isShowing: $webViewModel.isLoading, message: "Loading...") {
                    WebView(onCompletion: onCompletion, webViewModel: webViewModel, urlString: urlString, html: html)
                }
            }
            
        
       
    }
}

//#Preview {
//    WebContainer()
//}
