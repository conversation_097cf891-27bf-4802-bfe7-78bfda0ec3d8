//
//  WebContainerView.swift
//  BabyHouse
//
//  Created by Apple on 27/09/23.
//

import SwiftUI
import Swifty<PERSON><PERSON>N
import WebKit

class WebViewModel: ObservableObject {
    @Published var isLoading: Bool = false
}

struct WebView: UIViewRepresentable {
    let onCompletion: (PaymentStatusType) -> Void
    @SwiftUI.Environment(\.dismiss) private var dismiss
    @ObservedObject var webViewModel: WebViewModel
    let urlString: String
    let html: String?

    func makeUIView(context: Context) -> WKWebView {
        let wkWebView = WKWebView()

        wkWebView.uiDelegate = context.coordinator
        wkWebView.navigationDelegate = context.coordinator
        wkWebView.allowsBackForwardNavigationGestures = true
        wkWebView.scrollView.isScrollEnabled = true

        if let html = html {
            wkWebView.loadHTMLString(html, baseURL: nil)
        } else {
            if let url = URL(string: urlString) {
                let urlRequest = URLRequest(url: url)
                wkWebView.load(urlRequest)
            }
        }
        return wkWebView
    }

    func updateUIView(_ wkWebView: WKWebView, context: Context) {
        // do nothing
    }

    class Coordinator: NSObject, WKNavigationDelegate, WKUIDelegate {
        let webViewModel: WebViewModel
        let onCompletion: (PaymentStatusType) -> Void
        let dismiss: DismissAction
        init(_ webViewModel: WebViewModel, onCompletion: @escaping (PaymentStatusType) -> Void, dismiss: DismissAction) {
            self.webViewModel = webViewModel
            self.onCompletion = onCompletion
            self.dismiss = dismiss
        }

        func webView(_ webView: WKWebView, didStartProvisionalNavigation navigation: WKNavigation!) {
            webViewModel.isLoading = true
        }

        func webView(_ webView: WKWebView, didFinish navigation: WKNavigation!) {
            webViewModel.isLoading = false
            let url = webView.url

            if let url = url {
                let onSucess = url.absoluteString.contains("paymentSuccess")
                let onFailure = url.absoluteString.contains("paymentFailure")
                let onCancel = url.absoluteString.contains("paymentcancel")

                webView.evaluateJavaScript("document.body.innerText", completionHandler: { (value: Any!, error: Error!) in
                    if error != nil {
                        // Error logic
                        return
                    }

                    // let result = value as? String
                    // Main logic

                    if let data = value as? String {
                        let json = SwiftyJSON.JSON(parseJSON: data)

                        if onSucess {
                            if let error = json["error"].bool, !error {
                                let jsonData = json["data"]

                                let orderID: Int? = jsonData["order_id"].stringValue.toInt
                                let transactionID: String = jsonData["transaction_id"].stringValue
                                let payableAmount: Double? = jsonData["total"].stringValue.toDouble

                                let model = PODataClass(error: nil, message: nil, orderID: orderID, transactionID: transactionID, payableAmount: payableAmount, orderItemData: nil, paymentLink: nil)
                                self.dismiss()
                                self.onCompletion(.success(data: model))
                            }
                        } else if onFailure {
                            var reson: String = "Unknown Reason"
                            if let error = json["error"].bool, error {
                                reson = json["messages"].stringValue
                            }
                            self.onCompletion(.failure(reasone: reson))
                        } else if onCancel {
//                            self.onCompletion(.failure(reasone: "User Cancelled"))
                        }
                    }
                })
            }
        }

        func webView(_ webView: WKWebView, decidePolicyFor navigationAction: WKNavigationAction, decisionHandler: @escaping (WKNavigationActionPolicy) -> Void) {
//
//            let url = navigationAction.request.url
//            let data = navigationAction.request.httpBody
//
//            if let data = data{
//                if let url = url {
//                    print(url)
//                    let onSucess = url.absoluteString.contains("paymentSuccess")
//                    let onFailure = url.absoluteString.contains("paymentFailure")
//                    let onCancel = url.absoluteString.contains("paymentcancel")
//
//                    if onSucess {
//                        print(url)
//
//                            let json = try? SwiftyJSON.JSON(data: data)
//                            if let json = json {
//                                if let error = json["error"].bool, !error {
//                                    let jsonData = json["data"]
//
//                                    let orderID:Int? = jsonData["order_id"].stringValue.toInt
//                                    let transactionID:String = jsonData["transaction_id"].stringValue
//                                    let payableAmount:Double? = jsonData["total"].stringValue.toDouble
//
//                                    let model = PODataClass(error: nil, message: nil, orderID: orderID, transactionID: transactionID, payableAmount: payableAmount, orderItemData: nil, paymentLink: nil)
//                                    onCompletion(.success(data: model))
//
//                                }
//
//
//                            }
//
//
//                    }else if(onFailure){
//                        print(url)
//                            var reson:String = "Unknown Reason"
//                            let json = try? SwiftyJSON.JSON(data: data)
//                            if let json = json {
//                                if let error = json["error"].bool, error {
//                                    reson = json["messages"].stringValue
//                                }
//                            }
//                            onCompletion(.failure(reasone: reson))
//
//                    }
//                    else if (onCancel){
//    //                    print(url)
//    //                    onCompletion(.failure(reasone: "User Cancelled"))
//                    }
//                }
//            }

            decisionHandler(.allow)
        }
    }

    func makeCoordinator() -> WebView.Coordinator {
        Coordinator(webViewModel, onCompletion: onCompletion, dismiss: dismiss)
    }
}

// struct WebView_Previews: PreviewProvider {
//    static var previews: some View {
//        WebView(webViewModel: WebViewModel(),
//                urlString: "https://instagram.com/mahmudahsan/")
//    }
// }
