//
//  WebView.swift
//  BabyHouse
//
//  Created by Apple on 26/09/23.
//

import Foundation
import WebKit
import SwiftUI
import <PERSON><PERSON><PERSON><PERSON><PERSON>


struct WebContentView: UIViewRepresentable {
    
    
    
    let webView: WKWebView
    let onCompletion: (PaymentStatusType) -> Void
    @SwiftUI.Environment(\.dismiss) private var dismiss
    
    func makeUIView(context: Context) -> WKWebView {
//            webView
        webView.uiDelegate = context.coordinator
        webView.navigationDelegate = context.coordinator
        webView.allowsBackForwardNavigationGestures = true
        webView.scrollView.isScrollEnabled = true
        return webView
    }
    
    
    
    func updateUIView(_ uiView: WKWebView, context: Context) {}
    
    func load(web url: URL) {
        webView.load(URLRequest(url: url))
    }
    
    func makeCoordinator() -> Coordinator {
        Coordinator(self, when: { fddf in
        }, when: onCompletion, dismiss: {
            self.dismiss()
        })
    }
    
    class Coordinator: NSObject, WKUIDelegate, WKNavigationDelegate {
        var parent: WebContentView
        let processed: (Bool) -> Void
        let onCompletion: (PaymentStatusType) -> Void
        let dismiss:() -> Void
        private var finalResponseURL: URL?
        
        init(_ parent: WebContentView, when processed: @escaping (Bool) -> Void, when completed: @escaping (PaymentStatusType) -> Void, dismiss: @escaping () -> Void) {
            self.processed = processed
            self.onCompletion = completed
            self.dismiss = dismiss
            self.parent = parent
        }
        
        // Delegate methods go here
        
        func webView(_ webView: WKWebView, didFinish navigation: WKNavigation!) {
            Utilities.enQueue {
                if self.finalResponseURL != nil {
                    self.processed(true)
                }
            }
        }
        
        func webView(_ webView: WKWebView, decidePolicyFor navigationAction: WKNavigationAction, decisionHandler: @escaping (WKNavigationActionPolicy) -> Void) {
            // TODO:
//            if let url = navigationAction.request.url,
//               url.absoluteString.contains("tap_id")
//            {
//                finalResponseURL = url
//                
//            }
            
            let url = navigationAction.request.url
            let data = navigationAction.request.httpBody
            
            
            if let url = url {
                
                let onSucess = url.absoluteString.contains("paymentSuccess")
                let onFailure = url.absoluteString.contains("paymentFailure")
                let onCancel = url.absoluteString.contains("paymentcancel")
                
                if onSucess {
                    print(url)
                    if let data = data{
                        let json = try? SwiftyJSON.JSON(data: data)
                        if let json = json {
                            if let error = json["error"].bool, !error {
                                let jsonData = json["data"]
                                
                                let orderID:Int? = jsonData["order_id"].stringValue.toInt
                                let transactionID:String = jsonData["transaction_id"].stringValue
                                let payableAmount:Double? = jsonData["total"].stringValue.toDouble
                                
                                let model = PODataClass(error: nil, message: nil, orderID: orderID, transactionID: transactionID, payableAmount: payableAmount, orderItemData: nil, paymentLink: nil)
                                self.dismiss()
                                onCompletion(.success(data: model))
                                
                            }
                            
                            
                        }
                    }
                    
                }else if(onFailure){
                    print(url)
                    if let data = data{
                        var reson:String = "Unknown Reason"
                        let json = try? SwiftyJSON.JSON(data: data)
                        if let json = json {
                            if let error = json["error"].bool, error {
                                reson = json["messages"].stringValue
                            }
                        }
                        onCompletion(.failure(reasone: reson))
                    }
                }
                else if (onCancel){
                    print(url)
                    onCompletion(.failure(reasone: "User Cancelled"))
                }
            }
            decisionHandler(.allow)
        }
        
        func webView(_ webView: WKWebView, decidePolicyFor navigationResponse: WKNavigationResponse, decisionHandler: @escaping (WKNavigationResponsePolicy) -> Void) {
            // TODO:
            decisionHandler(.allow)
        }
        
        func webView(_ webView: WKWebView, runJavaScriptAlertPanelWithMessage message: String, initiatedByFrame frame: WKFrameInfo, completionHandler: @escaping () -> Void) {
            // alert functionality goes here
        }
    }
    
    init(web url: URL, when completed: @escaping (PaymentStatusType) -> Void) {
     
        let configuration = WKWebViewConfiguration()
        configuration.websiteDataStore = .nonPersistent()
        self.webView = WKWebView(frame: .zero, configuration: configuration)
//            webView.navigationDelegate = navigation
        self.onCompletion = completed
        load(web: url)
        
    }
}

enum PaymentStatusType: Equatable {
    static func == (lhs: PaymentStatusType, rhs: PaymentStatusType) -> Bool {
        switch (lhs, rhs) {
                        case (.success, .success),
                             (.failure, .failure),
                            (.canceled, .canceled),
                            (.none, .none):
                            return true
                        default:
                            return false
                    }
    }
    
    case success(data:PODataClass), failure(reasone:String), canceled, none
}



