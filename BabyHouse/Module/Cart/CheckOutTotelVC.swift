//
//  CheckOutTotelVC.swift
//  BabyHouse
//
//  Created by <PERSON> on 04/04/22.
//

import UIKit



class CheckOutTotelVC: UIViewController, UIViewControllerTransitioningDelegate {
   
  
    var checkoutPlease:((Bool)-> Void)?
        @IBOutlet weak var wrapLbl: UILabel!
        @IBOutlet weak var paymentView: UIView!
     
        @IBOutlet weak var summaryLbl: UILabel!
        @IBOutlet weak var totVal: UILabel!
        @IBOutlet weak var dcVal: UILabel!
        @IBOutlet weak var subVal: UILabel!
        @IBOutlet weak var totalLbl: UILabel!
        @IBOutlet weak var dcLbl: UILabel!
        @IBOutlet weak var subLbl: UILabel!
        @IBOutlet weak var checkoutView: UIView!
        
        @IBOutlet weak var checkoutLbl: UILabel!
        
        @IBOutlet weak var checkoutMain: UIView!
    
    var price = "0.0"
        override func viewDidLoad() {
            super.viewDidLoad()
            view.backgroundColor = UIColor.lightGray.withAlphaComponent(0.25)
            hideNavigationBar()
            
            subVal.text = price
            totVal.text = price
        }
        
        fileprivate func setFont() {
            summaryLbl.font = UIFont.with(size:16,.bold)
            checkoutLbl.font = UIFont.with(size:12,.bold)
            totVal.font = UIFont.with(size:15,.bold)
            dcVal.font = UIFont.with(size:12,.bold)
            subVal.font = UIFont.with(size:12,.bold)
            totalLbl.font = UIFont.with(size:15,.bold)
            dcLbl.font = UIFont.with(size:12,.bold)
            subLbl.font = UIFont.with(size:12,.bold)
            wrapLbl.font = UIFont.with(size:13,.bold)
            
        }
        override func viewDidLayoutSubviews() {
            checkoutView.cornerRadius = checkoutView.frame.height / 2
            checkoutMain.cornerRadius = checkoutMain.frame.height / 2
         //   self.checkoutView.gradient(colours:[buttong1,buttong2])
//            addmoreView.cornerRadius = addmoreView.frame.height / 2
//            addmoreView.layer.borderWidth = 0.8
//            addmoreView.layer.borderColor = saybrown.cgColor
          
           // addmoreView.setShadow()
            checkoutMain.setShadow()
            
            
//            self.view.gradient(colours: [saylightblue,UIColor.white,saylightblue])
            DispatchQueue.main.async {
                self.paymentView.cornerRadius = 20
                self.paymentView.setShadow(offset:.init(width: 0, height: -3))
                    
            }
            
        }

 
    @IBAction func checkOut(_ sender: Any) {
            
            
            dismiss(animated:true, completion:{
                DispatchQueue.main.async {
                    self.checkoutPlease?(true)
                }
               
            })
            
        }
    }
    extension CheckOutTotelVC:Storyboarded {
        static var storyboard: Storyboard { .cart }
    }
