//
//  AreaVC.swift
//  BabyHouse
//
//  Created by Notetech on 20/07/22.
//

import UIKit

class GovernorateVC: UIViewController {
    @IBOutlet var searchView: UIView!
    @IBOutlet var searchBar: UISearchBar!
    var governorate = [GovernorateModel]()
    var originalGovernorate = [GovernorateModel]()
    var currentSelectedGovernorate: GovernorateModel?
    var selectedArea: ((GovernorateModel) -> Void)?
    var viewModel: HomeVM?
    @IBOutlet var tableView: UITableView!
    override func viewDidLoad() {
        super.viewDidLoad()
        tableView.delegate = self
        tableView.dataSource = self
        tableView.register(AreaTableViewCell.self)
        tableView.contentInset = UIEdgeInsets(top: 15, left: 0, bottom: 60, right: 0)
        searchBarSetup()
        getData()
    }

    fileprivate func searchBarSetup() {
        searchBar.showsCancelButton = false
        searchBar.delegate = self
        searchView.cornerRadius = 10
        let searchTextField = searchBar.value(forKey: "searchField") as? UITextField
        searchTextField?.textColor = UIColor.black
        
        
        
        searchBar.placeholder =  LocalizationSystem.sharedInstance.localizedStringForKey(key: "Search Governorate", comment: "")
        if let textField = searchBar.value(forKey: "searchField") as? UITextField,
           let iconView = textField.leftView as? UIImageView
        {
            iconView.image = iconView.image?.withRenderingMode(UIImage.RenderingMode.alwaysTemplate)
            iconView.tintColor = UIColor.black
        }
        if #available(iOS 13.0, *) {
            self.searchBar.searchTextField.leftView?.tintColor = UIColor.black
        }
        
        for view in searchBar.subviews.last!.subviews {
            if type(of: view) == NSClassFromString("UISearchBarBackground") {
                view.alpha = 0.0
            }
        }
        if let textfield = searchBar.value(forKey: "searchField") as? UITextField {
            textfield.backgroundColor = UIColor.white
        }
    }
     
    func getData() {
        showActivity()
        viewModel?.getGovernorateList(completion: { response in
            DispatchQueue.main.async {
                hideActivity()
                guard let response = response else {
                    self.governorate = [GovernorateModel]()
                    self.tableView.reloadData()
                    self.tableView.backgroundText = LocalizationSystem.sharedInstance.localizedStringForKey(key: "no_area_text", comment: "")
                    return
                }
                 
                let governorateData = response.data
                 
                self.governorate = governorateData
                self.originalGovernorate = governorateData
                self.tableView.backgroundText = nil
                if self.governorate.count == 0 {
                    self.tableView.backgroundText = LocalizationSystem.sharedInstance.localizedStringForKey(key: "no_area_text", comment: "")
                }
                self.tableView.reloadData()
            }
        })
    }
}

extension GovernorateVC: UISearchBarDelegate {
    func searchBar(_ searchBar: UISearchBar, textDidChange searchText: String) {
        if searchText.isEmpty {
            searchBar.text = ""
            searchBar.resignFirstResponder()
            governorate = originalGovernorate
            // self.tableView.backgroundText = LocalizationSystem.sharedInstance.localizedStringForKey(key: "no_product_text", comment: "")
            tableView.reloadData()
            return
        }
        governorate = originalGovernorate.filter {
            $0.name.lowercased().contains(searchText.lowercased())
        }
        tableView.reloadData()
    }

    func searchBarCancelButtonClicked(_ searchBar: UISearchBar) {
        searchBar.resignFirstResponder()
        governorate = originalGovernorate
        // self.tableView.backgroundText = LocalizationSystem.sharedInstance.localizedStringForKey(key: "no_product_text", comment: "")
        tableView.reloadData()
    }
}

extension GovernorateVC: UITableViewDataSource, UITableViewDelegate {
    func tableView(_ tableView: UITableView, numberOfRowsInSection section: Int) -> Int {
        return governorate.count
    }

    func tableView(_ tableView: UITableView, cellForRowAt indexPath: IndexPath) -> UITableViewCell {
        let cell: AreaTableViewCell = tableView.dequeueReusableCell(for: indexPath)
        cell.tickLbl.isHidden = true
        if let current = currentSelectedGovernorate, governorate[indexPath.row].id == current.id {
            cell.tickLbl.isHidden = false
        }
       
        cell.areaLbl.text = governorate[indexPath.row].name
        
        return cell
    }

    func tableView(_ tableView: UITableView, didSelectRowAt indexPath: IndexPath) {
        selectedArea?(governorate[indexPath.row])
        dismiss(animated: true)
    }
}

extension GovernorateVC: Storyboarded {
    static var storyboard: Storyboard { .cart }
}
