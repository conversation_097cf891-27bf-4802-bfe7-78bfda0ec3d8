<?xml version="1.0" encoding="UTF-8"?>
<document type="com.apple.InterfaceBuilder3.CocoaTouch.XIB" version="3.0" toolsVersion="20037" targetRuntime="iOS.CocoaTouch" propertyAccessControl="none" useAutolayout="YES" useTraitCollections="YES" useSafeAreas="YES" colorMatched="YES">
    <device id="retina6_1" orientation="portrait" appearance="light"/>
    <dependencies>
        <deployment identifier="iOS"/>
        <plugIn identifier="com.apple.InterfaceBuilder.IBCocoaTouchPlugin" version="20020"/>
        <capability name="Safe area layout guides" minToolsVersion="9.0"/>
        <capability name="documents saved in the Xcode 8 format" minToolsVersion="8.0"/>
    </dependencies>
    <objects>
        <placeholder placeholderIdentifier="IBFilesOwner" id="-1" userLabel="File's Owner"/>
        <placeholder placeholderIdentifier="IBFirstResponder" id="-2" customClass="UIResponder"/>
        <tableViewCell contentMode="scaleToFill" selectionStyle="default" indentationWidth="10" id="KGk-i7-Jjw" customClass="AreaTableViewCell" customModule="BabyHouse" customModuleProvider="target">
            <rect key="frame" x="0.0" y="0.0" width="320" height="44"/>
            <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMaxY="YES"/>
            <tableViewCellContentView key="contentView" opaque="NO" clipsSubviews="YES" multipleTouchEnabled="YES" contentMode="center" tableViewCell="KGk-i7-Jjw" id="H2p-sc-9uM">
                <rect key="frame" x="0.0" y="0.0" width="320" height="44"/>
                <autoresizingMask key="autoresizingMask"/>
                <subviews>
                    <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Label" textAlignment="natural" lineBreakMode="tailTruncation" numberOfLines="0" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="4dK-8s-Uh2">
                        <rect key="frame" x="10" y="15" width="275" height="14"/>
                        <fontDescription key="fontDescription" type="system" pointSize="17"/>
                        <nil key="textColor"/>
                        <nil key="highlightedColor"/>
                    </label>
                    <imageView clipsSubviews="YES" userInteractionEnabled="NO" contentMode="scaleAspectFit" horizontalHuggingPriority="251" verticalHuggingPriority="251" image="radioBoxSelected" translatesAutoresizingMaskIntoConstraints="NO" id="mFY-bB-H9i">
                        <rect key="frame" x="295" y="14.5" width="15" height="15"/>
                        <constraints>
                            <constraint firstAttribute="height" constant="15" id="VEa-sy-A4o"/>
                            <constraint firstAttribute="width" constant="15" id="lFP-4L-HGD"/>
                        </constraints>
                    </imageView>
                </subviews>
                <constraints>
                    <constraint firstItem="4dK-8s-Uh2" firstAttribute="leading" secondItem="H2p-sc-9uM" secondAttribute="leading" constant="10" id="Cqr-4Z-ycq"/>
                    <constraint firstAttribute="bottom" secondItem="4dK-8s-Uh2" secondAttribute="bottom" constant="15" id="QIW-t4-zcJ"/>
                    <constraint firstItem="4dK-8s-Uh2" firstAttribute="top" secondItem="H2p-sc-9uM" secondAttribute="top" constant="15" id="Udr-dD-j4P"/>
                    <constraint firstItem="mFY-bB-H9i" firstAttribute="centerY" secondItem="H2p-sc-9uM" secondAttribute="centerY" id="Zca-R7-jeA"/>
                    <constraint firstAttribute="trailing" secondItem="mFY-bB-H9i" secondAttribute="trailing" constant="10" id="b2Q-vm-7Dr"/>
                    <constraint firstItem="mFY-bB-H9i" firstAttribute="leading" secondItem="4dK-8s-Uh2" secondAttribute="trailing" constant="10" id="hva-t1-Gbl"/>
                </constraints>
            </tableViewCellContentView>
            <viewLayoutGuide key="safeArea" id="njF-e1-oar"/>
            <connections>
                <outlet property="areaLbl" destination="4dK-8s-Uh2" id="ZYT-JV-FiD"/>
                <outlet property="tickLbl" destination="mFY-bB-H9i" id="AgK-mc-AJU"/>
            </connections>
            <point key="canvasLocation" x="132" y="113"/>
        </tableViewCell>
    </objects>
    <resources>
        <image name="radioBoxSelected" width="23" height="22"/>
    </resources>
</document>
