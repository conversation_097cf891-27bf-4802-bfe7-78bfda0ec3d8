//
//  TagCollectionViewCell.swift
//  BabyHouse
//
//  Created by Notetech on 19/07/22.
//

import UIKit

class TagCollectionViewCell: UICollectionViewCell {

    @IBOutlet weak var tagView: UIView!
    @IBOutlet weak var nameLbl: UILabel!
    @IBOutlet weak var mainView: UIView!
    override func awakeFromNib() {
        super.awakeFromNib()
        // Initialization code
    }
    override func layoutSubviews() {
        DispatchQueue.main.async {
            self.mainView.cornerRadius = self.mainView.frame.height / 2
        }
    }
}
extension  TagCollectionViewCell: NibLoadable, ReuseIdentifying {}
