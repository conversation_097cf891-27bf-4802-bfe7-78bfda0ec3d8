<?xml version="1.0" encoding="UTF-8"?>
<document type="com.apple.InterfaceBuilder3.CocoaTouch.XIB" version="3.0" toolsVersion="22155" targetRuntime="iOS.CocoaTouch" propertyAccessControl="none" useAutolayout="YES" useTraitCollections="YES" useSafeAreas="YES" colorMatched="YES">
    <device id="retina6_1" orientation="portrait" appearance="light"/>
    <dependencies>
        <plugIn identifier="com.apple.InterfaceBuilder.IBCocoaTouchPlugin" version="22131"/>
        <capability name="Safe area layout guides" minToolsVersion="9.0"/>
        <capability name="documents saved in the Xcode 8 format" minToolsVersion="8.0"/>
    </dependencies>
    <customFonts key="customFonts">
        <array key="Metropolis-Bold.otf">
            <string>Metropolis-Bold</string>
        </array>
    </customFonts>
    <objects>
        <placeholder placeholderIdentifier="IBFilesOwner" id="-1" userLabel="File's Owner"/>
        <placeholder placeholderIdentifier="IBFirstResponder" id="-2" customClass="UIResponder"/>
        <collectionViewCell opaque="NO" clipsSubviews="YES" multipleTouchEnabled="YES" contentMode="center" id="gTV-IL-0wX" customClass="TagCollectionViewCell" customModule="BabyHouse" customModuleProvider="target">
            <rect key="frame" x="0.0" y="0.0" width="85" height="40"/>
            <autoresizingMask key="autoresizingMask"/>
            <view key="contentView" opaque="NO" clipsSubviews="YES" multipleTouchEnabled="YES" contentMode="center">
                <rect key="frame" x="0.0" y="0.0" width="85" height="40"/>
                <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMaxY="YES"/>
                <subviews>
                    <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="4Uo-fa-lMr">
                        <rect key="frame" x="0.0" y="0.0" width="85" height="40"/>
                        <subviews>
                            <stackView opaque="NO" contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="j0G-SX-zps">
                                <rect key="frame" x="10" y="10" width="65" height="20"/>
                                <subviews>
                                    <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Home" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" minimumScaleFactor="0.75" translatesAutoresizingMaskIntoConstraints="NO" id="WX1-Se-8cD">
                                        <rect key="frame" x="0.0" y="0.0" width="40" height="20"/>
                                        <fontDescription key="fontDescription" name="Metropolis-Bold" family="Metropolis" pointSize="11"/>
                                        <color key="textColor" red="1" green="1" blue="1" alpha="1" colorSpace="calibratedRGB"/>
                                        <nil key="highlightedColor"/>
                                    </label>
                                    <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="hPE-5e-Cf1">
                                        <rect key="frame" x="40" y="0.0" width="25" height="20"/>
                                        <subviews>
                                            <imageView clipsSubviews="YES" userInteractionEnabled="NO" contentMode="scaleAspectFit" horizontalHuggingPriority="251" verticalHuggingPriority="251" image="mark" translatesAutoresizingMaskIntoConstraints="NO" id="7l9-Wi-EvM">
                                                <rect key="frame" x="7" y="2.5" width="15" height="15"/>
                                                <constraints>
                                                    <constraint firstAttribute="height" constant="15" id="0uH-l4-xlu"/>
                                                    <constraint firstAttribute="width" constant="15" id="Gne-Au-pBJ"/>
                                                </constraints>
                                            </imageView>
                                        </subviews>
                                        <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                        <constraints>
                                            <constraint firstAttribute="width" constant="25" id="DuR-8Q-HVG"/>
                                            <constraint firstAttribute="trailing" secondItem="7l9-Wi-EvM" secondAttribute="trailing" constant="3" id="RCh-m8-XRM"/>
                                            <constraint firstItem="7l9-Wi-EvM" firstAttribute="centerY" secondItem="hPE-5e-Cf1" secondAttribute="centerY" id="ZYq-bY-IHM"/>
                                        </constraints>
                                    </view>
                                </subviews>
                            </stackView>
                        </subviews>
                        <color key="backgroundColor" red="0.89803921568627454" green="0.37254901960784315" blue="0.4823529411764706" alpha="1" colorSpace="calibratedRGB"/>
                        <constraints>
                            <constraint firstAttribute="bottom" secondItem="j0G-SX-zps" secondAttribute="bottom" constant="10" id="2AV-do-5kc"/>
                            <constraint firstAttribute="trailing" secondItem="j0G-SX-zps" secondAttribute="trailing" constant="10" id="7W4-RS-kcU"/>
                            <constraint firstItem="j0G-SX-zps" firstAttribute="top" secondItem="4Uo-fa-lMr" secondAttribute="top" constant="10" id="I8O-2C-aRH"/>
                            <constraint firstItem="j0G-SX-zps" firstAttribute="leading" secondItem="4Uo-fa-lMr" secondAttribute="leading" constant="10" id="QfT-OQ-RoP"/>
                        </constraints>
                    </view>
                </subviews>
            </view>
            <viewLayoutGuide key="safeArea" id="ZTg-uK-7eu"/>
            <constraints>
                <constraint firstItem="4Uo-fa-lMr" firstAttribute="top" secondItem="gTV-IL-0wX" secondAttribute="top" id="A47-Za-pBh"/>
                <constraint firstAttribute="bottom" secondItem="4Uo-fa-lMr" secondAttribute="bottom" id="B0Y-cC-JF8"/>
                <constraint firstAttribute="trailing" secondItem="4Uo-fa-lMr" secondAttribute="trailing" id="D33-XW-JgO"/>
                <constraint firstItem="4Uo-fa-lMr" firstAttribute="leading" secondItem="gTV-IL-0wX" secondAttribute="leading" id="K7X-gp-ax6"/>
            </constraints>
            <size key="customSize" width="92" height="48"/>
            <connections>
                <outlet property="mainView" destination="4Uo-fa-lMr" id="VaM-St-Vy8"/>
                <outlet property="nameLbl" destination="WX1-Se-8cD" id="LLI-gz-F5c"/>
                <outlet property="tagView" destination="hPE-5e-Cf1" id="x3T-lh-Zhm"/>
            </connections>
            <point key="canvasLocation" x="161.59420289855075" y="102.45535714285714"/>
        </collectionViewCell>
    </objects>
    <resources>
        <image name="mark" width="15" height="15"/>
    </resources>
</document>
