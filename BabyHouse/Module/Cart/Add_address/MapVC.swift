//
//  MapVC.swift
//  Saydality
//
//  Created by <PERSON> on 09/03/22.
//

import UIKit
import MapKit

class MapVC: UIViewController  {
    var selectedLocation:((Citydata)->Void)?
   var isFromaddress = true
    @IBOutlet weak var mapView: MKMapView!
    @IBOutlet weak var btnTitle: UIButton!
    @IBOutlet weak var addressView: UIView!
    @IBOutlet weak var delieveryAddress: UILabel!
    @IBOutlet weak var deliveryTitle: UILabel!
    @IBOutlet weak var searchBar: UISearchBar!
    var locationManager = CLLocationManager()
    var curretLoc:Citydata?
    override func viewDidLoad() {
        super.viewDidLoad()
        locationManager.delegate = self
        locationManager.desiredAccuracy = kCLLocationAccuracyBest
        locationManager.requestWhenInUseAuthorization()
        locationManager.requestLocation()
        mapView.mapType = .standard
        // Do any additional setup after loading the view.
        deliveryTitle.font = UIFont.with(size: 12, .bold)
        delieveryAddress.font = UIFont.with(size: 10, .regular)
        btnTitle.titleLabel?.font = UIFont.with(size: 14, .bold)
        locationManager.requestWhenInUseAuthorization()
        isLocationAccessEnabled()
        addressView.isHidden = true
        mapView.showsUserLocation = true
        let gestureRecognizer = UITapGestureRecognizer(target: self, action: #selector(didTap))
        mapView.addGestureRecognizer(gestureRecognizer)
        mapView.isUserInteractionEnabled = true
        searchbarsetup()
        deliveryTitle.text = LocalizationSystem.sharedInstance.localizedStringForKey(key: "Delivery Location", comment: "")
        btnTitle.setTitle(LocalizationSystem.sharedInstance.localizedStringForKey(key: "Confirm Pin location", comment: ""), for: .normal)
    }
    @objc func didTap(gestureReconizer: UITapGestureRecognizer) {
        let touchPoint = gestureReconizer.location(in: mapView)
        let touchMapCoordinate = mapView.convert(touchPoint, toCoordinateFrom:mapView)

        print("latitude: \(touchMapCoordinate.latitude), longitude: \(touchMapCoordinate.longitude)")
        self.fetchCityAndCountry(from:CLLocation(latitude:touchMapCoordinate.latitude ,longitude:touchMapCoordinate.longitude), completion: { city, error in
            guard let city = city, error == nil else { return }
            print(city)
            self.addressView.isHidden = false
            self.curretLoc = Citydata(current:city.current,lat:"\(touchMapCoordinate.latitude)",long:"\(touchMapCoordinate.longitude)",adrress_id:city.adrress_id,name:city.name,type:city.type,area_id:
                                city.area_id,block:city.block,street:city.street,avenue:city.avenue,house_no:city.house_no,email:city.email,phone:city.phone)
            guard let c = self.curretLoc else {
                 return
            }
            self.delieveryAddress.text = c.name + " ," +  c.adrress_id 
            self.setAnnotation(lat:touchMapCoordinate.latitude, longi: touchMapCoordinate.longitude, name: c.current)
            
           })
    }
    fileprivate func searchbarsetup() {
        self.searchBar.delegate = self
        let searchTextField = self.searchBar.value(forKey: "searchField") as? UITextField
        searchTextField?.textColor = Babyred
        self.searchBar.placeholder = "Search here"
        if let textField = self.searchBar.value(forKey: "searchField") as? UITextField,
           let iconView = textField.leftView as? UIImageView {
            
            iconView.image = iconView.image?.withRenderingMode(UIImage.RenderingMode.alwaysTemplate)
            iconView.tintColor = Babyrose
        }
        if #available(iOS 13.0, *) {
            self.searchBar.searchTextField.leftView?.tintColor = Babyred
        }
        
        for view in self.searchBar.subviews.last!.subviews {
            if type(of: view) == NSClassFromString("UISearchBarBackground"){
                view.alpha = 0.0
            }
        }
        if let textfield = self.searchBar.value(forKey: "searchField") as? UITextField {
            textfield.backgroundColor = UIColor.lightGray
        }
    }
    override func viewDidLayoutSubviews() {
        DispatchQueue.main.async {
            self.addressView.round(corners: [.topLeft,.topRight], radius: 10)
            
        }
    }
    
    @IBAction func confirmPinlocation(_ sender: Any) {
        guard let c = self.curretLoc else { return }
        selectedLocation?(c)
        self.navigationController?.popViewController(animated:true)
        if isFromaddress {
            self.dismiss(animated:true, completion: nil)
            
        }
        
        
    }
    @IBAction func back(_ sender: Any) {
        if isFromaddress {
            self.dismiss(animated:true, completion: nil)
            return
        }
        self.navigationController?.popViewController(animated:true)
    }
    func setAnnotation(lat:CLLocationDegrees,longi:CLLocationDegrees,name:String) {
            mapView.removeAnnotations(mapView.annotations)
           
            let london = MKPointAnnotation()
            london.title = name
            london.coordinate = CLLocationCoordinate2D(latitude:lat, longitude:longi)
            mapView.addAnnotation(london)
        
        
    }

func isLocationAccessEnabled() {
    if CLLocationManager.locationServicesEnabled() {
               switch CLLocationManager.authorizationStatus() {
               case .notDetermined:
                locationManager.delegate = self
                locationManager.desiredAccuracy = kCLLocationAccuracyNearestTenMeters
                locationManager.startUpdatingLocation()
                
               case .restricted, .denied:
//                UserDefaults.standard.set(nil, forKey:"Cityinfo")
//                UserDefaults.standard.synchronize()
                locationAlert()
                   break

               case .authorizedWhenInUse, .authorizedAlways:
                   // Enable features that require location services here.
                   print("Full Access")
//                UserDefaults.standard.set(nil, forKey:"Cityinfo")
//                UserDefaults.standard.synchronize()
                locationManager.delegate = self
                locationManager.desiredAccuracy = kCLLocationAccuracyBest
                locationManager.distanceFilter = 200
                locationManager.startUpdatingLocation()
                   break
               }
           }
    
  
}
    func locationAlert() {
        
        let alertController = UIAlertController (title: "Location", message: "GPS access is restricted. In order to use tracking, please enable GPS in the Settigs app under Privacy, Location", preferredStyle: .alert)

            let settingsAction = UIAlertAction(title: "Go to Settings now", style: .default) { (_) -> Void in

                guard let settingsUrl = URL(string: UIApplication.openSettingsURLString) else {
                    return
                }

                if UIApplication.shared.canOpenURL(settingsUrl) {
                    UIApplication.shared.open(settingsUrl, completionHandler: { (success) in
                        print("Settings opened: \(success)") // Prints true
                    })
                }
            }
            alertController.addAction(settingsAction)
            let cancelAction = UIAlertAction(title: "Cancel", style: .default, handler: nil)
            alertController.addAction(cancelAction)

            present(alertController, animated: true, completion: nil)
       
    }
    func fetchCityAndCountry(from location: CLLocation, completion: @escaping (_ citydata:Citydata?, _ error: Error?) -> ()) {
        CLGeocoder().reverseGeocodeLocation(location) { placemarks, error in
            // address dictionary properties
            let adminstrativearea = placemarks?.first?.administrativeArea ?? ""
            let locality = placemarks?.first?.locality ?? ""
            var addressId = ""
            if locality.isEmpty {
                addressId = adminstrativearea
            }
            else {
                addressId = locality + ", " + adminstrativearea
                }
            
           let city = Citydata(current:locality , lat: "", long: "", adrress_id:addressId, name: placemarks?.first?.name ?? "", type: "", area_id: "", block: placemarks?.first?.subLocality ?? "", street: placemarks?.first?.thoroughfare ?? "", avenue:placemarks?.first?.subThoroughfare ?? "", house_no: "", email: "", phone: "")
            
            completion(city,error)
        }
    }
    
    
}
extension MapVC:UISearchBarDelegate {
    func searchBarSearchButtonClicked(_ searchBar: UISearchBar) {
        let geocoder = CLGeocoder()
        geocoder.geocodeAddressString(searchBar.text ?? "") { (placemarks, error) in

            if let center = (placemarks?.first?.region as? CLCircularRegion)?.center {

                let region = MKCoordinateRegion(center: center, span: MKCoordinateSpan(latitudeDelta: 0.02, longitudeDelta: 0.02))
                self.mapView.setRegion(region, animated: true)
                self.view.endEditing(true)
            }
        }
    }
   
    func searchBarCancelButtonClicked(_ searchBar: UISearchBar) {
        
        self.view.endEditing(true)
    }
    
}
extension MapVC: CLLocationManagerDelegate {
    
    func locationManager(_ manager: CLLocationManager, didUpdateLocations locations: [CLLocation]) {
        guard let locValue: CLLocationCoordinate2D = manager.location?.coordinate else { return }
        print("locations = \(locValue.latitude) \(locValue.longitude)")
        if let location = locations.first {
            let span =   MKCoordinateSpan(latitudeDelta: 0.05, longitudeDelta: 0.05)
                let region = MKCoordinateRegion(center: location.coordinate, span: span)
                mapView.setRegion(region, animated: true)
            DispatchQueue.main.async {
            let latitudeText:String = "\(locValue.latitude)"
            let longitudeText:String = "\(locValue.longitude)"
            self.fetchCityAndCountry(from:CLLocation(latitude:locValue.latitude ,longitude:locValue.longitude), completion: { city, error in
                guard let city = city, error == nil else { return }
                self.addressView.isHidden = false
                self.curretLoc = Citydata(current:city.current,lat:latitudeText,long:longitudeText,adrress_id:city.adrress_id ,name:city.name,type:city.type,area_id: city.area_id,block:city.block,street:city.street,avenue:city.avenue,house_no:city.house_no,email:city.email,phone:city.phone)
                guard let c = self.curretLoc else {
                     return
                }
                self.delieveryAddress.text =  c.name  + " ," +  c.adrress_id
                self.locationManager.delegate = nil
                self.locationManager.stopUpdatingLocation()
                
               })
            }
            }
    }
    func locationManager(_ manager: CLLocationManager, didFailWithError error: Error) {
             print("error:: \(error.localizedDescription)")
        }

}

extension MapVC:Storyboarded {
    static var storyboard: Storyboard { .cart }
}
