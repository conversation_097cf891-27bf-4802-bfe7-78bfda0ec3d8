//
//  AddAddressVC.swift
//  BabyHouse
//
//  Created by <PERSON> on 03/04/22.
//

import MapKit
import SkyFloatingLabelTextField
import UIKit
class AddAddressVC: UIViewController {
    // fields
    var isFromCheckout = false
    var viewModel1: EditProfileViewModel?
    var viewModel: HomeVM?
    var currentSelectedArea: DeliveryArea?
    var currentSelectedGovernorate: GovernorateModel?
    var latitudeText = ""
    var longitudeText = ""
    var addressId = ""
    var addressAdded: ((Bool, AddaddressData) -> Void)?
    var addressSelected: ((Address) -> Void)?
    @IBOutlet var officeTxt: SkyFloatingLabelTextField!
    @IBOutlet var apartmentTxt: SkyFloatingLabelTextField!
    @IBOutlet var housenoTxt: SkyFloatingLabelTextField!
    @IBOutlet var avanueTxt: SkyFloatingLabelTextField!
    @IBOutlet var floorTxt: SkyFloatingLabelTextField!
    @IBOutlet var blockTxt: SkyFloatingLabelTextField!
    @IBOutlet var streetTxt: SkyFloatingLabelTextField!
    @IBOutlet var areaTxt: SkyFloatingLabelTextField!
    @IBOutlet var governorateTxt: SkyFloatingLabelTextField!
    @IBOutlet var addressTxt: SkyFloatingLabelTextField!
    @IBOutlet var alternateMobtxt: SkyFloatingLabelTextField!
    @IBOutlet var mobTxt: SkyFloatingLabelTextField!
    @IBOutlet var nameTxt: SkyFloatingLabelTextField!
    @IBOutlet var emailTxt: SkyFloatingLabelTextField!
    // fields
    @IBOutlet var activityIndicator: UIActivityIndicatorView!
    @IBOutlet var tagTitle: UILabel!
    var selectedTag = 0
    @IBOutlet var tagCollectionview: UICollectionView!
    @IBOutlet var addAddressview: UIView!
    @IBOutlet var usecurentLocation: UIView!
    @IBOutlet var choosecurrentlocationLbl: UILabel!
    @IBOutlet var useCurrentlocation: UILabel!
    @IBOutlet var choosemapView: UIView!
    @IBOutlet var choosemapTitle: UILabel!
    @IBOutlet var delivertodifferentlocation: UILabel!
    @IBOutlet var newaddressTitle: UILabel!
    @IBOutlet var addressTitleView: UIView!
    @IBOutlet var useMyCurrentLocation: UIView!
    @IBOutlet var delivertoDfrentLocationView: UIView!
    @IBOutlet var chooseAvatharView: UIView!
    let locationManager = CLLocationManager()
    let tags = ["Home", "Flat/Apartment", "Work"]
    var addressType = ""
    var editAddresse: AddressData?

    override func viewDidLoad() {
        super.viewDidLoad()
        hideNavigationBar()
        setFont()
        setBorderAndUI()
        tagCollectionview.register(TagCollectionViewCell.self)
        tagCollectionview.delegate = self
        tagCollectionview.dataSource = self
        activityIndicator.isHidden = true
        selectHome()
        areaTxt.setupRightImage(imageName: "down-arrow-2")
        governorateTxt.setupRightImage(imageName: "down-arrow-2")

        if isLoggedIn() {
            getData()
            nameTxt.isUserInteractionEnabled = false
            emailTxt.isUserInteractionEnabled = false
            nameTxt.isEnabled = false
            emailTxt.isEnabled = false
        } else {
            nameTxt.isUserInteractionEnabled = true
            emailTxt.isUserInteractionEnabled = true
            nameTxt.isEnabled = true
            emailTxt.isEnabled = true
        }

        if let edit = editAddresse, let id = edit.id {
            alternateMobtxt.text = edit.alternate_mobile
            addressTxt.text = edit.address
            areaTxt.text = edit.area_name

            currentSelectedArea = DeliveryArea(id: edit.area_id ?? "", name: edit.area_name ?? "", deliveryCharges: "", minimumFreeDeliveryOrderAmount: "")

            let governorateName = edit.governorate ?? ""
            currentSelectedGovernorate = GovernorateModel(id: edit.governorate_id ?? "", name: governorateName, arName: "")
            governorateTxt.text = governorateName

            blockTxt.text = edit.block
            streetTxt.text = edit.street
            avanueTxt.text = edit.avenue
            housenoTxt.text = edit.house_no
            officeTxt.text = edit.office
            floorTxt.text = edit.floor
            apartmentTxt.text = edit.apartment
            latitudeText = edit.latitude ?? ""
            longitudeText = edit.longitude ?? ""
            addressId = id
            selectedTag = getAddressType(type: edit.type?.lowercased() ?? "")
            tagCollectionview.reloadData()
        }
        guard let _ = UserData.getuserInfo() else { return }
        // nameTxt.text = data.
    }

    func getAddressType(type: String) -> Int {
        switch type {
        case "flat/apartment":
            selectApartment()
            return 1
        case "work":
            selectWork()
            return 2
        default:
            selectHome()
            return 0
        }
    }

    fileprivate func selectHome() {
        addressType = tags.first!
        floorTxt.isHidden = false
        apartmentTxt.isHidden = false
        housenoTxt.isHidden = false
        officeTxt.isHidden = true
    }

    fileprivate func selectApartment() {
        addressType = tags[1]
        floorTxt.isHidden = false
        apartmentTxt.isHidden = false
        housenoTxt.isHidden = false
        officeTxt.isHidden = true
    }

    fileprivate func selectWork() {
        addressType = tags.last!
        floorTxt.isHidden = false
        apartmentTxt.isHidden = false
        housenoTxt.isHidden = false
        officeTxt.isHidden = false
    }

    @IBAction func fetchCurrentlocation(_ sender: Any) {
        activityIndicator.isHidden = false
        choosecurrentlocationLbl.text = "Detecting location..."
        locationManager.requestWhenInUseAuthorization()
        isLocationAccessEnabled()
    }

    fileprivate func getData() {
        showActivity()
        viewModel1?.getProfileData(completion: { data in
            hideActivity()

            DispatchQueue.main.async {
                hideActivity()
                guard let response = data else {
                    let msg = LocalizationSystem.sharedInstance.localizedStringForKey(key: "http_error_text", comment: "")
                    self.view.makeToast(msg)
                    self.dismiss(animated: true)
                    return
                }
                let name = (response.data?.firstName ?? "") + " " + (response.data?.lastName ?? "")
                self.nameTxt.text = name
                self.emailTxt.text = response.data?.email
                self.mobTxt.text = response.data?.mobile
            }
        })
    }

    fileprivate func addAction(addaddressData: AddaddressData) {
        showActivity()
        viewModel?.addAddress(data: addaddressData, completion: { data in
            hideActivity()

            DispatchQueue.main.async {
                hideActivity()
                guard let response = data else {
                    let msg = LocalizationSystem.sharedInstance.localizedStringForKey(key: "http_error_text", comment: "")
                    self.view.makeToast(msg)
                    return
                }
                if response.status == 200, response.error == false {
//                    if self.isFromCheckout {
//                        guard let data = data ,let address = data.data else {
//                            return
//                        }
//                        self.addressSelected?(address)
//                        self.dismiss(animated:true)
//                        return
//                    }
                    self.view.makeToast(response.messages)
                    self.addressAdded?(true, addaddressData)
                    self.dismiss(animated: true)
                    return
                }
                self.view.makeToast(response.messages)
            }
        })
    }

    @IBAction func fromMap(_ sender: Any) {
        let vc = MapVC.instantiate()
        vc.selectedLocation = { current in

            DispatchQueue.main.async {
                self.choosemapTitle.text = current.adrress_id + ", " + current.street
                self.latitudeText = current.lat
                self.longitudeText = current.long
            }
        }
        vc.isFromaddress = false
        navigationController?.pushViewController(vc, animated: true)
    }

    func locationAlert() {
        let alertController = UIAlertController(title: "Location", message: "GPS access is restricted. In order to use tracking, please enable GPS in the Settigs app under Privacy, Location", preferredStyle: .alert)

        let settingsAction = UIAlertAction(title: "Go to Settings now", style: .default) { _ in

            guard let settingsUrl = URL(string: UIApplication.openSettingsURLString) else {
                return
            }

            if UIApplication.shared.canOpenURL(settingsUrl) {
                UIApplication.shared.open(settingsUrl, completionHandler: { success in
                    print("Settings opened: \(success)") // Prints true
                })
            }
        }
        alertController.addAction(settingsAction)
        let cancelAction = UIAlertAction(title: "Cancel", style: .default, handler: nil)
        alertController.addAction(cancelAction)

        present(alertController, animated: true, completion: nil)
    }

    func isLocationAccessEnabled() {
        if CLLocationManager.locationServicesEnabled() {
            switch CLLocationManager.authorizationStatus() {
            case .notDetermined:
                locationManager.delegate = self
                locationManager.desiredAccuracy = kCLLocationAccuracyNearestTenMeters
                locationManager.startUpdatingLocation()

            case .restricted, .denied:

                locationAlert()

            case .authorizedWhenInUse, .authorizedAlways:

                locationManager.delegate = self
                locationManager.desiredAccuracy = kCLLocationAccuracyBest
                locationManager.distanceFilter = 200
                locationManager.startUpdatingLocation()
            }
        }
    }

    @IBAction func selectGovernate(_ sender: Any?) {
        let vc = GovernorateVC.instantiate()
        vc.currentSelectedGovernorate = currentSelectedGovernorate
        vc.viewModel = HomeVM(with: 0)
        vc.selectedArea = { selected in
            DispatchQueue.main.async {
                self.areaTxt.text = nil
                self.currentSelectedArea = nil

                self.currentSelectedGovernorate = selected
                self.governorateTxt.text = selected.name
            }
        }
        present(vc, animated: true)
    }

    @IBAction func selectArea(_ sender: Any) {
        let vc = AreaVC.instantiate()
        vc.currentSelectedArea = currentSelectedArea
        vc.governorateID = currentSelectedGovernorate?.id ?? ""
        vc.viewModel = HomeVM(with: 0)
        vc.selectedArea = { selected in
            DispatchQueue.main.async {
                self.currentSelectedArea = selected
                self.areaTxt.text = selected.name
            }
        }
        present(vc, animated: true)
    }

    @IBAction func closeAction(_ sender: Any) {
        dismiss(animated: true)
    }

    @IBAction func addAddressaction(_ sender: Any) {
        guard
            let name = nameTxt.text, !name.isEmpty,
//            let email = emailTxt.text, !email.isEmpty,
            let selectedArea = currentSelectedArea,
            let selectedGovernorate = currentSelectedGovernorate,
            let block = blockTxt.text, !block.isEmpty,
            let houseNo = housenoTxt.text, !houseNo.isEmpty,
            let apartment = apartmentTxt.text, !apartment.isEmpty,
            let floor = floorTxt.text, !floor.isEmpty,
            let street = streetTxt.text, !street.isEmpty,
            let mobile = mobTxt.text, !mobile.isEmpty
        else {
            Utilities.showToast("Please fill all mandatory(*) fields")
            return
        }
        let address = AddaddressData(address_id: addressId, name: name, email:  emailTxt.text ?? "", mobile: mobile, type: addressType, alternate_mobile: alternateMobtxt.text!, address: addressTxt.text!, street: street, block: block, apartment: apartment, avenue: avanueTxt.text!, house_no: houseNo, country: "Kuwait", floor: floor, office: officeTxt.text!, area: areaTxt.text!, area_id: selectedArea.id, governorate: governorateTxt.text!, governorate_id: selectedGovernorate.id, latitude: latitudeText, longitude: longitudeText, is_default: "1")

        if isLoggedIn() {
            addAction(addaddressData: address)
        } else {
            addressAdded?(true, address)
            dismiss(animated: true)
        }
    }

    fileprivate func setFont() {
        newaddressTitle.font = UIFont.with(size: 15, .bold)
        delivertodifferentlocation.font = UIFont.with(size: 13, .medium)
        choosemapTitle.font = UIFont.with(size: 11, .regular)
        useCurrentlocation.font = UIFont.with(size: 13, .medium)
        choosecurrentlocationLbl.font = UIFont.with(size: 11, .regular)
        tagTitle.font = UIFont.with(size: 11, .medium)
    }

    override func viewDidLayoutSubviews() {
        DispatchQueue.main.async {
            self.choosemapView.cornerRadius = self.choosemapView.frame.height / 2
            self.usecurentLocation.cornerRadius = self.usecurentLocation.frame.height / 2
            self.addAddressview.cornerRadius = self.addAddressview.frame.height / 2
            // self.addAddressview.cornerRadius  = self.addAddressview.frame.height / 2
            self.addAddressview.gradient(colours: [G1, G2, G3])
            // self.addAddressview.setShadow()
        }
    }

    func setBorderAndUI() {
        addressTitleView.layer.borderWidth = 1
        addressTitleView.layer.borderColor = UIColor.lightGray.cgColor
        addressTitleView.layer.cornerRadius = 20

        useMyCurrentLocation.layer.borderWidth = 1
        useMyCurrentLocation.layer.borderColor = UIColor.lightGray.cgColor
        useMyCurrentLocation.layer.cornerRadius = 20

        delivertoDfrentLocationView.layer.borderWidth = 1
        delivertoDfrentLocationView.layer.borderColor = UIColor.lightGray.cgColor
        delivertoDfrentLocationView.layer.cornerRadius = 20

        chooseAvatharView.layer.borderWidth = 1
        chooseAvatharView.layer.borderColor = UIColor.lightGray.cgColor
        chooseAvatharView.layer.cornerRadius = 20
    }
}

extension AddAddressVC: UICollectionViewDelegate, UICollectionViewDataSource {
    func collectionView(_ collectionView: UICollectionView, numberOfItemsInSection section: Int) -> Int {
        return tags.count
    }

    func collectionView(_ collectionView: UICollectionView, cellForItemAt indexPath: IndexPath) -> UICollectionViewCell {
        let cell: TagCollectionViewCell = collectionView.dequeueReusableCell(for: indexPath)
        cell.nameLbl.text = tags[indexPath.row]
        if selectedTag == indexPath.row {
            cell.nameLbl.textColor = .white
            cell.mainView.backgroundColor = G2
            cell.tagView.isHidden = false
            cell.mainView.layer.borderWidth = 0.0
        } else {
            cell.mainView.backgroundColor = .clear
            cell.tagView.isHidden = true
            cell.nameLbl.textColor = G2
            cell.mainView.layer.borderColor = G2.cgColor
            cell.mainView.layer.borderWidth = 0.8
        }
        return cell
    }

    func collectionView(_ collectionView: UICollectionView, didSelectItemAt indexPath: IndexPath) {
        selectedTag = indexPath.row
        switch selectedTag {
        case 0: selectHome()
        case 1: selectApartment()
        case 2: selectWork()
        default: print("default")
        }
        collectionView.reloadData()
    }
}

extension AddAddressVC: UICollectionViewDelegateFlowLayout {
    func collectionView(_ collectionView: UICollectionView, layout collectionViewLayout: UICollectionViewLayout, minimumLineSpacingForSectionAt section: Int) -> CGFloat {
        return 10.0
    }

    func collectionView(_ collectionView: UICollectionView, layout collectionViewLayout: UICollectionViewLayout, minimumInteritemSpacingForSectionAt section: Int) -> CGFloat {
        return 10.0
    }

    func collectionView(_ collectionView: UICollectionView, layout collectionViewLayout: UICollectionViewLayout, sizeForItemAt indexPath: IndexPath) -> CGSize {
        if selectedTag == indexPath.row {
            let name = tags[indexPath.row]
            let width = name.width(withConstrainedHeight: collectionView.frame.height, font: UIFont.with(size: 11, .regular)) + 40
            return CGSize(width: width, height: 30)
        }
        let name = tags[indexPath.row]
        let width = name.width(withConstrainedHeight: collectionView.frame.height, font: UIFont.with(size: 11, .regular)) + 20
        return CGSize(width: width, height: 30)
    }
}

extension AddAddressVC: CLLocationManagerDelegate {
    func fetchCityAndCountry(from location: CLLocation, completion: @escaping (_ citydata: Citydata?, _ error: Error?) -> Void) {
        CLGeocoder().reverseGeocodeLocation(location) { placemarks, error in
            // address dictionary properties
//            print(placemarks)
//            print(placemarks?.first?.administrativeArea)
//            print(placemarks?.first?.name)
//            print(placemarks?.first?.location)
//            print(placemarks?.first?.locality)
//            print(placemarks?.first?.subLocality)
//            print(placemarks?.first?.subThoroughfare)
//            print(placemarks?.first?.subAdministrativeArea)
//            print(placemarks?.first?.region)
//            print(placemarks?.first?.subAdministrativeArea)
            let adminstrativearea = placemarks?.first?.administrativeArea ?? ""
            let locality = placemarks?.first?.locality ?? ""
            let addressId = locality + ", " + adminstrativearea

            let city = Citydata(current: placemarks?.first?.locality ?? "", lat: "", long: "", adrress_id: addressId, name: placemarks?.first?.name ?? "", type: "1", area_id: "", block: placemarks?.first?.subLocality ?? "", street: placemarks?.first?.thoroughfare ?? "", avenue: placemarks?.first?.subThoroughfare ?? "", house_no: "", email: "", phone: "")

            completion(city, error)
        }
    }

    func locationManager(_ manager: CLLocationManager, didUpdateLocations locations: [CLLocation]) {
        DispatchQueue.main.async {
            guard let locValue: CLLocationCoordinate2D = manager.location?.coordinate else { return }
            print("location")

            self.latitudeText = "\(locValue.latitude)"
            self.longitudeText = "\(locValue.longitude)"
            self.locationManager.delegate = nil
            self.locationManager.stopUpdatingLocation()

            self.fetchCityAndCountry(from: CLLocation(latitude: locValue.latitude, longitude: locValue.longitude), completion: { city, error in
                DispatchQueue.main.async {
                    guard let city = city, error == nil else { return }
                    self.activityIndicator.isHidden = true
                    print(city)
                    self.choosecurrentlocationLbl.text = city.adrress_id + ", " + city.street
                }

            })
        }
    }
}

extension AddAddressVC: Storyboarded {
    static var storyboard: Storyboard { .cart }
}

struct Citydata: Codable {
    let current: String
    let lat: String
    let long: String
    let adrress_id: String
    let name: String
    let type: String
    let area_id: String
    let block: String
    let street: String
    let avenue: String
    let house_no: String
    let email: String
    let phone: String
}

extension Citydata {
    static func getcityInfo() -> Citydata? {
        if let data = UserDefaults.standard.value(forKey: "Cityinfo") as? Data {
            let cityinfo = try? PropertyListDecoder().decode(Citydata.self, from: data)
            return cityinfo
        }
        return nil
    }
}
