//
//  AreaVC.swift
//  BabyHouse
//
//  Created by Notetech on 20/07/22.
//

import UIKit

class AreaVC: UIViewController {
    @IBOutlet weak var searchView: UIView!
    @IBOutlet weak var searchBar: UISearchBar!
    var areas = [DeliveryArea]()
    var originalAreas = [DeliveryArea]()
    var currentSelectedArea:DeliveryArea?
    var selectedArea:((DeliveryArea)->Void)?
    var viewModel: HomeVM?
    var governorateID:String = ""
    @IBOutlet weak var tableView: UITableView!
    override func viewDidLoad() {
        super.viewDidLoad()
        tableView.delegate = self
        tableView.dataSource = self
        tableView.register(AreaTableViewCell.self)
        self.tableView.contentInset = UIEdgeInsets(top: 15, left: 0, bottom: 60, right: 0)
        searchbarsetup() 
        getData()
     }
    fileprivate func searchbarsetup() {
        self.searchBar.showsCancelButton = false
        self.searchBar.delegate = self
        searchView.cornerRadius = 10
        let searchTextField = self.searchBar.value(forKey: "searchField") as? UITextField
        searchTextField?.textColor = UIColor.black
        self.searchBar.placeholder = LocalizationSystem.sharedInstance.localizedStringForKey(key:"search_area_text", comment: "")
        if let textField = self.searchBar.value(forKey: "searchField") as? UITextField,
           let iconView = textField.leftView as? UIImageView {
            
            iconView.image = iconView.image?.withRenderingMode(UIImage.RenderingMode.alwaysTemplate)
            iconView.tintColor = UIColor.black
        }
        if #available(iOS 13.0, *) {
            self.searchBar.searchTextField.leftView?.tintColor = UIColor.black
        }
        
        for view in self.searchBar.subviews.last!.subviews {
            if type(of: view) == NSClassFromString("UISearchBarBackground"){
                view.alpha = 0.0
            }
        }
        if let textfield = self.searchBar.value(forKey: "searchField") as? UITextField {
            textfield.backgroundColor = UIColor.white
        }
    }
     
     func getData()  {
         showActivity()
         viewModel?.deliveryArea(governorateID: governorateID, completion: { data in
             DispatchQueue.main.async {
                 hideActivity()
                 guard let res = data,let areasData = res.data  else  {
                     self.areas = [DeliveryArea]()
                     self.tableView.reloadData()
//                     self.tableView.backgroundText = LocalizationSystem.sharedInstance.localizedStringForKey(key: "no_area_text", comment: "")
                     self.tableView.backgroundText = LocalizationSystem.sharedInstance.localizedStringForKey(key: "Please select governorate first", comment: "")
//
                     return
                 }

                 self.areas = areasData
                 self.originalAreas = areasData
                 self.tableView.backgroundText = nil
                 if self.areas.count == 0 {
                     self.tableView.backgroundText = LocalizationSystem.sharedInstance.localizedStringForKey(key: "Please select governorate first", comment: "")
                 }
                 self.tableView.reloadData()
             }
         })
     }


}
extension AreaVC:UISearchBarDelegate {
    func searchBar(_ searchBar: UISearchBar, textDidChange searchText: String) {
      
            if searchText.isEmpty {
                searchBar.text = ""
                searchBar.resignFirstResponder()
                self.areas = self.originalAreas
                //self.tableView.backgroundText = LocalizationSystem.sharedInstance.localizedStringForKey(key: "no_product_text", comment: "")
                self.tableView.reloadData()
                return
            }
            self.areas = self.originalAreas.filter {
                $0.name.lowercased().contains(searchText.lowercased())
            }
        self.tableView.reloadData()
          
           
        
    }
    func searchBarCancelButtonClicked(_ searchBar: UISearchBar) {
        
        
        
        searchBar.resignFirstResponder()
        self.areas = originalAreas
       // self.tableView.backgroundText = LocalizationSystem.sharedInstance.localizedStringForKey(key: "no_product_text", comment: "")
        tableView.reloadData()
    }
    
}
extension AreaVC:UITableViewDataSource,UITableViewDelegate {
    func tableView(_ tableView: UITableView, numberOfRowsInSection section: Int) -> Int {
        return areas.count
    }

    func tableView(_ tableView: UITableView, cellForRowAt indexPath: IndexPath) -> UITableViewCell {
        let cell:AreaTableViewCell = tableView.dequeueReusableCell(for: indexPath)
        cell.tickLbl.isHidden = true
        if let current = currentSelectedArea,areas[indexPath.row].id == current.id  {
            cell.tickLbl.isHidden = false
        }
       
            cell.areaLbl.text = areas[indexPath.row].name
        
        return cell
    }
    func tableView(_ tableView: UITableView, didSelectRowAt indexPath: IndexPath) {
        selectedArea?(areas[indexPath.row])
        dismiss(animated:true)
    }

}
extension AreaVC:Storyboarded {
    static var storyboard: Storyboard { .cart }
}
