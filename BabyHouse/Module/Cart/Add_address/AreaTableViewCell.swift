//
//  AreaTableViewCell.swift
//  BabyHouse
//
//  Created by Notetech on 20/07/22.
//

import UIKit

class AreaTableViewCell: UITableViewCell {

    @IBOutlet weak var tickLbl: UIImageView!
    @IBOutlet weak var areaLbl: UILabel!
    override func awakeFromNib() {
        super.awakeFromNib()
        areaLbl.font = UIFont.with(size:12, .regular)
    }

    override func setSelected(_ selected: <PERSON><PERSON>, animated: <PERSON><PERSON>) {
        super.setSelected(selected, animated: animated)

        // Configure the view for the selected state
    }
    
}
extension  AreaTableViewCell: NibLoadable, ReuseIdentifying {}
