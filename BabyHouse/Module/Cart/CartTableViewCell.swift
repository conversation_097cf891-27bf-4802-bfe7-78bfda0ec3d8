//
//  CartTableViewCell.swift
//  Saydality
//
//  Created by <PERSON><PERSON> on 11/12/21.
//

import UIKit
enum Cartstatus {
    case minus,plus,delete
}

class CartTableViewCell: UITableViewCell {
    var cartAction:((Int,Cartstatus)->Void)?
    @IBOutlet weak var plusBtn: UIButton!
    @IBOutlet weak var minusBtn: UIButton!
    @IBOutlet weak var imageProf: UIImageView!
    @IBOutlet weak var soldOutView:UIView!
  
    @IBOutlet weak var orderView: UIView!
   
    @IBOutlet weak var countLbl: UILabel!
  
    @IBOutlet weak var paymentView: UIView!
    @IBOutlet weak var priceLbl: UILabel!
   
    @IBOutlet weak var soldby: UILabel!
 
    @IBOutlet weak var nameLbl: UILabel!
    @IBOutlet weak var itemView: UIView!
  
    
    
    @IBOutlet weak var stockValue:UILabel!
    @IBOutlet weak var stockLbl:UILabel!
    
    
    @IBOutlet weak var skuLbl: UILabel!
    @IBOutlet weak var skuValueLbl: UILabel!
    
    @IBOutlet weak var colurAmtLbl: UILabel!
    @IBOutlet weak var colourLbl: UILabel!
    
    @IBOutlet weak var sizeAmtLbl: UILabel!
    @IBOutlet weak var sizeLbl: UILabel!
    
    @IBOutlet weak var deleteBtn: UIButton!
    var items:Items? {
        didSet {
            self.nameLbl.text = items?.name ?? ""
            let url = items?.image ?? ""
            self.imgView.sd_setImage(with:URL(string:url), placeholderImage: UIImage(named: ""))
            self.priceLbl.text = items?.price ?? "0.00"
            self.countLbl.text = items?.qty ?? "0"
            self.sizeLbl.text = items?.attributeName
            self.sizeAmtLbl.text = items?.attributeValue
            self.skuValueLbl.text = items?.sku
            self.stockValue.text = items?.productStock
            
            
            let inventoryStock:Int = items?.productStock.toInt ?? 0
            let hideSoldOutView:Bool = inventoryStock > 0
            self.soldOutView.isHidden = hideSoldOutView
//            self.colurAmtLbl.text =  ""
        }
    }
    
    @IBOutlet weak var imgView: UIImageView!
    override func awakeFromNib() {
        super.awakeFromNib()
        // Initialization code
        
       
        selectionStyle = .none
        sizeLbl.font = UIFont.with(size:10, .bold)
        sizeAmtLbl.font = UIFont.with(size:10, .bold)
        
        skuLbl.font = UIFont.with(size:10, .bold)
        skuValueLbl.font = UIFont.with(size:10, .bold)
        
        stockLbl.font = UIFont.with(size:10, .bold)
        stockValue.font = UIFont.with(size:10, .bold)
        
        colourLbl.font = UIFont.with(size:10, .regular)
        colurAmtLbl.font = UIFont.with(size:10, .regular)
        countLbl.font = UIFont.with(size:18,.regular)
        plusBtn.titleLabel?.font = UIFont.with(size:18, .regular)
        minusBtn.titleLabel?.font = UIFont.with(size:18,.regular)
     
        paymentView.cornerRadius = paymentView.frame.height / 2
        //paymentView.addShadow()
        priceLbl.font = UIFont.with(size:12, .bold)
      
        paymentView.layer.borderColor = Babyred.cgColor
        paymentView.layer.borderWidth = 0.8
     
        nameLbl.font = UIFont.with(size:14, .bold)
        itemView.addShadow()
    }
    
//    func setData(cartData:CDItem) {
//        nameLbl.text = cartData.name
//        categoryLbl.text = cartData.type
//        soldbyname.text = cartData.pharmacyName
//        priceLbl.text = "KD \(cartData.salePrice)"
//        countLbl.text = cartData.qty
//
//        let mainURl = App.Environment.imagePath + "products/"
//        let url = mainURl + cartData.image
//        self.imageProf.sd_setImage(with:URL(string:url), placeholderImage: UIImage(named: "dbg"))
//
//
//    }

    @IBAction func deleteAction(_ sender: UIButton) {
        let index = sender.tag
        cartAction?(index,.delete)
        
    }
    @IBAction func plusAction(_ sender: UIButton) {
        let index = sender.tag
        cartAction?(index,.plus)
    }
    @IBAction func minusAction(_ sender:UIButton) {
        let index = sender.tag
        cartAction?(index,.minus)
    }
    override func setSelected(_ selected: Bool, animated: Bool) {
        super.setSelected(selected, animated: animated)

        // Configure the view for the selected state
    }
    
}
extension CartTableViewCell: NibLoadable, ReuseIdentifying {}
