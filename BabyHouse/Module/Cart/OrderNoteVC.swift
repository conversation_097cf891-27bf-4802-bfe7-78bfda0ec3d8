//
//  OrderNoteVC.swift
//  BabyHouse
//
//  Created by <PERSON> on 01/04/22.
//

import UIKit

class OrderNoteVC: UIViewController {
    let max = 250
    var currentNote:((String)->Void)?
    @IBOutlet weak var wrapLbl: UILabel!
    @IBOutlet weak var giftzView: UIView!
    @IBOutlet weak var confirmView: UIView!
    @IBOutlet weak var noteText: UITextView!
    @IBOutlet weak var confirmLbl: UILabel!
    @IBOutlet weak var charecterdRemainiung: UILabel!
    @IBOutlet weak var yourMsgLbl: UILabel!
    @IBOutlet weak var orederTitle: UILabel!
        override func viewDidLoad() {
            super.viewDidLoad()

            // Do any additional setup after loading the view.
            setFont()
            setLocalisation()
            noteText.delegate = self
           // confirmView.gradient(colours: [buttong1,buttong2])
            self.view.backgroundColor = UIColor.gray.withAlphaComponent(0.4)
            noteText.layer.borderColor = UIColor.darkGray.cgColor
            noteText.layer.borderWidth = 0.8
        }
        
    @IBAction func closeBtn(_ sender: Any) {
        self.dismiss(animated: true)
    }
    
    @IBAction func confermBtn(_ sender: Any) {
        currentNote?(noteText.text)
        self.dismiss(animated: true)
    }
    
        func setLocalisation()  {
            self.confirmLbl.text = LocalizationSystem.sharedInstance.localizedStringForKey(key: "confirm_txt", comment: "")
            self.orederTitle.text = LocalizationSystem.sharedInstance.localizedStringForKey(key: "order_note_text", comment: "")
            self.yourMsgLbl.text = LocalizationSystem.sharedInstance.localizedStringForKey(key: "your_message_txt", comment: "")
            self.charecterdRemainiung.text = "\(max)" + " " + LocalizationSystem.sharedInstance.localizedStringForKey(key: "character_remaning_txt", comment: "")
        }
        fileprivate func setFont() {
            self.noteText.font = UIFont.with(size:14,.regular)
            self.charecterdRemainiung.font = UIFont.with(size:11,.bold)
            self.yourMsgLbl.font = UIFont.with(size:13,.bold)
            self.orederTitle.font = UIFont.with(size:13,.bold)
            self.confirmLbl.font = UIFont.with(size:15,.bold)
            
        }
        @IBAction func closeTap(_ sender: Any) {
        }
        
    }
extension OrderNoteVC: UITextViewDelegate {

    func textViewDidChange(_ textView: UITextView) {
        self.charecterdRemainiung.text  = "\(max - textView.text.count)" + " " +  LocalizationSystem.sharedInstance.localizedStringForKey(key: "character_remaning_txt", comment: "")
    }


    func textView(_ textView: UITextView, shouldChangeTextIn range: NSRange, replacementText text: String) -> Bool {
        return textView.text.count + (text.count - range.length) <= max
    }

}
    extension OrderNoteVC:Storyboarded {
        static var storyboard: Storyboard { .cart }
    }

