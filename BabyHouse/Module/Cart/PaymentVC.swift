//
//  PaymentVC.swift
//  BabyHouse
//
//  Created by <PERSON> on 04/04/22.
//

import UIKit

class PaymentVC: UIViewController {
    @IBOutlet weak var giftWrapSelectImg: UIImageView!
    @IBOutlet weak var giftzView: UIView!
    @IBOutlet weak var shipaddressView: UIView!
    @IBOutlet weak var summuryLbl: UILabel!
    @IBOutlet weak var totalLbl: UILabel!
    @IBOutlet weak var payView: UIView!
    @IBOutlet weak var itemsLbl: UILabel!
    @IBOutlet weak var paynowLbl: UILabel!
    @IBOutlet weak var applyBtn: UIButton!
    @IBOutlet weak var codLbl: UILabel!
    @IBOutlet weak var visaLbl: UILabel!
    @IBOutlet weak var knetlbl: UILabel!
    @IBOutlet weak var paymentmethod: UILabel!
    @IBOutlet weak var expressdeliveryLbl: UILabel!
    @IBOutlet weak var normalLbl: UILabel!
    @IBOutlet weak var addview: UIView!
    @IBOutlet weak var scheduleView: UIView!
    @IBOutlet weak var addaddressLbl: UILabel!
    @IBOutlet weak var shipAddress: UILabel!
    @IBOutlet weak var shipAddressheader: UILabel!
    @IBOutlet weak var deliveryDate: UILabel!
    @IBOutlet weak var deliveryDateheader: UILabel!
    @IBOutlet weak var deliveryHeader: UILabel!
    @IBOutlet weak var promoTxt: UITextField!
    @IBOutlet weak var cashOnDeliverySelect: UIImageView!
    @IBOutlet weak var knetSelect: UIImageView!
    var viewModel: HomeVM?
    @IBOutlet weak var priceLbl: UILabel!
    @IBOutlet weak var sheduleLbl: UILabel!
    @IBOutlet weak var addAddressLbl: UILabel!
    @IBOutlet weak var payNowContainerView: UIView!
    @IBOutlet weak var shipingAddressView: UIView!
    @IBOutlet weak var deliveryDateView: UIView!
    @IBOutlet weak var topZView: UIView!
    @IBOutlet weak var top2View: UIView!
    @IBOutlet weak var headerLbl: UILabel!
    @IBOutlet weak var promoCodeView: UIView!
    
    @IBOutlet weak var orderTXt: UILabel!
    var backtoHome:((Bool)-> Void)?
    var giftWrap = 0
    var orderNotes = ""
    var price = ""
    var paymentType = "COD"
    var addressId = ""
    var itemCount = ""
    override func viewDidLoad() {
        super.viewDidLoad()

        // Do any additional setup after loading the view.
       
        shipingAddressView.setShadow()
        deliveryDateView.setShadow()
//        payNowContainerView.setShadow()
        setFont()
        setLOcalization()
        priceLbl.text = price
        promoTxt.attributedPlaceholder = NSAttributedString(string:NSLocalizedString(LocalizationSystem.sharedInstance.localizedStringForKey(key: "promocod_msg", comment: ""), comment: "Input Group Name"), attributes: [NSAttributedString.Key.foregroundColor: UIColor.lightGray.withAlphaComponent(0.5)])
        shipaddressView.isHidden = true
        if let userinfo = UserData.getuserInfo() {
            shipaddressView.isHidden = false
        }
        itemsLbl.text = itemCount + " " + LocalizationSystem.sharedInstance.localizedStringForKey(key:  "items_text", comment: "")
        
        
        
        self.orderTXt.text = orderNotes
    }
    override func viewDidLayoutSubviews() {
        DispatchQueue.main.async {
            self.top2View.round(corners:[.bottomLeft,.bottomRight], radius: 20)
            self.topZView.round(corners:[.bottomLeft,.bottomRight], radius: 20)
            self.topZView.gradient(colours:[Babyrose,Babyred])
            self.topZView.setShadow()
            self.addview.cornerRadius = 10
            self.scheduleView.cornerRadius = 10
            self.applyBtn.cornerRadius = 10
            self.scheduleView.gradient(colours:[G1,G3])
            self.addview.gradient(colours:[G1,G3])
            self.applyBtn.gradient(colours:[G1,G3])
           // self.cartView.cornerRadius = self.cartView.frame.height / 2
            //self.notiView.cornerRadius = self.notiView.frame.height / 2
            self.payView.gradient(colours:[G1,G2,G3])
            self.payView.cornerRadius =  self.payView.frame.height / 2
            self.promoCodeView.layer.borderWidth = 0.8
            self.promoCodeView.layer.borderColor = Babyred.cgColor
            self.promoCodeView.cornerRadius =  self.promoCodeView.frame.height / 2
            
//            self.payNowContainerView.setShadow(offset:.init(width: 0, height: -5))
            self.giftzView.layer.borderWidth = 0.8
            self.giftzView.layer.borderColor = Babyred.cgColor
            self.giftzView.cornerRadius = 20
        }
        
    }
    func setLOcalization() {
        headerLbl.text =  LocalizationSystem.sharedInstance.localizedStringForKey(key:"check_out", comment: "")
        deliveryHeader.text =  LocalizationSystem.sharedInstance.localizedStringForKey(key:"delivery_text", comment: "")
        deliveryDate.text =  LocalizationSystem.sharedInstance.localizedStringForKey(key:"delivery_date_text", comment: "")
        sheduleLbl.text =  LocalizationSystem.sharedInstance.localizedStringForKey(key:"schedule_text", comment: "")
        shipAddressheader.text =  LocalizationSystem.sharedInstance.localizedStringForKey(key:"Shipaddress_text", comment: "")
        shipAddress.text =  LocalizationSystem.sharedInstance.localizedStringForKey(key: "shipaddressmsg_text", comment: "")
        addaddressLbl.text =  LocalizationSystem.sharedInstance.localizedStringForKey(key: "Add_address_text", comment: "")
//        expressdeliveryLbl.text =  LocalizationSystem.sharedInstance.localizedStringForKey(key: "expres_delivery_text", comment: "")
//        normalLbl.text =  LocalizationSystem.sharedInstance.localizedStringForKey(key: "normal_delivery_text", comment: "")
     
        paymentmethod.text =  LocalizationSystem.sharedInstance.localizedStringForKey(key: "payment_method_text", comment: "")
         codLbl.text =  LocalizationSystem.sharedInstance.localizedStringForKey(key: "cod", comment: "")
        visaLbl.text =  LocalizationSystem.sharedInstance.localizedStringForKey(key: "visa_card_msg", comment: "")
        knetlbl.text =  LocalizationSystem.sharedInstance.localizedStringForKey(key: "knet_msg", comment: "")
        promoTxt.placeholder =  LocalizationSystem.sharedInstance.localizedStringForKey(key: "promocod_msg", comment: "")
        applyBtn.setTitle(LocalizationSystem.sharedInstance.localizedStringForKey(key:  "apply_text", comment: ""), for: .normal)
        summuryLbl.text =  LocalizationSystem.sharedInstance.localizedStringForKey(key: "payment_summary_text", comment: "")
        paynowLbl.text =  LocalizationSystem.sharedInstance.localizedStringForKey(key: "paynow_text", comment: "")
        totalLbl.text  = LocalizationSystem.sharedInstance.localizedStringForKey(key:  "bagtotal_text", comment: "")


    }
    func setFont() {
        self.headerLbl.font = UIFont.with(size:14,.bold)
        self.sheduleLbl.font = UIFont.with(size:12,.regular)
        self.addAddressLbl.font = UIFont.with(size:12,.regular)
        self.deliveryHeader.font = UIFont.with(size:14,.bold)
        deliveryDate.font = UIFont.with(size:11,.regular)
        deliveryDateheader.font = UIFont.with(size:13,.regular)
        shipAddressheader.font = UIFont.with(size:13,.regular)
        shipAddress.font = UIFont.with(size:11,.regular)
//        expressdeliveryLbl.font = UIFont.with(size:12,.regular)
//        normalLbl.font = UIFont.with(size:12,.regular)
        paymentmethod.font = UIFont.with(size:14,.bold)
        codLbl.font = UIFont.with(size:12,.regular)
        visaLbl.font = UIFont.with(size:12,.regular)
        knetlbl.font = UIFont.with(size:12,.regular)
        promoTxt.font = UIFont.with(size:12,.regular)
        summuryLbl.font = UIFont.with(size:14,.bold)
        paynowLbl.font = UIFont.with(size:13,.regular)
        totalLbl.font = UIFont.with(size:12,.medium)
        itemsLbl.font = UIFont.with(size:10,.regular)
        orderTXt.font = UIFont.with(size:12,.regular)
    }

    @IBAction func giftWrapAction(_ sender: Any) {
        
        if giftWrap == 0 {
            giftWrap = 1
            giftWrapSelectImg.image = UIImage(named: "roundRed")
            
           
        }else{
            giftWrap = 0
            giftWrapSelectImg.image = UIImage(named: "roundGray")
        }
    }
    @IBAction func orderNote(_ sender: Any) {
        let view = OrderNoteVC.instantiate()
        view.modalPresentationStyle = .overCurrentContext
        view.currentNote = { (note) in
            self.orderTXt.text = note
            self.orderNotes = note
        }
        present(view, animated:true, completion:nil)
    }
    @IBAction func back(_ sender: Any) {
        self.navigationController?.popViewController(animated:true)
    }
    @IBAction func shippingAddressSelect(_ sender: Any) {
        
        let vc = AddressVC.instantiate()
        vc.viewModel = HomeVM(with:0)
        vc.isFromCheckout = true
        vc.addressSelected = { (address) in
            guard let id =  address.id else {
                return
            }
            self.addressId = id
            let addrress = address.address ?? ""
            if !addrress.isEmpty {
                self.shipAddress.text =  address.address
                return
            }
            let area = address.area_name ?? ""
            let country = address.country_name ?? ""
            self.shipAddress.text = area + " ," + country
            
        }
        vc.viewModel = HomeVM(with:0)
        let nvc = UINavigationController(rootViewController:vc)
        self.present(nvc, animated: true, completion: nil)
    
        
    }
    @IBAction func deliveryDateSelect(_ sender: Any) {}
    
    
    @IBAction func knetSelect(_ sender: Any) {
        cashOnDeliverySelect.image = UIImage(named: "roundGray")
        knetSelect.image = UIImage(named: "roundRed")
        paymentType = "KNET"
    }
    
    @IBAction func cashOnDelivery(_ sender: Any) {
        cashOnDeliverySelect.image = UIImage(named: "roundRed")
        knetSelect.image = UIImage(named: "roundGray")
        paymentType = "COD"
    }
    
    @IBAction func applyCoupen(_ sender: Any) {
        showActivity()
        viewModel?.applyPromotions(data: promoCodeApply(promo_code: promoTxt.text!), completion: { data,error  in
            hideActivity()
            if data?.error == false {
                self.priceLbl.text = "KD \(data?.data?.finalTotal ?? "0.0")"
            }
        })
    }
    @IBAction func payNowAction(_ sender: Any) {
        if addressId.isEmpty {
           let msg =  LocalizationSystem.sharedInstance.localizedStringForKey(key:   "address_select_txt", comment: "")
            self.view.makeToast(msg)
            return
        }
        showActivity()
//        let data = placeOrderData(promo_code: promoTxt.text!, payment_method: paymentType, address_id: addressId, giftwrap: giftWrap, order_notes:orderNotes, is_loyalty_checked: 0)
        
//        viewModel?.payFinalCheckout(data: data, completion: { data in
//            hideActivity()
//            DispatchQueue.main.async {
//                guard let data = data ,data.status == 200,data.error == false else {
//                    
//                    self.view.makeToast(data?.messages)
//                    return }
//                if data.data?.orderItemData.count ?? 0 > 0 {
//                    let vc = PaymentsuccessVC.instantiate()
//                    vc.orderid = String(data.data?.orderItemData[0].orderID ?? 0)
//                    vc.amout = data.data?.orderItemData[0].price ?? ""
//                    self.navigationController?.pushViewController(vc, animated: true)
//                }
//            }
//          
//           
//            
//            
//        })
        
    }
}

extension PaymentVC:Storyboarded {
    static var storyboard: Storyboard { .cart }
}
