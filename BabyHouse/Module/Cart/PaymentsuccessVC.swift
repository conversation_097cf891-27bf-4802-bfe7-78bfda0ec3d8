//
//  PaymentsuccessVC.swift
//  BabyHouse
//
//  Created by Notetech on 09/08/22.
//

import UIKit

class PaymentsuccessVC: UIViewController {
    var amout = ""
    var orderid = ""
    var failureReason: String? = nil
    var isGuestCheckout: Bool = false
    @IBOutlet var trackLbl: UILabel!
    @IBOutlet var idLbl: UILabel!
    @IBOutlet var amountLbl: UILabel!
    @IBOutlet var idHeader: UILabel!
    @IBOutlet var paidHeader: UILabel!
    @IBOutlet var msgLbl: UILabel!
    @IBOutlet var headerLbl: UILabel!

    @IBOutlet var trackView: UIView!

    override func viewDidLoad() {
        super.viewDidLoad()
        self.setFont()

        if let failureReason = failureReason {
            self.paidHeader.isHidden = true
            self.idHeader.isHidden = true
            self.amountLbl.isHidden = true
            self.idLbl.isHidden = true
            self.trackLbl.text = "Ok"
            self.msgLbl.text = "Your Payment has been Failed. \(failureReason)"
            self.headerLbl.text = "Payment Failed!"
            self.headerLbl.textColor = UIColor(red: 255, green: 0, blue: 53, alpha: 1.0)
        }
        else {
            
            if self.isGuestCheckout {
                self.trackLbl.text = "Home"
            }
            
            self.amountLbl.text = self.amout
            self.idLbl.text = self.orderid

            NotificationCenter.callCartSummeryApi()
        }
    }

    override func viewDidLayoutSubviews() {
        DispatchQueue.main.async {
            self.trackView.gradient(colours: [G1, G2, G3])
            self.trackView.cornerRadius = self.trackView.frame.height / 2
        }
    }

    func setFont() {
        self.headerLbl.font = UIFont.with(size: 15, .bold)
        self.msgLbl.font = UIFont.with(size: 12, .regular)
        self.paidHeader.font = UIFont.with(size: 12, .medium)
        self.trackLbl.font = UIFont.with(size: 15, .bold)
        self.idHeader.font = UIFont.with(size: 12, .medium)
        self.amountLbl.font = UIFont.with(size: 12, .regular)
        self.idLbl.font = UIFont.with(size: 12, .regular)

        self.headerLbl.text = LocalizationSystem.sharedInstance.localizedStringForKey(key: "payment_success", comment: "")
        self.msgLbl.text = LocalizationSystem.sharedInstance.localizedStringForKey(key: "payment_success_msg", comment: "")
        self.paidHeader.text = LocalizationSystem.sharedInstance.localizedStringForKey(key: "amount_paid", comment: "")
        self.idHeader.text = LocalizationSystem.sharedInstance.localizedStringForKey(key: "order_id", comment: "")
    }

    @IBAction func trackBtn(_ sender: Any) {
        self.navigationController?.popToRootViewController(animated: true)

        if self.failureReason == nil && !self.isGuestCheckout {
            NotificationCenter.default.post(name: .trackOrder, object: self.orderid, userInfo: nil)
        }
        if self.failureReason == nil && self.isGuestCheckout {
            let vc = HomecontainerVC.instantiate()
            self.navigationController?.setViewControllers([vc], animated: true)
        }
    }
}

extension PaymentsuccessVC: Storyboarded {
    static var storyboard: Storyboard { .cart }
}
