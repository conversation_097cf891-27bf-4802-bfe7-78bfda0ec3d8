//
//  SignupVM.swift
//  BabyHouse
//
//  Created by <PERSON> on 10/04/22.
//

import Foundation

class SignupVM: BaseViewModel {
    /// DoctorId
    var dataSource: Int?

    required init(with dataSource: Int?) {
        self.dataSource = dataSource
    }

    func regestration(data: registerData, completion: ((RegistrationResponse?) -> Void)?) {
        guard let tokenData = GuestData.getUserInfo() else {
            print("❌ No guest token found for user registration")
            return
        }

        let token = "Bearer " + tokenData.accessToken

        print("\n🚀 INITIATING USER REGISTRATION")
        print("Email: \(data.email)")
        print("First Name: \(data.first_name)")
        print("Last Name: \(data.last_name)")
        print("Phone: \(data.phone)")
        print("Guest Token: \(tokenData.accessToken)")
        print("Authorization Header: \(token)")

        HomeRequest.register(data, token).response { (result: Result<RegistrationResponse, NError>)
            in

                switch result {
                case let .success(response):
                    print("\n✅ USER REGISTRATION RESPONSE")
                    print("Status: \(response.status)")
                    print("Error: \(response.error)")
                    print("Message: \(response.messages)")

                    if let userData = response.data {
                        print("🆔 REGISTRATION DATA:")
                        print("User ID: \(userData.userID)")
                    } else {
                        print("⚠️ No user data in registration response")
                    }

                    completion?(response)

                case let .failure(error):
                    print("\n❌ USER REGISTRATION FAILED")
                    print("Error: \(error.localizedDescription)")

                    DispatchQueue.main.async {
                        // hideActivity()
                    }
                    completion?(nil)
                }
        }
    }

    func login(data: LoginData, completion: ((LoginResponse?) -> Void)?) {
        guard let tokenData = GuestData.getUserInfo() else {
            print("❌ No guest token found for user login (SignupVM)")
            return
        }

        var token = "Bearer " + tokenData.accessToken

        print("\n🚀 INITIATING USER LOGIN (from SignupVM)")
        print("Username: \(data.user_name)")
        print("Login Type: \(data.type)")
        print("Guest Token: \(tokenData.accessToken)")
        print("Authorization Header: \(token)")

        HomeRequest.login(data, token).response { (result: Result<LoginResponse, NError>)
            in

                switch result {
                case let .success(response):
                    print("\n✅ USER LOGIN RESPONSE (SignupVM)")
                    print("Status: \(response.status)")
                    print("Error: \(response.error)")
                    print("Message: \(response.messages)")

                    if let userData = response.data {
                        print("🔑 USER AUTHENTICATION TOKENS:")
                        print("Access Token: \(userData.accessToken)")
                        print("User ID: \(userData.userid)")
                        print("Token Type: \(userData.tokenType)")
                        print("Bearer Token for API calls: Bearer \(userData.accessToken)")

                        // Save user token
                        UserDefaults.standard.set(try? PropertyListEncoder().encode(userData), forKey: "Userinfo")
                        UserDefaults.standard.synchronize()
                        print("✅ User token saved to UserDefaults")
                    } else {
                        print("⚠️ No user data in response")
                    }

                    completion?(response)

                case let .failure(error):
                    print("\n❌ USER LOGIN FAILED (SignupVM)")
                    print("Error: \(error.localizedDescription)")

                    DispatchQueue.main.async {
                        // hideActivity()
                    }
                    completion?(nil)
                }
        }
    }
}

struct RegistrationResponse: Codable {
    let status: Int
    let error: Bool
    let messages: String
    let data: RegistrationData?
}

// MARK: - DataClass

struct RegistrationData: Codable {
    let userID: Int
    enum CodingKeys: String, CodingKey {
        case userID = "user_id"
    }
}
