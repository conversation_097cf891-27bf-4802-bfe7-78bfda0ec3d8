//
//  LoginViewMode.swift
//  BabyHouse
//
//  Created by <PERSON> on 09/04/22.
//

import Foundation

struct SendEmailOtpParams: Encodable {
    let email: String
}

struct VerifyEmailOtpParams: Encodable {
    let email, otp: String
}

struct ResetPasswordParams: Encodable {
    let email, new_password, confirm_password: String
}

// MARK: - SendOtpResponse
struct SendOtpResponse: Codable {
    let status: Int
    let error: Bool
    let messages: String
}

class LoginViewMode: BaseViewModel {
    /// DoctorId
    var dataSource: Int?

    required init(with dataSource: Int?) {
        self.dataSource = dataSource
    }

    func onSendOtpToMail(params: SendEmailOtpParams, completion: ((SendOtpResponse?, String?) -> Void)?) {
        print("line executed")
        guard let tokenData = GuestData.getUserInfo() else {
            return
        }
        let token = "Bearer " + tokenData.accessToken
        
        print("line executed")
        HomeRequest.sendEmailOtp(params, token).response { (result: Result<SendOtpResponse, NError>)
            in

                switch result {
                case let .success(response):
                    completion?(response, nil)

                case let .failure(error):
                    DispatchQueue.main.async {
                        // hideActivity()
                    }
                    completion?(nil, error.localizedDescription)
                    print(error.localizedDescription)
                }
        }
        
    }
    
    func onVerifyEmailOtp(params: VerifyEmailOtpParams, completion: ((SendOtpResponse?, String?) -> Void)?) {
        guard let tokenData = GuestData.getUserInfo() else { return }
        let token = "Bearer " + tokenData.accessToken
        HomeRequest.verifyEmailOtp(params, token).response { (result: Result<SendOtpResponse, NError>)
            in

                switch result {
                case let .success(response):
                    completion?(response, nil)

                case let .failure(error):
                    DispatchQueue.main.async {
                        // hideActivity()
                    }
                    completion?(nil, error.localizedDescription)
                    print(error.localizedDescription)
                }
        }
        
    }
    
    func onResetPassword(params: ResetPasswordParams, completion: ((SendOtpResponse?, String?) -> Void)?) {
        guard let tokenData = GuestData.getUserInfo() else { return }
        let token = "Bearer " + tokenData.accessToken
        HomeRequest.resetPassword(params, token).response { (result: Result<SendOtpResponse, NError>)
            in

                switch result {
                case let .success(response):
                    completion?(response, nil)

                case let .failure(error):
                    DispatchQueue.main.async {
                        // hideActivity()
                    }
                    completion?(nil, error.localizedDescription)
                    print(error.localizedDescription)
                }
        }
        
    }

    func login(data: LoginData, completion: ((LoginResponse?) -> Void)?) {
        guard let tokenData = GuestData.getUserInfo() else {
            print("❌ No guest token found for user login")
            return
        }

        let token = "Bearer " + tokenData.accessToken

        print("\n🚀 INITIATING USER LOGIN")
        print("Username: \(data.user_name)")
        print("Login Type: \(data.type)")
        print("Guest Token: \(tokenData.accessToken)")
        print("Authorization Header: \(token)")

        HomeRequest.login(data, token).response { (result: Result<LoginResponse, NError>)
            in

                switch result {
                case let .success(response):
                    print("\n✅ USER LOGIN RESPONSE")
                    print("Status: \(response.status)")
                    print("Error: \(response.error)")
                    print("Message: \(response.messages)")

                    if let userData = response.data {
                        print("🔑 USER AUTHENTICATION TOKENS:")
                        print("Access Token: \(userData.accessToken)")
                        print("User ID: \(userData.userid)")
                        print("Token Type: \(userData.tokenType)")
                        print("Bearer Token for API calls: Bearer \(userData.accessToken)")

                        // Save user token
                        UserDefaults.standard.set(try? PropertyListEncoder().encode(userData), forKey: "Userinfo")
                        UserDefaults.standard.synchronize()
                        print("✅ User token saved to UserDefaults")
                    } else {
                        print("⚠️ No user data in response")
                    }

                    completion?(response)

                case let .failure(error):
                    print("\n❌ USER LOGIN FAILED")
                    print("Error: \(error.localizedDescription)")

                    DispatchQueue.main.async {
                        // hideActivity()
                    }
                    completion?(nil)
                }
        }
    }
}

struct FcmTokenParameter: Encodable {
    let fcm_token, country_code: String
}

struct LoginData: Encodable {
    var user_name: String
    var password, unique_id, name: String?
    var type: Int
}

enum LoginType: Int {
    case server = 1, google = 2, apple = 3
}

struct registerData: Encodable {
    let email, password, confirm_password, first_name, last_name, phone: String
   
}

struct logoutData: Encodable {
    var user_name: String
    var password: String
}

// struct loginSuccess: Codable {
//    let status: Int
//    let error: Bool
//    let message: String
//    let data: UserData
// }
//
//// MARK: - DataClass
// struct UserData: Codable {
//    let accessToken, userid, tokenType: String
//
//    enum CodingKeys: String, CodingKey {
//        case accessToken = "access_token"
//        case userid
//        case tokenType = "token_type"
//    }
// }
