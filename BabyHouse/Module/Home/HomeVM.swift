//
//  HomeVM.swift
//  Saydality
//
//  Created by <PERSON><PERSON> on 24/01/22.
//

import Foundation

class HomeVM: BaseViewModel {
    /// DoctorId
    var dataSource: Int?

    required init(with dataSource: Int?) {
        self.dataSource = dataSource
    }

    func postEnquiry(model: SendEnquiryModel, completion: ((Bool, String) -> Void)?) {
        guard let tokenData = GuestData.getUserInfo() else { return }
        var token = "Bearer " + tokenData.accessToken
        if let tokenValue = UserData.getuserInfo()?.accessToken {
            token = "Bearer " + tokenValue
            print("user token")
            print(token)
        }

        print(token)
        let params = EnquiryParams(product_variant_id: model.productVariantID, product_id: model.productID, email: model.email, phone: model.phone)
        HomeRequest.postEnquiry(params, token).response { (result: Result<EnquiryResponse, NError>)
            in

                switch result {
                case let .success(response):
                    completion?(response.error, response.messages)

                case let .failure(error):
                    DispatchQueue.main.async {
                        // hideActivity()
                    }
                    completion?(true, error.localizedDescription)
                    print(error.localizedDescription)
                }
        }
    }

    func homeData(completion: ((Homeresponse?) -> Void)?) {
        guard let tokenData = GuestData.getUserInfo() else {
            print("❌ No guest token found for home data request")
            return
        }

        var token = "Bearer " + tokenData.accessToken
        var tokenType = "Guest"

        if let tokenValue = UserData.getuserInfo()?.accessToken {
            token = "Bearer " + tokenValue
            tokenType = "User"
            print("🔐 Using user token for home data")
        } else {
            print("👤 Using guest token for home data")
        }

        print("🏠 REQUESTING HOME DATA")
        print("Token Type: \(tokenType)")
        print("Authorization: \(token)")

        HomeRequest.home(token).response { (result: Result<Homeresponse, NError>)
            in

                switch result {
                case let .success(response):
                    print("✅ HOME DATA SUCCESS")
                    print("Status: \(response.status)")
                    print("Error: \(response.error)")
                    print("Message: \(response.messages)")

                    // Log offer banners count for verification
                    print("🎯 Offer Banners Count: \(response.data.offerBanners.count)")
                    print("📦 New Products Count: \(response.data.newProducts.count)")
                    print("⭐ Featured Products Count: \(response.data.featuredProducts.count)")

                    completion?(response)

                case let .failure(error):
                    print("❌ HOME DATA FAILED")
                    print("Error: \(error.localizedDescription)")

                    DispatchQueue.main.async {
                        // hideActivity()
                    }
                    completion?(nil)
                }
        }
    }

    func productDetails(data: DetailData, completion: ((ProductDetailResponse?) -> Void)?) {
        guard let tokenData = GuestData.getUserInfo() else { return }
        var token = "Bearer " + tokenData.accessToken
        if let tokenValue = UserData.getuserInfo()?.accessToken {
            token = "Bearer " + tokenValue
        }

        print(token)

        HomeRequest.productdetails(data, token).response { (result: Result<Data, NError>)
            in

                switch result {
                case let .success(response):
                    do {
                        let people = try JSONDecoder().decode(ProductDetailResponse.self, from: response)
                        completion?(people)
                    } catch let error {
                        completion?(nil)
                        print(error.localizedDescription)
                    }

                // completion?(response)
                case let .failure(error):
                    hideActivity()
                    completion?(nil)
                    print(error.localizedDescription)
                }
        }
    }

    func getPrivacyPolicy(completion: ((ContentPageModel?, String?) -> Void)?) {
        guard let tokenData = GuestData.getUserInfo() else { return }
        var token = "Bearer " + tokenData.accessToken
        if let tokenValue = UserData.getuserInfo()?.accessToken { token = "Bearer " + tokenValue }
        HomeRequest.getPrivacyPolicy(token).response { (result: Result<ContentPageModel, NError>)
            in

                switch result {
                case let .success(response):
                    NotificationCenter.callCartSummeryApi()
                    completion?(response, nil)

                case let .failure(error):
                    DispatchQueue.main.async {
                        // hideActivity()
                    }
                    completion?(nil, error.localizedDescription)
                    print(error.localizedDescription)
                }
        }
    }
    
    
    func getTermsAndConditions(completion: ((ContentPageModel?, String?) -> Void)?) {
        guard let tokenData = GuestData.getUserInfo() else { return }
        var token = "Bearer " + tokenData.accessToken
        if let tokenValue = UserData.getuserInfo()?.accessToken { token = "Bearer " + tokenValue }
        HomeRequest.getTermsAndConditions(token).response { (result: Result<ContentPageModel, NError>)
            in

                switch result {
                case let .success(response):
                    NotificationCenter.callCartSummeryApi()
                    completion?(response, nil)

                case let .failure(error):
                    DispatchQueue.main.async {
                        // hideActivity()
                    }
                    completion?(nil, error.localizedDescription)
                    print(error.localizedDescription)
                }
        }
    }

    func addCart(cartdata: AddToCart, completion: ((AddcartResponse?) -> Void)?) {
        guard let tokenData = GuestData.getUserInfo() else { return }
        var token = "Bearer " + tokenData.accessToken
        if let tokenValue = UserData.getuserInfo()?.accessToken {
            token = "Bearer " + tokenValue
        }
        print(cartdata)
        HomeRequest.addtocart(cartdata, token).response { (result: Result<AddcartResponse, NError>)
            in

                switch result {
                case let .success(response):
                    NotificationCenter.callCartSummeryApi()
                    completion?(response)

                case let .failure(error):
                    DispatchQueue.main.async {
                        // hideActivity()
                    }
                    completion?(nil)
                    print(error.localizedDescription)
                }
        }
    }

    func postFcmToken(_ fcmToken: String?) {
        guard let tokenData = GuestData.getUserInfo() else {
            return
        }
        var token = "Bearer " + tokenData.accessToken
        if let tokenValue = UserData.getuserInfo()?.accessToken {
            token = "Bearer " + tokenValue
        }
        let parameter = FcmTokenParameter(fcm_token: fcmToken ?? "", country_code: "KW")
        HomeRequest.postFcmToken(parameter, token).response { (result: Result<FcmTokenUpdateResponse, NError>) in
                
                print("this line executed!")
                
                switch result {
                case .success:
                    print(fcmToken ?? "")
                    print("FCM token Updated")
                case let .failure(error):
//                    completion?(nil)
                    print(error.localizedDescription)
                }
        }
    }

    func cartSummary(params: CartParams? = nil, completion: ((CartResponse?, String?) -> Void)? = nil) {
        guard let tokenData = GuestData.getUserInfo() else { return }
        var token = "Bearer " + tokenData.accessToken
        if let tokenValue = UserData.getuserInfo()?.accessToken {
            token = "Bearer " + tokenValue
        }

        HomeRequest.cartSummary(params, token).response { (result: Result<CartResponse, NError>)
            in

                switch result {
                case let .success(response):
                    completion?(response, nil)
//                    NotificationCenter.refreshHome()
                case let .failure(error):
                    DispatchQueue.main.async {
                        // hideActivity()
                    }
                    completion?(nil, error.localizedDescription)
                    print(error.localizedDescription)
                }
        }
    }

    func cartQtyChange(data: Changequanty, completion: ((CartQuantityResponse?, String?) -> Void)?) {
        guard let tokenData = GuestData.getUserInfo() else { return }
        var token = "Bearer " + tokenData.accessToken
        if let tokenValue = UserData.getuserInfo()?.accessToken {
            token = "Bearer " + tokenValue
        }

        HomeRequest.cartchangequantity(data, token).response { (result: Result<CartQuantityResponse, NError>)
            in

                switch result {
                case let .success(response):
                    completion?(response, nil)

                case let .failure(error):
                    DispatchQueue.main.async {
                        // hideActivity()
                    }
                    completion?(nil, error.localizedDescription)
                    print(error.localizedDescription)
                }
        }
    }

    func cartDeleteItem(data: Deletecartitem, completion: ((Deletecartresponse?) -> Void)?) {
        guard let tokenData = GuestData.getUserInfo() else { return }
        var token = "Bearer " + tokenData.accessToken
        if let tokenValue = UserData.getuserInfo()?.accessToken {
            token = "Bearer " + tokenValue
        }

        HomeRequest.deletecartItem(data, token).response { (result: Result<Deletecartresponse, NError>)
            in

                switch result {
                case let .success(response):
                    NotificationCenter.callCartSummeryApi()
                    completion?(response)

                case let .failure(error):
                    DispatchQueue.main.async {
                        // hideActivity()
                    }
                    completion?(nil)
                    print(error.localizedDescription)
                }
        }
    }

    func getLoyaltyPoints(completion: ((LoyaltyCashModel.Response?) -> Void)?) {
        guard let tokenData = GuestData.getUserInfo() else { return }
        var token = "Bearer " + tokenData.accessToken
        if let tokenValue = UserData.getuserInfo()?.accessToken {
            token = "Bearer " + tokenValue
        }

        HomeRequest.getLoyaltyPoints(token).response { (result: Result<LoyaltyCashModel.Response, NError>)
            in

                switch result {
                case let .success(response):
                    completion?(response)

                case let .failure(error):
                    DispatchQueue.main.async {
                        // hideActivity()
                    }
                    completion?(nil)
                    print(error.localizedDescription)
                }
        }
    }

    func getWalletBalance(completion: ((WalletModel.Response?) -> Void)?) {
        guard let tokenData = GuestData.getUserInfo() else { return }
        var token = "Bearer " + tokenData.accessToken
        if let tokenValue = UserData.getuserInfo()?.accessToken {
            token = "Bearer " + tokenValue
        }

        HomeRequest.getWalletBalance(token).response { (result: Result<WalletModel.Response, NError>)
            in

                switch result {
                case let .success(response):
                    completion?(response)

                case let .failure(error):
                    DispatchQueue.main.async {
                        // hideActivity()
                    }
                    completion?(nil)
                    print(error.localizedDescription)
                }
        }
    }

    func getShareEarn(completion: ((ShareEarnModel.Response?) -> Void)?) {
        guard let tokenData = GuestData.getUserInfo() else { return }
        var token = "Bearer " + tokenData.accessToken
        if let tokenValue = UserData.getuserInfo()?.accessToken {
            token = "Bearer " + tokenValue
        }

        HomeRequest.getShareEarn(token).response { (result: Result<ShareEarnModel.Response, NError>)
            in

                switch result {
                case let .success(response):
                    completion?(response)

                case let .failure(error):
                    DispatchQueue.main.async {
                        // hideActivity()
                    }
                    completion?(nil)
                    print(error.localizedDescription)
                }
        }
    }

    func addwishlist(wishlist: AddWishlist, completion: ((Addwishlistresponse?) -> Void)?) {
        guard let tokenData = GuestData.getUserInfo() else { return }
        var token = "Bearer " + tokenData.accessToken
        if let tokenValue = UserData.getuserInfo()?.accessToken {
            token = "Bearer " + tokenValue
        }
        HomeRequest.addwishlist(wishlist, token).response { (result: Result<Addwishlistresponse, NError>)
            in

                switch result {
                case let .success(response):
                    NotificationCenter.updateWishlistBadge()
                    completion?(response)

                case let .failure(error):
                    DispatchQueue.main.async {
                        hideActivity()
                    }
                    completion?(nil)
                    print(error.localizedDescription)
                }
        }
    }

    func addtoCompare(compare: Compareparam, completion: ((CompareResponse?) -> Void)?) {
        guard let tokenData = GuestData.getUserInfo() else { return }
        var token = "Bearer " + tokenData.accessToken
        if let tokenValue = UserData.getuserInfo()?.accessToken {
            token = "Bearer " + tokenValue
        }
        HomeRequest.addCompare(compare, token).response { (result: Result<CompareResponse, NError>)
            in

                switch result {
                case let .success(response):
                    completion?(response)

                case let .failure(error):
                    DispatchQueue.main.async {
                        hideActivity()
                    }
                    completion?(nil)
                    print(error.localizedDescription)
                }
        }
    }

    func deleteFromWishlist(data: AddWishlist, completion: ((Addwishlistresponse?) -> Void)?) {
        guard let tokenData = GuestData.getUserInfo() else { return }
        var token = "Bearer " + tokenData.accessToken
        if let tokenValue = UserData.getuserInfo()?.accessToken {
            token = "Bearer " + tokenValue
        }
        showActivity()
        HomeRequest.deletewishlist(data, token).response { (result: Result<Addwishlistresponse, NError>)
            in
                hideActivity()
                switch result {
                case let .success(response):
                    NotificationCenter.updateWishlistBadge()
                    completion?(response)

                case let .failure(error):
                    DispatchQueue.main.async {
                        // hideActivity()
                    }
                    completion?(nil)
                    print(error.localizedDescription)
                }
        }
    }

    func wishList(completion: ((WishlistResponse?) -> Void)?) {
        guard let tokenData = GuestData.getUserInfo() else { return }
        var token = "Bearer " + tokenData.accessToken
        if let tokenValue = UserData.getuserInfo()?.accessToken {
            token = "Bearer " + tokenValue
        }
        print(token)
        HomeRequest.showWishlist(token).response { (result: Result<WishlistResponse, NError>)
            in

                switch result {
                case let .success(response):
//                                                do {
//                                                    let res = try JSONDecoder().decode(WishlistResponse.self, from: response)
//                                                    completion?(res)
//                                                }catch let error {
//                                                    print(error)
//                                                }
//                                                completion?(nil)
                    completion?(response)

                case let .failure(error):
                    DispatchQueue.main.async {
                        // hideActivity()
                    }
                    completion?(nil)
                    print(error.localizedDescription)
                }
        }
    }

    func categotyProducts(data: Catproductparam, completion: ((Categoryproductresponse?) -> Void)?) {
        guard let tokenData = GuestData.getUserInfo() else { return }
        var token = "Bearer " + tokenData.accessToken
        if let tokenValue = UserData.getuserInfo()?.accessToken {
            token = "Bearer " + tokenValue
        }
        
        
       

        HomeRequest.categotyproducts(data, token).response { (result: Result<Categoryproductresponse, NError>)
            in

                switch result {
                case let .success(response):
                    completion?(response)

                case let .failure(error):
                    DispatchQueue.main.async {
                        // hideActivity()
                    }
                    completion?(nil)
                    print(error.localizedDescription)
                }
        }
    }

    func changeCurrency(data: Currencyparam, completion: ((Currencychangeresponse?) -> Void)?) {
        guard let tokenData = GuestData.getUserInfo() else { return }
        var token = "Bearer " + tokenData.accessToken
        if let tokenValue = UserData.getuserInfo()?.accessToken {
            token = "Bearer " + tokenValue
        }

        HomeRequest.changeCurrency(data, token).response { (result: Result<Currencychangeresponse, NError>)
            in

                switch result {
                case let .success(response):
                    completion?(response)

                case let .failure(error):
                    DispatchQueue.main.async {
                        // hideActivity()
                    }
                    completion?(nil)
                    print(error.localizedDescription)
                }
        }
    }

    func changeCountry(data: Countryparam, completion: ((Currencychangeresponse?) -> Void)?) {
        guard let tokenData = GuestData.getUserInfo() else { return }
        var token = "Bearer " + tokenData.accessToken
        if let tokenValue = UserData.getuserInfo()?.accessToken {
            token = "Bearer " + tokenValue
        }

        HomeRequest.chnageCountry(data, token).response { (result: Result<Currencychangeresponse, NError>)
            in

                switch result {
                case let .success(response):
                    completion?(response)

                case let .failure(error):
                    DispatchQueue.main.async {
                        // hideActivity()
                    }
                    completion?(nil)
                    print(error.localizedDescription)
                }
        }
    }
    
    
    func getContactUs(completion: ((ContactUsModel.Response?, String?) -> Void)?){
        guard let tokenData = GuestData.getUserInfo() else { return }
        var token = "Bearer " + tokenData.accessToken
        if let tokenValue = UserData.getuserInfo()?.accessToken { token = "Bearer " + tokenValue }
        HomeRequest.getContactUs(token).response { (result: Result<ContactUsModel.Response, NError>)
            in

                switch result {
                case let .success(response):
                    completion?(response, nil)

                case let .failure(error):
                    DispatchQueue.main.async {
                        // hideActivity()
                    }
                    completion?(nil, error.localizedDescription)
                    print(error.localizedDescription)
                }
        }
    }

    func applyPromotions(data: promoCodeApply, completion: ((ApplyPromo?, String?) -> Void)?) {
        guard let tokenData = GuestData.getUserInfo() else { return }
        var token = "Bearer " + tokenData.accessToken
        if let tokenValue = UserData.getuserInfo()?.accessToken { token = "Bearer " + tokenValue }
        print(token)
        HomeRequest.applyPromocode(data, token).response { (result: Result<ApplyPromo, NError>)
            in

                switch result {
                case let .success(response):
                    completion?(response, nil)

                case let .failure(error):
                    DispatchQueue.main.async {
                        // hideActivity()
                    }
                    completion?(nil, error.localizedDescription)
                    print(error.localizedDescription)
                }
        }
    }

    func payFinalCheckout(data: placeOrderData, completion: ((PlaceOrderDetails?) -> Void)?) {
        guard let tokenData = GuestData.getUserInfo() else { return }
        var token = "Bearer " + tokenData.accessToken
        if let tokenValue = UserData.getuserInfo()?.accessToken {
            token = "Bearer " + tokenValue
        }

        HomeRequest.placeOrder(data, token).response { (result: Result<PlaceOrderDetails, NError>)
            in

                switch result {
                case let .success(response):
                    completion?(response)

                case let .failure(error):
                    DispatchQueue.main.async {
                        // hideActivity()
                    }
                    completion?(nil)
                    print(error.localizedDescription)
                }
        }
    }

    func deliveryArea(governorateID: String, completion: ((DeliveryAreaResponse?) -> Void)?) {
        guard let tokenData = GuestData.getUserInfo() else { return }
        var token = "Bearer " + tokenData.accessToken
        if let tokenValue = UserData.getuserInfo()?.accessToken {
            token = "Bearer " + tokenValue
        }
        let param = GovernorateAreaListParam(governorate_id: governorateID)
        HomeRequest.getGovernorateAreaList(param, token).response { (result: Result<DeliveryAreaResponse, NError>)
            in

                switch result {
                case let .success(response):
                    completion?(response)

                case let .failure(error):
                    DispatchQueue.main.async {
                        // hideActivity()
                    }
                    completion?(nil)
                    print(error.localizedDescription)
                }
        }
    }

    func getGovernorateList(completion: ((GovernorateModel.Response?) -> Void)?) {
        guard let tokenData = GuestData.getUserInfo() else { return }
        var token = "Bearer " + tokenData.accessToken
        if let tokenValue = UserData.getuserInfo()?.accessToken {
            token = "Bearer " + tokenValue
        }

        HomeRequest.getGovernorateList(token).response { (result: Result<GovernorateModel.Response, NError>)
            in

                switch result {
                case let .success(response):
                    completion?(response)

                case let .failure(error):
                    DispatchQueue.main.async {
                        // hideActivity()
                    }
                    completion?(nil)
                    print(error.localizedDescription)
                }
        }
    }

    // Add address
    func addAddress(data: AddaddressData, completion: ((Addressaddresponse?) -> Void)?) {
        guard let tokenData = GuestData.getUserInfo() else { return }
        var token = "Bearer " + tokenData.accessToken
        if let tokenValue = UserData.getuserInfo()?.accessToken {
            token = "Bearer " + tokenValue
        }

        HomeRequest.addAddress(data, token).response { (result: Result<Addressaddresponse, NError>)
            in

                switch result {
                case let .success(response):
                    completion?(response)

                case let .failure(error):
                    DispatchQueue.main.async {
                        // hideActivity()
                    }
                    completion?(nil)
                    print(error.localizedDescription)
                }
        }
    }

    func addressList(completion: ((AddresslistResponse?) -> Void)?) {
        guard let tokenData = GuestData.getUserInfo() else { return }
        var token = "Bearer " + tokenData.accessToken
        if let tokenValue = UserData.getuserInfo()?.accessToken {
            token = "Bearer " + tokenValue
        }

        HomeRequest.addresslist(token).response { (result: Result<AddresslistResponse, NError>)
            in

                switch result {
                case let .success(response):
                    completion?(response)

                case let .failure(error):
                    DispatchQueue.main.async {
                        // hideActivity()
                    }
                    completion?(nil)
                    print(error.localizedDescription)
                }
        }
    }

    func deleteAddress(data: Deleteaddressparam, completion: ((AddresslistResponse?) -> Void)?) {
        guard let tokenData = GuestData.getUserInfo() else { return }
        var token = "Bearer " + tokenData.accessToken
        if let tokenValue = UserData.getuserInfo()?.accessToken {
            token = "Bearer " + tokenValue
        }

        HomeRequest.deleteAddress(data, token).response { (result: Result<AddresslistResponse, NError>)
            in

                switch result {
                case let .success(response):
                    completion?(response)

                case let .failure(error):
                    DispatchQueue.main.async {
                        // hideActivity()
                    }
                    completion?(nil)
                    print(error.localizedDescription)
                }
        }
    }

    func applyPromocode(data: Promocodeparam, completion: ((PromocodeResponse?) -> Void)?) {
        guard let tokenData = GuestData.getUserInfo() else { return }
        var token = "Bearer " + tokenData.accessToken
        if let tokenValue = UserData.getuserInfo()?.accessToken {
            token = "Bearer " + tokenValue
        }

        HomeRequest.promocode(data, token).response { (result: Result<PromocodeResponse, NError>)
            in

                switch result {
                case let .success(response):
                    completion?(response)

                case let .failure(error):
                    DispatchQueue.main.async {
                        // hideActivity()
                    }
                    completion?(nil)
                    print(error.localizedDescription)
                }
        }
    }

    func lanChanged(data: Lanparam, completion: ((LanguageChangeResponse?) -> Void)?) {
        guard let tokenData = GuestData.getUserInfo() else { return }
        var token = "Bearer " + tokenData.accessToken
        if let tokenValue = UserData.getuserInfo()?.accessToken {
            token = "Bearer " + tokenValue
        }

        HomeRequest.languageChanged(data, token).response { (result: Result<LanguageChangeResponse, NError>)
            in

                switch result {
                case let .success(response):
                    completion?(response)

                case let .failure(error):
                    DispatchQueue.main.async {
                        // hideActivity()
                    }
                    completion?(nil)
                    print(error.localizedDescription)
                }
        }
    }
}
