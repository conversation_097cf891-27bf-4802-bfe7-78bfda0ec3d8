<?xml version="1.0" encoding="UTF-8"?>
<document type="com.apple.InterfaceBuilder3.CocoaTouch.XIB" version="3.0" toolsVersion="17701" targetRuntime="iOS.CocoaTouch" propertyAccessControl="none" useAutolayout="YES" useTraitCollections="YES" useSafeAreas="YES" colorMatched="YES">
    <device id="retina6_1" orientation="portrait" appearance="light"/>
    <dependencies>
        <deployment identifier="iOS"/>
        <plugIn identifier="com.apple.InterfaceBuilder.IBCocoaTouchPlugin" version="17703"/>
        <capability name="Safe area layout guides" minToolsVersion="9.0"/>
        <capability name="documents saved in the Xcode 8 format" minToolsVersion="8.0"/>
    </dependencies>
    <customFonts key="customFonts">
        <array key="Roboto-Regular.ttf">
            <string>Roboto-Regular</string>
        </array>
    </customFonts>
    <objects>
        <placeholder placeholderIdentifier="IBFilesOwner" id="-1" userLabel="File's Owner"/>
        <placeholder placeholderIdentifier="IBFirstResponder" id="-2" customClass="UIResponder"/>
        <collectionViewCell opaque="NO" clipsSubviews="YES" multipleTouchEnabled="YES" contentMode="center" id="gTV-IL-0wX" customClass="SizeCollectionViewCell" customModule="Saydality" customModuleProvider="target">
            <rect key="frame" x="0.0" y="0.0" width="70" height="50"/>
            <autoresizingMask key="autoresizingMask"/>
            <view key="contentView" opaque="NO" clipsSubviews="YES" multipleTouchEnabled="YES" contentMode="center">
                <rect key="frame" x="0.0" y="0.0" width="70" height="50"/>
                <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMaxY="YES"/>
                <subviews>
                    <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="iV9-re-ZwK">
                        <rect key="frame" x="10" y="10" width="50" height="30"/>
                        <subviews>
                            <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="50ml" textAlignment="center" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="5ZU-YY-0b1">
                                <rect key="frame" x="8" y="8" width="34" height="14"/>
                                <fontDescription key="fontDescription" name="Roboto-Regular" family="Roboto" pointSize="13"/>
                                <nil key="textColor"/>
                                <nil key="highlightedColor"/>
                            </label>
                        </subviews>
                        <color key="backgroundColor" red="0.44705882349999998" green="0.55294117649999996" blue="0.92941176469999998" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                        <constraints>
                            <constraint firstAttribute="bottom" secondItem="5ZU-YY-0b1" secondAttribute="bottom" constant="8" id="9eE-wR-gxu"/>
                            <constraint firstAttribute="trailing" secondItem="5ZU-YY-0b1" secondAttribute="trailing" constant="8" id="J1p-Nx-6H4"/>
                            <constraint firstItem="5ZU-YY-0b1" firstAttribute="top" secondItem="iV9-re-ZwK" secondAttribute="top" constant="8" id="ilh-1J-pco"/>
                            <constraint firstItem="5ZU-YY-0b1" firstAttribute="leading" secondItem="iV9-re-ZwK" secondAttribute="leading" constant="8" id="uaG-RC-UG4"/>
                        </constraints>
                    </view>
                </subviews>
            </view>
            <viewLayoutGuide key="safeArea" id="ZTg-uK-7eu"/>
            <constraints>
                <constraint firstItem="iV9-re-ZwK" firstAttribute="top" secondItem="gTV-IL-0wX" secondAttribute="top" constant="10" id="1kH-s1-Btf"/>
                <constraint firstAttribute="trailing" secondItem="iV9-re-ZwK" secondAttribute="trailing" constant="10" id="STL-42-tTP"/>
                <constraint firstItem="iV9-re-ZwK" firstAttribute="leading" secondItem="gTV-IL-0wX" secondAttribute="leading" constant="10" id="gaX-qF-PgP"/>
                <constraint firstAttribute="bottom" secondItem="iV9-re-ZwK" secondAttribute="bottom" constant="10" id="tom-oP-Vyr"/>
            </constraints>
            <size key="customSize" width="71" height="62"/>
            <connections>
                <outlet property="sizeView" destination="iV9-re-ZwK" id="79v-0k-8Ez"/>
                <outlet property="tLbl" destination="5ZU-YY-0b1" id="Iu6-xP-yZL"/>
            </connections>
            <point key="canvasLocation" x="-102.89855072463769" y="116.51785714285714"/>
        </collectionViewCell>
    </objects>
</document>
