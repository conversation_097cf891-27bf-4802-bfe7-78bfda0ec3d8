<?xml version="1.0" encoding="UTF-8"?>
<document type="com.apple.InterfaceBuilder3.CocoaTouch.Storyboard.XIB" version="3.0" toolsVersion="32700.99.1234" targetRuntime="iOS.CocoaTouch" propertyAccessControl="none" useAutolayout="YES" useTraitCollections="YES" useSafeAreas="YES" colorMatched="YES">
    <device id="retina6_1" orientation="portrait" appearance="light"/>
    <dependencies>
        <deployment identifier="iOS"/>
        <plugIn identifier="com.apple.InterfaceBuilder.IBCocoaTouchPlugin" version="22685"/>
        <capability name="Safe area layout guides" minToolsVersion="9.0"/>
        <capability name="System colors in document resources" minToolsVersion="11.0"/>
        <capability name="documents saved in the Xcode 8 format" minToolsVersion="8.0"/>
    </dependencies>
    <scenes>
        <!--CategoryVC-->
        <scene sceneID="ldI-Zh-0nj">
            <objects>
                <viewController storyboardIdentifier="CategoryVC" id="sqP-Ea-oRz" customClass="CategoryVC" customModule="BabyHouse" customModuleProvider="target" sceneMemberID="viewController">
                    <view key="view" contentMode="scaleToFill" id="Fdq-5y-RU7">
                        <rect key="frame" x="0.0" y="0.0" width="414" height="896"/>
                        <autoresizingMask key="autoresizingMask" widthSizable="YES" heightSizable="YES"/>
                        <subviews>
                            <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="zkk-t0-q1X">
                                <rect key="frame" x="0.0" y="-30" width="414" height="142"/>
                                <color key="backgroundColor" red="0.71372549019999998" green="0.21176470589999999" blue="0.30588235289999999" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                            </view>
                            <view clipsSubviews="YES" contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="2Sa-ro-J7t">
                                <rect key="frame" x="0.0" y="48" width="414" height="64"/>
                                <subviews>
                                    <imageView clipsSubviews="YES" userInteractionEnabled="NO" contentMode="scaleAspectFit" horizontalHuggingPriority="251" verticalHuggingPriority="251" image="leftarrowB" translatesAutoresizingMaskIntoConstraints="NO" id="CLv-xa-Sut">
                                        <rect key="frame" x="10" y="23" width="18" height="18"/>
                                        <constraints>
                                            <constraint firstAttribute="width" constant="18" id="bkn-Nq-Evq"/>
                                        </constraints>
                                    </imageView>
                                    <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Categories" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="Obh-bH-vg0">
                                        <rect key="frame" x="60" y="8" width="83" height="48"/>
                                        <fontDescription key="fontDescription" type="system" pointSize="17"/>
                                        <color key="textColor" red="1" green="1" blue="1" alpha="1" colorSpace="calibratedRGB"/>
                                        <nil key="highlightedColor"/>
                                    </label>
                                    <stackView opaque="NO" contentMode="scaleToFill" distribution="fillEqually" spacing="10" translatesAutoresizingMaskIntoConstraints="NO" id="5LK-r2-qTf">
                                        <rect key="frame" x="338" y="19" width="61" height="26"/>
                                        <subviews>
                                            <imageView clipsSubviews="YES" userInteractionEnabled="NO" contentMode="scaleAspectFit" horizontalHuggingPriority="251" verticalHuggingPriority="251" image="cart" translatesAutoresizingMaskIntoConstraints="NO" id="Ola-0a-tXS">
                                                <rect key="frame" x="0.0" y="0.0" width="25.5" height="26"/>
                                                <constraints>
                                                    <constraint firstAttribute="width" secondItem="Ola-0a-tXS" secondAttribute="height" multiplier="1:1" id="4BE-Lu-2uf"/>
                                                </constraints>
                                            </imageView>
                                            <imageView clipsSubviews="YES" userInteractionEnabled="NO" contentMode="scaleAspectFit" horizontalHuggingPriority="251" verticalHuggingPriority="251" image="notification-status" translatesAutoresizingMaskIntoConstraints="NO" id="cR9-Aw-DpT">
                                                <rect key="frame" x="35.5" y="0.0" width="25.5" height="26"/>
                                            </imageView>
                                        </subviews>
                                    </stackView>
                                    <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="ZM9-hE-AkR">
                                        <rect key="frame" x="353.5" y="14" width="15" height="15"/>
                                        <subviews>
                                            <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="0" textAlignment="center" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" minimumScaleFactor="0.69999998807907104" translatesAutoresizingMaskIntoConstraints="NO" id="TgA-Z5-IQy">
                                                <rect key="frame" x="2" y="2" width="11" height="11"/>
                                                <fontDescription key="fontDescription" type="system" pointSize="9"/>
                                                <color key="textColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                                <nil key="highlightedColor"/>
                                            </label>
                                        </subviews>
                                        <color key="backgroundColor" red="0.86274509799999999" green="0.035294117649999998" blue="0.19215686269999999" alpha="1" colorSpace="calibratedRGB"/>
                                        <constraints>
                                            <constraint firstAttribute="trailing" secondItem="TgA-Z5-IQy" secondAttribute="trailing" constant="2" id="9cP-e6-uhz"/>
                                            <constraint firstAttribute="bottom" secondItem="TgA-Z5-IQy" secondAttribute="bottom" constant="2" id="NAF-AL-AF4"/>
                                            <constraint firstAttribute="width" constant="15" id="cPf-3p-rWy"/>
                                            <constraint firstItem="TgA-Z5-IQy" firstAttribute="top" secondItem="ZM9-hE-AkR" secondAttribute="top" constant="2" id="jTJ-zi-1MX"/>
                                            <constraint firstItem="TgA-Z5-IQy" firstAttribute="leading" secondItem="ZM9-hE-AkR" secondAttribute="leading" constant="2" id="kw4-oo-7F6"/>
                                            <constraint firstAttribute="height" constant="15" id="mDO-ET-dYt"/>
                                        </constraints>
                                    </view>
                                    <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="Uni-aW-UAg">
                                        <rect key="frame" x="389" y="14" width="15" height="15"/>
                                        <subviews>
                                            <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="0" textAlignment="center" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" minimumScaleFactor="0.69999998807907104" translatesAutoresizingMaskIntoConstraints="NO" id="y7R-QI-dPj">
                                                <rect key="frame" x="2" y="2" width="6" height="11"/>
                                                <fontDescription key="fontDescription" type="system" pointSize="9"/>
                                                <color key="textColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                                <nil key="highlightedColor"/>
                                            </label>
                                        </subviews>
                                        <color key="backgroundColor" red="0.86274509799999999" green="0.035294117649999998" blue="0.19215686269999999" alpha="1" colorSpace="calibratedRGB"/>
                                        <constraints>
                                            <constraint firstAttribute="width" constant="15" id="RFN-dV-NHR"/>
                                            <constraint firstItem="y7R-QI-dPj" firstAttribute="leading" secondItem="Uni-aW-UAg" secondAttribute="leading" constant="2" id="VIw-Cp-wx0"/>
                                            <constraint firstAttribute="height" constant="15" id="rfW-tA-bpZ"/>
                                            <constraint firstAttribute="bottom" secondItem="y7R-QI-dPj" secondAttribute="bottom" constant="2" id="tdx-kW-UvT"/>
                                        </constraints>
                                    </view>
                                    <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" buttonType="system" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="NCZ-hr-87L">
                                        <rect key="frame" x="0.0" y="0.0" width="60" height="64"/>
                                        <constraints>
                                            <constraint firstAttribute="width" constant="60" id="Z3H-vH-9g3"/>
                                        </constraints>
                                        <connections>
                                            <action selector="back:" destination="sqP-Ea-oRz" eventType="touchUpInside" id="lug-KF-J63"/>
                                        </connections>
                                    </button>
                                    <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" buttonType="system" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="gAk-NK-xPA">
                                        <rect key="frame" x="338" y="19" width="25.5" height="26"/>
                                        <state key="normal" title="Button"/>
                                        <buttonConfiguration key="configuration" style="plain" title=" "/>
                                        <connections>
                                            <action selector="showCart:" destination="sqP-Ea-oRz" eventType="touchUpInside" id="DSA-52-4Tj"/>
                                        </connections>
                                    </button>
                                    <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" buttonType="system" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="Xbp-iE-5a9">
                                        <rect key="frame" x="373.5" y="19" width="25.5" height="26"/>
                                        <state key="normal" title="Button"/>
                                        <buttonConfiguration key="configuration" style="plain" title=" "/>
                                        <connections>
                                            <action selector="showNotification:" destination="sqP-Ea-oRz" eventType="touchUpInside" id="wM8-6Q-S0T"/>
                                        </connections>
                                    </button>
                                </subviews>
                                <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                <constraints>
                                    <constraint firstItem="gAk-NK-xPA" firstAttribute="leading" secondItem="Ola-0a-tXS" secondAttribute="leading" id="5gD-RP-V0E"/>
                                    <constraint firstItem="ZM9-hE-AkR" firstAttribute="top" secondItem="Ola-0a-tXS" secondAttribute="top" constant="-5" id="6Qk-IY-Nar"/>
                                    <constraint firstItem="Obh-bH-vg0" firstAttribute="leading" secondItem="NCZ-hr-87L" secondAttribute="trailing" id="8hR-xs-Eo5"/>
                                    <constraint firstItem="Ola-0a-tXS" firstAttribute="height" secondItem="2Sa-ro-J7t" secondAttribute="height" multiplier="0.4" id="8ml-fT-9eD"/>
                                    <constraint firstItem="Xbp-iE-5a9" firstAttribute="top" secondItem="cR9-Aw-DpT" secondAttribute="top" id="Cbr-6s-ywd"/>
                                    <constraint firstItem="Obh-bH-vg0" firstAttribute="top" secondItem="2Sa-ro-J7t" secondAttribute="top" constant="8" id="EU7-3Y-OrU"/>
                                    <constraint firstItem="Uni-aW-UAg" firstAttribute="trailing" secondItem="cR9-Aw-DpT" secondAttribute="trailing" constant="5" id="FXx-IM-TRj"/>
                                    <constraint firstItem="NCZ-hr-87L" firstAttribute="top" secondItem="2Sa-ro-J7t" secondAttribute="top" id="GDA-ua-Ltx"/>
                                    <constraint firstItem="gAk-NK-xPA" firstAttribute="bottom" secondItem="Ola-0a-tXS" secondAttribute="bottom" id="H45-FV-e9m"/>
                                    <constraint firstItem="Xbp-iE-5a9" firstAttribute="trailing" secondItem="cR9-Aw-DpT" secondAttribute="trailing" id="IPq-mZ-WyI"/>
                                    <constraint firstItem="5LK-r2-qTf" firstAttribute="centerY" secondItem="2Sa-ro-J7t" secondAttribute="centerY" id="LcI-hT-uev"/>
                                    <constraint firstItem="Uni-aW-UAg" firstAttribute="top" secondItem="cR9-Aw-DpT" secondAttribute="top" constant="-5" id="N6x-2T-hjd"/>
                                    <constraint firstItem="NCZ-hr-87L" firstAttribute="leading" secondItem="2Sa-ro-J7t" secondAttribute="leading" id="Qm8-1c-0X5"/>
                                    <constraint firstAttribute="trailing" secondItem="5LK-r2-qTf" secondAttribute="trailing" constant="15" id="UO3-PN-AKh"/>
                                    <constraint firstItem="Xbp-iE-5a9" firstAttribute="leading" secondItem="cR9-Aw-DpT" secondAttribute="leading" id="WJl-NQ-NG7"/>
                                    <constraint firstItem="gAk-NK-xPA" firstAttribute="top" secondItem="Ola-0a-tXS" secondAttribute="top" id="Wvf-v1-4G8"/>
                                    <constraint firstItem="gAk-NK-xPA" firstAttribute="trailing" secondItem="Ola-0a-tXS" secondAttribute="trailing" id="ZaY-aJ-RcA"/>
                                    <constraint firstItem="Xbp-iE-5a9" firstAttribute="bottom" secondItem="cR9-Aw-DpT" secondAttribute="bottom" id="cMO-e8-dl2"/>
                                    <constraint firstAttribute="bottom" secondItem="NCZ-hr-87L" secondAttribute="bottom" id="d9b-a2-eP4"/>
                                    <constraint firstItem="Obh-bH-vg0" firstAttribute="leading" secondItem="CLv-xa-Sut" secondAttribute="trailing" constant="32" id="eOi-YT-QN7"/>
                                    <constraint firstItem="ZM9-hE-AkR" firstAttribute="trailing" secondItem="Ola-0a-tXS" secondAttribute="trailing" constant="5" id="k1u-cn-aL4"/>
                                    <constraint firstItem="CLv-xa-Sut" firstAttribute="top" secondItem="2Sa-ro-J7t" secondAttribute="top" constant="23" id="v5F-zh-iwQ"/>
                                    <constraint firstAttribute="bottom" secondItem="Obh-bH-vg0" secondAttribute="bottom" constant="8" id="w4s-CA-L6f"/>
                                    <constraint firstAttribute="height" constant="64" id="whs-XH-lFY"/>
                                    <constraint firstItem="CLv-xa-Sut" firstAttribute="centerY" secondItem="NCZ-hr-87L" secondAttribute="centerY" id="yap-bL-iVg"/>
                                </constraints>
                            </view>
                            <tableView clipsSubviews="YES" contentMode="scaleToFill" alwaysBounceVertical="YES" dataMode="prototypes" style="plain" separatorStyle="none" rowHeight="-1" estimatedRowHeight="-1" sectionHeaderHeight="-1" estimatedSectionHeaderHeight="-1" sectionFooterHeight="-1" estimatedSectionFooterHeight="-1" translatesAutoresizingMaskIntoConstraints="NO" id="v3q-GP-9GP">
                                <rect key="frame" x="0.0" y="112" width="414" height="784"/>
                                <color key="backgroundColor" systemColor="systemBackgroundColor"/>
                                <connections>
                                    <outlet property="dataSource" destination="sqP-Ea-oRz" id="eSj-2j-JgZ"/>
                                    <outlet property="delegate" destination="sqP-Ea-oRz" id="pdA-zC-mS3"/>
                                </connections>
                            </tableView>
                        </subviews>
                        <viewLayoutGuide key="safeArea" id="k8P-6J-g04"/>
                        <color key="backgroundColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                        <constraints>
                            <constraint firstItem="k8P-6J-g04" firstAttribute="trailing" secondItem="v3q-GP-9GP" secondAttribute="trailing" id="2Qb-NP-bip"/>
                            <constraint firstItem="v3q-GP-9GP" firstAttribute="leading" secondItem="k8P-6J-g04" secondAttribute="leading" id="5Ca-ib-76y"/>
                            <constraint firstAttribute="bottom" secondItem="v3q-GP-9GP" secondAttribute="bottom" id="HoY-bT-Nq5"/>
                            <constraint firstItem="zkk-t0-q1X" firstAttribute="leading" secondItem="k8P-6J-g04" secondAttribute="leading" id="MuL-oS-zZh"/>
                            <constraint firstItem="2Sa-ro-J7t" firstAttribute="top" secondItem="k8P-6J-g04" secondAttribute="top" id="N1E-mU-1Xa"/>
                            <constraint firstItem="k8P-6J-g04" firstAttribute="trailing" secondItem="zkk-t0-q1X" secondAttribute="trailing" id="R6v-zh-q1e"/>
                            <constraint firstItem="zkk-t0-q1X" firstAttribute="top" secondItem="Fdq-5y-RU7" secondAttribute="top" constant="-30" id="eUj-Lt-Mx6"/>
                            <constraint firstItem="zkk-t0-q1X" firstAttribute="bottom" secondItem="2Sa-ro-J7t" secondAttribute="bottom" id="erU-F5-Qag"/>
                            <constraint firstItem="v3q-GP-9GP" firstAttribute="top" secondItem="zkk-t0-q1X" secondAttribute="bottom" id="hwh-ud-XcN"/>
                            <constraint firstItem="2Sa-ro-J7t" firstAttribute="leading" secondItem="k8P-6J-g04" secondAttribute="leading" id="pVG-W7-er9"/>
                            <constraint firstItem="2Sa-ro-J7t" firstAttribute="trailing" secondItem="k8P-6J-g04" secondAttribute="trailing" id="zsz-91-prE"/>
                        </constraints>
                    </view>
                    <connections>
                        <outlet property="cartLbl" destination="TgA-Z5-IQy" id="EAe-Xn-jWr"/>
                        <outlet property="cartView" destination="ZM9-hE-AkR" id="MR6-48-m59"/>
                        <outlet property="headerLbl" destination="Obh-bH-vg0" id="kUI-SE-bKU"/>
                        <outlet property="notiLbl" destination="y7R-QI-dPj" id="5y9-AQ-6c5"/>
                        <outlet property="notiView" destination="Uni-aW-UAg" id="lK0-qs-ceU"/>
                        <outlet property="tableView" destination="v3q-GP-9GP" id="5TV-l0-se1"/>
                        <outlet property="top2View" destination="2Sa-ro-J7t" id="cro-dQ-sjM"/>
                        <outlet property="topZView" destination="zkk-t0-q1X" id="9fX-sX-ytD"/>
                    </connections>
                </viewController>
                <placeholder placeholderIdentifier="IBFirstResponder" id="kHV-3h-Efq" userLabel="First Responder" customClass="UIResponder" sceneMemberID="firstResponder"/>
            </objects>
            <point key="canvasLocation" x="-1510" y="-49"/>
        </scene>
        <!--CategoryprodutsVC-->
        <scene sceneID="1Be-gy-cqf">
            <objects>
                <viewController storyboardIdentifier="CategoryprodutsVC" id="pmQ-6T-0ek" customClass="CategoryprodutsVC" customModule="BabyHouse" customModuleProvider="target" sceneMemberID="viewController">
                    <view key="view" contentMode="scaleToFill" id="v2m-Tb-FbK">
                        <rect key="frame" x="0.0" y="0.0" width="414" height="896"/>
                        <autoresizingMask key="autoresizingMask" widthSizable="YES" heightSizable="YES"/>
                        <subviews>
                            <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="x5x-vC-Uyq">
                                <rect key="frame" x="0.0" y="-30" width="414" height="142"/>
                                <color key="backgroundColor" red="0.71372549019999998" green="0.21176470589999999" blue="0.30588235289999999" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                            </view>
                            <view clipsSubviews="YES" contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="dcM-Zy-Fwq">
                                <rect key="frame" x="0.0" y="48" width="414" height="64"/>
                                <subviews>
                                    <imageView clipsSubviews="YES" userInteractionEnabled="NO" contentMode="scaleAspectFit" horizontalHuggingPriority="251" verticalHuggingPriority="251" image="leftarrowB" translatesAutoresizingMaskIntoConstraints="NO" id="qoB-Dt-Pwa">
                                        <rect key="frame" x="10" y="23" width="18" height="18"/>
                                        <constraints>
                                            <constraint firstAttribute="width" constant="18" id="T1g-e1-X5H"/>
                                        </constraints>
                                    </imageView>
                                    <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Products" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="GOe-CI-6MB">
                                        <rect key="frame" x="60" y="8" width="69" height="48"/>
                                        <fontDescription key="fontDescription" type="system" pointSize="17"/>
                                        <color key="textColor" red="1" green="1" blue="1" alpha="1" colorSpace="calibratedRGB"/>
                                        <nil key="highlightedColor"/>
                                    </label>
                                    <stackView opaque="NO" contentMode="scaleToFill" distribution="fillEqually" spacing="10" translatesAutoresizingMaskIntoConstraints="NO" id="8hf-JA-2pt">
                                        <rect key="frame" x="336.5" y="19" width="62.5" height="26"/>
                                        <subviews>
                                            <imageView clipsSubviews="YES" userInteractionEnabled="NO" contentMode="scaleAspectFit" horizontalHuggingPriority="251" verticalHuggingPriority="251" image="cart" translatesAutoresizingMaskIntoConstraints="NO" id="yOH-sF-YSD">
                                                <rect key="frame" x="0.0" y="0.0" width="26.5" height="26"/>
                                                <constraints>
                                                    <constraint firstAttribute="width" secondItem="yOH-sF-YSD" secondAttribute="height" multiplier="1:1" id="JSy-Zs-goF"/>
                                                </constraints>
                                            </imageView>
                                            <imageView clipsSubviews="YES" userInteractionEnabled="NO" contentMode="scaleAspectFit" horizontalHuggingPriority="251" verticalHuggingPriority="251" image="notification-status" translatesAutoresizingMaskIntoConstraints="NO" id="Rj3-x6-vdV">
                                                <rect key="frame" x="36.5" y="0.0" width="26" height="26"/>
                                            </imageView>
                                        </subviews>
                                    </stackView>
                                    <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="k7h-vJ-HGJ">
                                        <rect key="frame" x="353" y="14" width="15" height="15"/>
                                        <subviews>
                                            <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="0" textAlignment="center" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" minimumScaleFactor="0.69999998807907104" translatesAutoresizingMaskIntoConstraints="NO" id="hVF-7u-pC9">
                                                <rect key="frame" x="2" y="2" width="11" height="11"/>
                                                <fontDescription key="fontDescription" type="system" pointSize="9"/>
                                                <color key="textColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                                <nil key="highlightedColor"/>
                                            </label>
                                        </subviews>
                                        <color key="backgroundColor" red="0.86274509799999999" green="0.035294117649999998" blue="0.19215686269999999" alpha="1" colorSpace="calibratedRGB"/>
                                        <constraints>
                                            <constraint firstItem="hVF-7u-pC9" firstAttribute="leading" secondItem="k7h-vJ-HGJ" secondAttribute="leading" constant="2" id="A5R-dl-pCT"/>
                                            <constraint firstAttribute="trailing" secondItem="hVF-7u-pC9" secondAttribute="trailing" constant="2" id="Bnm-hz-kvh"/>
                                            <constraint firstAttribute="width" constant="15" id="E6K-nP-4ek"/>
                                            <constraint firstAttribute="bottom" secondItem="hVF-7u-pC9" secondAttribute="bottom" constant="2" id="GlC-f3-NFj"/>
                                            <constraint firstAttribute="height" constant="15" id="HBh-7F-oyp"/>
                                            <constraint firstItem="hVF-7u-pC9" firstAttribute="top" secondItem="k7h-vJ-HGJ" secondAttribute="top" constant="2" id="e8X-Tg-qxX"/>
                                        </constraints>
                                    </view>
                                    <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="1JL-yZ-vHi">
                                        <rect key="frame" x="389" y="14" width="15" height="15"/>
                                        <subviews>
                                            <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="0" textAlignment="center" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" minimumScaleFactor="0.69999998807907104" translatesAutoresizingMaskIntoConstraints="NO" id="WoO-le-l4k">
                                                <rect key="frame" x="2" y="2" width="11" height="11"/>
                                                <fontDescription key="fontDescription" type="system" pointSize="9"/>
                                                <color key="textColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                                <nil key="highlightedColor"/>
                                            </label>
                                        </subviews>
                                        <color key="backgroundColor" red="0.86274509799999999" green="0.035294117649999998" blue="0.19215686269999999" alpha="1" colorSpace="calibratedRGB"/>
                                        <constraints>
                                            <constraint firstItem="WoO-le-l4k" firstAttribute="leading" secondItem="1JL-yZ-vHi" secondAttribute="leading" constant="2" id="03A-iz-OuW"/>
                                            <constraint firstAttribute="bottom" secondItem="WoO-le-l4k" secondAttribute="bottom" constant="2" id="Qka-3e-b60"/>
                                            <constraint firstItem="WoO-le-l4k" firstAttribute="top" secondItem="1JL-yZ-vHi" secondAttribute="top" constant="2" id="WYC-bL-dra"/>
                                            <constraint firstAttribute="trailing" secondItem="WoO-le-l4k" secondAttribute="trailing" constant="2" id="giC-LX-3uw"/>
                                            <constraint firstAttribute="width" constant="15" id="ta1-IZ-CQs"/>
                                            <constraint firstAttribute="height" constant="15" id="wpM-K4-7yg"/>
                                        </constraints>
                                    </view>
                                    <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" buttonType="system" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="PCJ-0N-vD9">
                                        <rect key="frame" x="0.0" y="0.0" width="60" height="64"/>
                                        <constraints>
                                            <constraint firstAttribute="width" constant="60" id="UiQ-pw-nE9"/>
                                        </constraints>
                                        <connections>
                                            <action selector="back:" destination="pmQ-6T-0ek" eventType="touchUpInside" id="aDZ-Io-tIS"/>
                                        </connections>
                                    </button>
                                    <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" buttonType="system" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="gIk-2C-EPJ">
                                        <rect key="frame" x="336.5" y="19" width="26.5" height="26"/>
                                        <state key="normal" title="Button"/>
                                        <buttonConfiguration key="configuration" style="plain" title=" "/>
                                        <connections>
                                            <action selector="showCart:" destination="pmQ-6T-0ek" eventType="touchUpInside" id="6Bo-JJ-4u2"/>
                                            <action selector="showCart:" destination="sqP-Ea-oRz" eventType="touchUpInside" id="XM5-ga-v8C"/>
                                        </connections>
                                    </button>
                                    <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" buttonType="system" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="ZAC-ZK-y6Z">
                                        <rect key="frame" x="373" y="19" width="26" height="26"/>
                                        <state key="normal" title="Button"/>
                                        <buttonConfiguration key="configuration" style="plain" title=" "/>
                                        <connections>
                                            <action selector="showNotification:" destination="pmQ-6T-0ek" eventType="touchUpInside" id="ae4-Kf-7o6"/>
                                            <action selector="showNotification:" destination="sqP-Ea-oRz" eventType="touchUpInside" id="cem-vG-d6b"/>
                                        </connections>
                                    </button>
                                </subviews>
                                <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                <constraints>
                                    <constraint firstAttribute="trailing" secondItem="8hf-JA-2pt" secondAttribute="trailing" constant="15" id="0xt-aC-T0l"/>
                                    <constraint firstItem="gIk-2C-EPJ" firstAttribute="leading" secondItem="yOH-sF-YSD" secondAttribute="leading" id="7qI-yD-xW5"/>
                                    <constraint firstItem="PCJ-0N-vD9" firstAttribute="top" secondItem="dcM-Zy-Fwq" secondAttribute="top" id="CK1-hz-xlj"/>
                                    <constraint firstItem="GOe-CI-6MB" firstAttribute="top" secondItem="dcM-Zy-Fwq" secondAttribute="top" constant="8" id="DQr-kc-uFa"/>
                                    <constraint firstAttribute="bottom" secondItem="GOe-CI-6MB" secondAttribute="bottom" constant="8" id="DfD-wa-qwg"/>
                                    <constraint firstItem="qoB-Dt-Pwa" firstAttribute="centerY" secondItem="PCJ-0N-vD9" secondAttribute="centerY" id="GDw-Y7-YQA"/>
                                    <constraint firstItem="ZAC-ZK-y6Z" firstAttribute="bottom" secondItem="Rj3-x6-vdV" secondAttribute="bottom" id="K5L-j0-v39"/>
                                    <constraint firstItem="k7h-vJ-HGJ" firstAttribute="trailing" secondItem="yOH-sF-YSD" secondAttribute="trailing" constant="5" id="KiW-X8-jY2"/>
                                    <constraint firstItem="PCJ-0N-vD9" firstAttribute="leading" secondItem="dcM-Zy-Fwq" secondAttribute="leading" id="NqV-Yi-ptq"/>
                                    <constraint firstItem="gIk-2C-EPJ" firstAttribute="trailing" secondItem="yOH-sF-YSD" secondAttribute="trailing" id="PCJ-91-mMS"/>
                                    <constraint firstItem="qoB-Dt-Pwa" firstAttribute="top" secondItem="dcM-Zy-Fwq" secondAttribute="top" constant="23" id="PMc-wt-had"/>
                                    <constraint firstItem="8hf-JA-2pt" firstAttribute="centerY" secondItem="GOe-CI-6MB" secondAttribute="centerY" id="R7e-xO-3kb"/>
                                    <constraint firstItem="GOe-CI-6MB" firstAttribute="leading" secondItem="PCJ-0N-vD9" secondAttribute="trailing" id="Suc-6J-HaU"/>
                                    <constraint firstItem="ZAC-ZK-y6Z" firstAttribute="top" secondItem="Rj3-x6-vdV" secondAttribute="top" id="UEg-0X-dBH"/>
                                    <constraint firstItem="8hf-JA-2pt" firstAttribute="height" secondItem="dcM-Zy-Fwq" secondAttribute="height" multiplier="0.41" id="YQ9-wW-gB2"/>
                                    <constraint firstItem="1JL-yZ-vHi" firstAttribute="top" secondItem="Rj3-x6-vdV" secondAttribute="top" constant="-5" id="b0M-Ku-6pQ"/>
                                    <constraint firstItem="gIk-2C-EPJ" firstAttribute="bottom" secondItem="yOH-sF-YSD" secondAttribute="bottom" id="f1v-g6-HsV"/>
                                    <constraint firstItem="1JL-yZ-vHi" firstAttribute="trailing" secondItem="Rj3-x6-vdV" secondAttribute="trailing" constant="5" id="gW7-X6-2ds"/>
                                    <constraint firstAttribute="height" constant="64" id="gdA-Dc-kFj"/>
                                    <constraint firstItem="gIk-2C-EPJ" firstAttribute="top" secondItem="yOH-sF-YSD" secondAttribute="top" id="l2Y-O2-hfV"/>
                                    <constraint firstItem="ZAC-ZK-y6Z" firstAttribute="leading" secondItem="Rj3-x6-vdV" secondAttribute="leading" id="lNt-x9-j7P"/>
                                    <constraint firstItem="k7h-vJ-HGJ" firstAttribute="top" secondItem="yOH-sF-YSD" secondAttribute="top" constant="-5" id="lVQ-JA-Gqi"/>
                                    <constraint firstItem="ZAC-ZK-y6Z" firstAttribute="trailing" secondItem="Rj3-x6-vdV" secondAttribute="trailing" id="lXO-OB-WAH"/>
                                    <constraint firstItem="GOe-CI-6MB" firstAttribute="leading" secondItem="qoB-Dt-Pwa" secondAttribute="trailing" constant="32" id="mgI-ru-crv"/>
                                    <constraint firstAttribute="bottom" secondItem="PCJ-0N-vD9" secondAttribute="bottom" id="ndE-Zx-aXk"/>
                                </constraints>
                            </view>
                            <stackView opaque="NO" contentMode="scaleToFill" axis="vertical" translatesAutoresizingMaskIntoConstraints="NO" id="TyU-EU-JUc">
                                <rect key="frame" x="0.0" y="112" width="414" height="784"/>
                                <subviews>
                                    <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="zTD-bM-cyX">
                                        <rect key="frame" x="0.0" y="0.0" width="414" height="108"/>
                                        <subviews>
                                            <collectionView clipsSubviews="YES" multipleTouchEnabled="YES" contentMode="scaleToFill" showsHorizontalScrollIndicator="NO" showsVerticalScrollIndicator="NO" dataMode="prototypes" translatesAutoresizingMaskIntoConstraints="NO" id="Wx2-rM-C6v">
                                                <rect key="frame" x="15" y="15" width="399" height="93"/>
                                                <color key="backgroundColor" systemColor="systemBackgroundColor"/>
                                                <collectionViewFlowLayout key="collectionViewLayout" scrollDirection="horizontal" minimumLineSpacing="10" minimumInteritemSpacing="10" id="bJQ-Ag-YOY">
                                                    <size key="itemSize" width="128" height="128"/>
                                                    <size key="headerReferenceSize" width="0.0" height="0.0"/>
                                                    <size key="footerReferenceSize" width="0.0" height="0.0"/>
                                                    <inset key="sectionInset" minX="0.0" minY="0.0" maxX="0.0" maxY="0.0"/>
                                                </collectionViewFlowLayout>
                                                <cells/>
                                            </collectionView>
                                            <view hidden="YES" contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="Vn7-jb-azc" customClass="ShimmerView" customModule="BabyHouse" customModuleProvider="target">
                                                <rect key="frame" x="15" y="0.0" width="399" height="98"/>
                                                <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                            </view>
                                        </subviews>
                                        <color key="backgroundColor" systemColor="systemBackgroundColor"/>
                                        <constraints>
                                            <constraint firstAttribute="bottom" secondItem="Vn7-jb-azc" secondAttribute="bottom" constant="10" id="2Ie-fC-nFk"/>
                                            <constraint firstItem="Vn7-jb-azc" firstAttribute="top" secondItem="zTD-bM-cyX" secondAttribute="top" id="GTr-r6-1Fp"/>
                                            <constraint firstItem="Wx2-rM-C6v" firstAttribute="top" secondItem="zTD-bM-cyX" secondAttribute="top" constant="15" id="P2T-jh-4FI"/>
                                            <constraint firstItem="Vn7-jb-azc" firstAttribute="trailing" secondItem="Wx2-rM-C6v" secondAttribute="trailing" id="ae6-31-zMV"/>
                                            <constraint firstAttribute="trailing" secondItem="Wx2-rM-C6v" secondAttribute="trailing" id="baG-j6-RpP"/>
                                            <constraint firstItem="Vn7-jb-azc" firstAttribute="leading" secondItem="Wx2-rM-C6v" secondAttribute="leading" id="eSI-hW-tl6"/>
                                            <constraint firstAttribute="bottom" secondItem="Wx2-rM-C6v" secondAttribute="bottom" id="ggV-MF-flk"/>
                                            <constraint firstAttribute="height" constant="108" id="tOE-U2-0gM"/>
                                            <constraint firstItem="Wx2-rM-C6v" firstAttribute="leading" secondItem="zTD-bM-cyX" secondAttribute="leading" constant="15" id="uiV-FX-iEl"/>
                                        </constraints>
                                    </view>
                                    <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="K6w-Fx-SES">
                                        <rect key="frame" x="0.0" y="108" width="414" height="60"/>
                                        <subviews>
                                            <collectionView clipsSubviews="YES" multipleTouchEnabled="YES" contentMode="scaleToFill" showsHorizontalScrollIndicator="NO" showsVerticalScrollIndicator="NO" dataMode="prototypes" translatesAutoresizingMaskIntoConstraints="NO" id="7uV-oP-7sZ">
                                                <rect key="frame" x="10" y="0.0" width="404" height="60"/>
                                                <color key="backgroundColor" systemColor="systemBackgroundColor"/>
                                                <collectionViewFlowLayout key="collectionViewLayout" scrollDirection="horizontal" minimumLineSpacing="10" minimumInteritemSpacing="10" id="h87-ps-ESe">
                                                    <size key="itemSize" width="128" height="128"/>
                                                    <size key="headerReferenceSize" width="0.0" height="0.0"/>
                                                    <size key="footerReferenceSize" width="0.0" height="0.0"/>
                                                    <inset key="sectionInset" minX="0.0" minY="0.0" maxX="0.0" maxY="0.0"/>
                                                </collectionViewFlowLayout>
                                                <cells/>
                                            </collectionView>
                                        </subviews>
                                        <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                        <constraints>
                                            <constraint firstAttribute="trailing" secondItem="7uV-oP-7sZ" secondAttribute="trailing" id="3c4-6p-Duj"/>
                                            <constraint firstAttribute="height" constant="60" id="N1S-Du-ciA"/>
                                            <constraint firstItem="7uV-oP-7sZ" firstAttribute="leading" secondItem="K6w-Fx-SES" secondAttribute="leading" constant="10" id="NEU-TY-d1d"/>
                                            <constraint firstAttribute="bottom" secondItem="7uV-oP-7sZ" secondAttribute="bottom" id="O9R-WQ-KRx"/>
                                            <constraint firstItem="7uV-oP-7sZ" firstAttribute="top" secondItem="K6w-Fx-SES" secondAttribute="top" id="Qz2-gb-LXz"/>
                                        </constraints>
                                    </view>
                                    <view hidden="YES" contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="yuh-cy-WJx">
                                        <rect key="frame" x="0.0" y="168" width="414" height="60"/>
                                        <subviews>
                                            <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="FCb-A9-inD">
                                                <rect key="frame" x="15" y="8" width="0.0" height="44"/>
                                                <fontDescription key="fontDescription" type="system" pointSize="17"/>
                                                <nil key="textColor"/>
                                                <nil key="highlightedColor"/>
                                            </label>
                                            <imageView clipsSubviews="YES" userInteractionEnabled="NO" contentMode="scaleAspectFit" horizontalHuggingPriority="251" verticalHuggingPriority="251" image="vuesax-outline-setting-4" translatesAutoresizingMaskIntoConstraints="NO" id="qWU-W4-06E">
                                                <rect key="frame" x="366" y="16" width="28" height="28"/>
                                                <constraints>
                                                    <constraint firstAttribute="height" constant="28" id="Qo8-YX-JWQ"/>
                                                    <constraint firstAttribute="width" constant="28" id="jCt-yO-rsz"/>
                                                </constraints>
                                            </imageView>
                                        </subviews>
                                        <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                        <constraints>
                                            <constraint firstAttribute="height" constant="60" id="0nB-fE-l6P"/>
                                            <constraint firstItem="qWU-W4-06E" firstAttribute="centerY" secondItem="FCb-A9-inD" secondAttribute="centerY" id="DiM-Ok-46u"/>
                                            <constraint firstItem="FCb-A9-inD" firstAttribute="leading" secondItem="yuh-cy-WJx" secondAttribute="leading" constant="15" id="THv-qN-hFF"/>
                                            <constraint firstItem="FCb-A9-inD" firstAttribute="top" secondItem="yuh-cy-WJx" secondAttribute="top" constant="8" id="TdR-HB-yQj"/>
                                            <constraint firstAttribute="bottom" secondItem="FCb-A9-inD" secondAttribute="bottom" constant="8" id="hfZ-gx-EWD"/>
                                            <constraint firstAttribute="trailing" secondItem="qWU-W4-06E" secondAttribute="trailing" constant="20" id="r0O-j7-6r6"/>
                                        </constraints>
                                    </view>
                                    <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="9vU-Vh-LsF">
                                        <rect key="frame" x="0.0" y="168" width="414" height="616"/>
                                        <subviews>
                                            <collectionView clipsSubviews="YES" multipleTouchEnabled="YES" contentMode="scaleToFill" showsHorizontalScrollIndicator="NO" showsVerticalScrollIndicator="NO" dataMode="prototypes" translatesAutoresizingMaskIntoConstraints="NO" id="MiS-0a-bvl">
                                                <rect key="frame" x="15" y="0.0" width="384" height="616"/>
                                                <color key="backgroundColor" systemColor="systemBackgroundColor"/>
                                                <collectionViewFlowLayout key="collectionViewLayout" minimumLineSpacing="10" minimumInteritemSpacing="10" id="MfG-N5-GZh">
                                                    <size key="itemSize" width="128" height="128"/>
                                                    <size key="headerReferenceSize" width="0.0" height="0.0"/>
                                                    <size key="footerReferenceSize" width="0.0" height="0.0"/>
                                                    <inset key="sectionInset" minX="0.0" minY="0.0" maxX="0.0" maxY="0.0"/>
                                                </collectionViewFlowLayout>
                                                <cells/>
                                            </collectionView>
                                        </subviews>
                                        <color key="backgroundColor" systemColor="systemBackgroundColor"/>
                                        <constraints>
                                            <constraint firstItem="MiS-0a-bvl" firstAttribute="top" secondItem="9vU-Vh-LsF" secondAttribute="top" id="FMV-4u-UPd"/>
                                            <constraint firstAttribute="bottom" secondItem="MiS-0a-bvl" secondAttribute="bottom" id="JP0-tR-hdP"/>
                                            <constraint firstItem="MiS-0a-bvl" firstAttribute="leading" secondItem="9vU-Vh-LsF" secondAttribute="leading" constant="15" id="OOy-b3-KDX"/>
                                            <constraint firstAttribute="trailing" secondItem="MiS-0a-bvl" secondAttribute="trailing" constant="15" id="RfO-hZ-SYG"/>
                                        </constraints>
                                    </view>
                                </subviews>
                            </stackView>
                        </subviews>
                        <viewLayoutGuide key="safeArea" id="iAm-X8-8Sl"/>
                        <color key="backgroundColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                        <constraints>
                            <constraint firstItem="dcM-Zy-Fwq" firstAttribute="top" secondItem="iAm-X8-8Sl" secondAttribute="top" id="A6A-qD-faQ"/>
                            <constraint firstItem="dcM-Zy-Fwq" firstAttribute="leading" secondItem="iAm-X8-8Sl" secondAttribute="leading" id="CTt-xR-4Oj"/>
                            <constraint firstItem="TyU-EU-JUc" firstAttribute="top" secondItem="x5x-vC-Uyq" secondAttribute="bottom" id="OPn-gt-6r1"/>
                            <constraint firstItem="x5x-vC-Uyq" firstAttribute="top" secondItem="v2m-Tb-FbK" secondAttribute="top" constant="-30" id="QIO-PB-f7M"/>
                            <constraint firstItem="TyU-EU-JUc" firstAttribute="leading" secondItem="iAm-X8-8Sl" secondAttribute="leading" id="Tf0-X5-wUE"/>
                            <constraint firstItem="iAm-X8-8Sl" firstAttribute="trailing" secondItem="x5x-vC-Uyq" secondAttribute="trailing" id="VHb-sY-SiB"/>
                            <constraint firstItem="x5x-vC-Uyq" firstAttribute="leading" secondItem="iAm-X8-8Sl" secondAttribute="leading" id="bQD-d5-bIW"/>
                            <constraint firstItem="x5x-vC-Uyq" firstAttribute="bottom" secondItem="dcM-Zy-Fwq" secondAttribute="bottom" id="bg3-a8-0FC"/>
                            <constraint firstAttribute="bottom" secondItem="TyU-EU-JUc" secondAttribute="bottom" id="keT-fJ-t4c"/>
                            <constraint firstItem="dcM-Zy-Fwq" firstAttribute="trailing" secondItem="iAm-X8-8Sl" secondAttribute="trailing" id="qhO-h0-oUB"/>
                            <constraint firstItem="iAm-X8-8Sl" firstAttribute="trailing" secondItem="TyU-EU-JUc" secondAttribute="trailing" id="tkc-th-QlG"/>
                        </constraints>
                    </view>
                    <connections>
                        <outlet property="cartLbl" destination="hVF-7u-pC9" id="ZAg-mD-Mwl"/>
                        <outlet property="cartView" destination="k7h-vJ-HGJ" id="qyr-UC-lNX"/>
                        <outlet property="catCollectionview" destination="Wx2-rM-C6v" id="3DX-z0-ZGs"/>
                        <outlet property="catShimmerview" destination="Vn7-jb-azc" id="zUf-ZR-OYy"/>
                        <outlet property="cattitleCollectionview" destination="7uV-oP-7sZ" id="Ohq-VN-S5M"/>
                        <outlet property="collectionView" destination="MiS-0a-bvl" id="6VG-ud-UZZ"/>
                        <outlet property="headerLbl" destination="GOe-CI-6MB" id="u4E-vk-bKi"/>
                        <outlet property="notiLbl" destination="WoO-le-l4k" id="zSH-pl-8wu"/>
                        <outlet property="notiView" destination="1JL-yZ-vHi" id="yrm-9P-wgM"/>
                        <outlet property="subCategoryview" destination="K6w-Fx-SES" id="r8c-mV-5m7"/>
                        <outlet property="subtitleLbl" destination="FCb-A9-inD" id="h6t-ac-nFJ"/>
                        <outlet property="top2View" destination="dcM-Zy-Fwq" id="fNt-nC-wyZ"/>
                        <outlet property="topZView" destination="x5x-vC-Uyq" id="IPE-Io-B3p"/>
                    </connections>
                </viewController>
                <placeholder placeholderIdentifier="IBFirstResponder" id="Zwt-2b-88H" userLabel="First Responder" customClass="UIResponder" sceneMemberID="firstResponder"/>
                <stackView opaque="NO" contentMode="scaleToFill" distribution="fillEqually" spacing="10" id="DyN-bp-xls">
                    <rect key="frame" x="0.0" y="0.0" width="61" height="26"/>
                    <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMaxY="YES"/>
                    <subviews>
                        <imageView clipsSubviews="YES" userInteractionEnabled="NO" contentMode="scaleAspectFit" horizontalHuggingPriority="251" verticalHuggingPriority="251" image="cart" translatesAutoresizingMaskIntoConstraints="NO" id="MOh-2b-23u">
                            <rect key="frame" x="0.0" y="0.0" width="25.5" height="96"/>
                            <constraints>
                                <constraint firstAttribute="width" secondItem="MOh-2b-23u" secondAttribute="height" multiplier="1:1" id="15Y-Me-JpO"/>
                            </constraints>
                        </imageView>
                        <imageView clipsSubviews="YES" userInteractionEnabled="NO" contentMode="scaleAspectFit" horizontalHuggingPriority="251" verticalHuggingPriority="251" image="notification-status" translatesAutoresizingMaskIntoConstraints="NO" id="qAM-VW-M7p">
                            <rect key="frame" x="35.5" y="0.0" width="25.5" height="96"/>
                        </imageView>
                    </subviews>
                    <viewLayoutGuide key="safeArea" id="Xp8-1D-oaO"/>
                </stackView>
            </objects>
            <point key="canvasLocation" x="-859.4202898550725" y="-49.553571428571423"/>
        </scene>
    </scenes>
    <resources>
        <image name="cart" width="24" height="24"/>
        <image name="leftarrowB" width="512" height="512"/>
        <image name="notification-status" width="96" height="96"/>
        <image name="vuesax-outline-setting-4" width="24" height="24"/>
        <systemColor name="systemBackgroundColor">
            <color white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
        </systemColor>
    </resources>
</document>
