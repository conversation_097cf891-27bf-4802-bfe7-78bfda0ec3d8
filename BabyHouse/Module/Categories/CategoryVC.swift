//
//  CategoryVC.swift
//  BabyHouse
//
//  Created by <PERSON> on 23/03/22.
//

import UIKit

struct CellData {
    var open: Bool = false
    var title: Categoryies
    var sectionData = [Categoryies]()
}

class CategoryVC: UIViewController {
    var id: String?
    var viewModel: CategoryVM?
    var isFrommenu: Bool = false
    var fromSearchcategoryclick = false
    var categoryID = ""
    var backtoHome: ((Bool) -> Void)?
    var categoryArray = [Categoryies]()
    @IBOutlet var headerLbl: UILabel!
    @IBOutlet var top2View: UIView!
    @IBOutlet var topZView: UIView!

    @IBOutlet var tableView: UITableView!
    @IBOutlet var cartLbl: UILabel!
    @IBOutlet var cartView: UIView!
    @IBOutlet var notiLbl: UILabel!
    @IBOutlet var notiView: UIView!
    var tableviewData = [CellData]()
    override func viewDidLoad() {
        super.viewDidLoad()
        setFont()
        tableView.register(CategoryTableViewCell.self)
        tableView.register(TitleTableViewCell.self)
        tableView.delegate = self
        tableView.dataSource = self
        getData()
        hideNavigationBar()
        tableView.contentInset = UIEdgeInsets(top: 15, left: 0, bottom: 60, right: 0)

        cartLbl.addCartCountObserver()
        notiLbl.addNotificationCountObserver()
    }

    fileprivate func setFont() {
        headerLbl.font = UIFont.with(size: 14, .bold)
    }

    override func viewDidLayoutSubviews() {
        cartView.cornerRadius = cartView.frame.height / 2
        notiView.cornerRadius = notiView.frame.height / 2
        top2View.round(corners: [.bottomLeft, .bottomRight], radius: 20)
        topZView.round(corners: [.bottomLeft, .bottomRight], radius: 20)
        topZView.gradient(colours: [Babyrose, Babyred])
    }

    @IBAction func showNotification(_ sender: Any) {
        let vc = NotificationVC.instantiate()
        vc.isFrommenu = true
        vc.viewModel = NotificationViewModel(with: 0)
        navigationController?.pushViewController(vc, animated: true)
    }

    @IBAction func showCart(_ sender: Any) {
        let vc = ShoppingBagVC.instantiate()
        // vc.backtoHome = true
        vc.viewModel = HomeVM(with: 0)
        navigationController?.pushViewController(vc, animated: true)
    }

    @IBAction func back(_ sender: Any) {
        if isFrommenu {
            navigationController?.popViewController(animated: true)
            return
        }
        backtoHome?(true)
    }
}

private extension CategoryVC {
    func getIndex() -> Int? {
        for (index, data) in tableviewData.enumerated() {
            if data.title.id == categoryID {
                return index
            }
        }

        return nil
    }

    func getData() {
        showActivity()

        viewModel?.categoryList(completion: { homeResponse in

            DispatchQueue.main.async {
                hideActivity()
                guard let home = homeResponse else {
                    let msg = LocalizationSystem.sharedInstance.localizedStringForKey(key: "http_error_text", comment: "")
                    let alert = LocalizationSystem.sharedInstance.localizedStringForKey(key: "no_doc_text", comment: "")
                    // self.showMsg?(msg)
                    self.tableView.backgroundText = alert
                    return
                }
                if home.status == 200 {
                    self.tableView.backgroundText = nil
                    self.categoryArray = home.data
                    if home.data.count > 0 {
                        for data in home.data {
                            let childrens = data.children
//                            if childrens.count > 0 {
                            self.tableviewData.append(CellData(title: data, sectionData: childrens))
//                            }
                        }
                        if self.tableviewData.count == 0 {
                            self.tableView.backgroundText = LocalizationSystem.sharedInstance.localizedStringForKey(key: "no_doc_text", comment: "")
                        }
                    } else if home.data.count == 0 {
                        self.tableView.backgroundText = LocalizationSystem.sharedInstance.localizedStringForKey(key: "no_doc_text", comment: "")
                    }
//                      if let id = self.id {
//                          for (index,data) in self.tableviewData.enumerated() {
//                              if data.title.id == id {
//
//                              }
//                          }
//                      }
                    self.tableView.reloadData()
                    if self.fromSearchcategoryclick == true {
                        guard let index = self.getIndex() else {
                            self.view.makeToast("Failed to find out a category")
                            return
                        }
                        self.tableviewData[index].open = true
                        let sections = IndexSet(integer: index)
                        UIView.performWithoutAnimation {
                            self.tableView.reloadSections(sections, with: .none)
                        }
                        let indexPath = IndexPath(row: 0, section: index)
                        self.tableView.scrollToRow(at: indexPath, at: .top, animated: true)
                    }

                    return
                }
                self.tableView.backgroundText = LocalizationSystem.sharedInstance.localizedStringForKey(key: "no_doc_text", comment: "")
                // self.showMsg?(home.messages)
            }
        })
    }
}

extension CategoryVC: UITableViewDataSource, UITableViewDelegate {
    func numberOfSections(in tableView: UITableView) -> Int {
        tableviewData.count
    }

    func tableView(_ tableView: UITableView, numberOfRowsInSection section: Int) -> Int {
        if tableviewData[section].open == true {
            return tableviewData[section].sectionData.count + 1
        } else {
            return 1
        }
//
    }

    func tableView(_ tableView: UITableView, heightForFooterInSection section: Int) -> CGFloat {
        return 0
    }

    func tableView(_ tableView: UITableView, heightForHeaderInSection section: Int) -> CGFloat {
        return CGFloat.leastNonzeroMagnitude
    }

    func tableView(_ tableView: UITableView, heightForRowAt indexPath: IndexPath) -> CGFloat {
        if indexPath.row == 0 {
            return 140
        }
        return UITableView.automaticDimension
    }

    func tableView(_ tableView: UITableView, cellForRowAt indexPath: IndexPath) -> UITableViewCell {
        if indexPath.row == 0 {
            let cell: TitleTableViewCell = tableView.dequeueReusableCell(for: indexPath)

            let cellData: CellData = tableviewData[indexPath.section]

            cell.tittleLbl.text = cellData.title.name

            let url = cellData.title.catIcon
            cell.iconImg.sd_setImage(with: URL(string: url), placeholderImage: UIImage(named: "dbg"))

            cell.sideImg.isHidden = cellData.title.children.isEmpty

            if cellData.title.children.count > 0 {}
            if cellData.open == true {
                cell.select()
                // cell.bottomConstraint.constant = 10
            } else {
                cell.deselect()
                // cell.bottomConstraint.constant = 10
            }
            return cell
        } else {
            let cell: CategoryTableViewCell = tableView.dequeueReusableCell(for: indexPath)
            cell.category = tableviewData[indexPath.section].sectionData[indexPath.row - 1]
            if tableviewData[indexPath.section].sectionData.count == indexPath.row {
                DispatchQueue.main.async {
                    cell.parentView.round(corners: [.bottomLeft, .bottomRight], radius: 20)
                }
            }
            return cell
        }
    }

    func tableView(_ tableView: UITableView, didSelectRowAt indexPath: IndexPath) {
        if indexPath.row == 0 {
            if tableviewData[indexPath.section].open == true {
                tableviewData[indexPath.section].open = false
                let sections = IndexSet(integer: indexPath.section)
                UIView.performWithoutAnimation {
                    self.tableView.reloadSections(sections, with: .none)
                }
            } else {
                if tableviewData[indexPath.section].title.children.count > 0 {
                    tableviewData[indexPath.section].open = true
                    let sections = IndexSet(integer: indexPath.section)
                    UIView.performWithoutAnimation {
                        self.tableView.reloadSections(sections, with: .none)
                    }
                } else {
                    let vc = CategoryprodutsVC.instantiate()
//                    vc.childrenCategory = current
                    vc.parentSelected = tableviewData[indexPath.section].title
                    vc.viewModel = HomeVM(with: 0)
                    vc.categoryArray = categoryArray
                    navigationController?.pushViewController(vc, animated: true)
                }
            }
        } else {
            let current = tableviewData[indexPath.section].sectionData[indexPath.row - 1]
            print(current)
            let vc = CategoryprodutsVC.instantiate()
            vc.childrenCategory = current
            vc.parentSelected = tableviewData[indexPath.section].title
            vc.viewModel = HomeVM(with: 0)
            vc.categoryArray = categoryArray
            navigationController?.pushViewController(vc, animated: true)
        }
    }
}

extension CategoryVC: Storyboarded {
    static var storyboard: Storyboard { .category }
}
