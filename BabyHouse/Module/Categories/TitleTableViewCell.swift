//
//  TitleTableViewCell.swift
//  shopping
//
//  Created by <PERSON><PERSON> on 13/10/20.
//  Copyright © 2020 macpot. All rights reserved.
//

import UIKit

class TitleTableViewCell: UITableViewCell {

   
    @IBOutlet weak var dimRoseview: UIView!
    @IBOutlet weak var bgViewHeader: UIView!
    
    @IBOutlet weak var sideImg: UIImageView!
    
    @IBOutlet weak var iconImg: UIImageView!
    @IBOutlet weak var bgView: UIView!
    @IBOutlet weak var bottomConstraint: NSLayoutConstraint!
    @IBOutlet weak var tittleLbl: UILabel!
    
   
    override func awakeFromNib() {
        super.awakeFromNib()
        // Initialization code
        
        tittleLbl.font = UIFont.with(size:13,.bold)
        
        selectionStyle = .none
      
    }
 
    override func layoutSubviews() {
        DispatchQueue.main.async {
           
            self.bgView.round(corners:[.bottomLeft,.bottomRight,.topLeft,.topRight], radius: 20)
            self.bgViewHeader.round(corners:[.bottomLeft,.bottomRight,.topLeft,.topRight], radius: 20)
            self.bgViewHeader.horizontalgradient(colours:[G5,G2])
            self.bgViewHeader.addShadow()
            
            
           
          
            
        }
        
    }
    override func setSelected(_ selected: Bool, animated: Bool) {
        super.setSelected(selected, animated: animated)

        // Configure the view for the selected state
    }
    func select(){
        sideImg.setImage(UIImage(named:"minus"))
        dimRoseview.isHidden = false
    }
    func deselect(){
       
     
        sideImg.setImage(UIImage(named:"outline-add"))
        dimRoseview.isHidden = true
    }
}
extension TitleTableViewCell:ReuseIdentifying,NibLoadable {}
extension UIImageView{
    func setImage(_ image: UIImage?, animated: Bool = true) {
        let duration = animated ? 0.3 : 0.0
        UIView.transition(with: self, duration: duration, options: .transitionCrossDissolve, animations: {
            self.image = image
        }, completion: nil)
    }
}
