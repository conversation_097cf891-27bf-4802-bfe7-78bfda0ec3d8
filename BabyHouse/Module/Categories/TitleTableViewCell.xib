<?xml version="1.0" encoding="UTF-8"?>
<document type="com.apple.InterfaceBuilder3.CocoaTouch.XIB" version="3.0" toolsVersion="22154" targetRuntime="iOS.CocoaTouch" propertyAccessControl="none" useAutolayout="YES" useTraitCollections="YES" useSafeAreas="YES" colorMatched="YES">
    <device id="retina6_1" orientation="portrait" appearance="light"/>
    <dependencies>
        <plugIn identifier="com.apple.InterfaceBuilder.IBCocoaTouchPlugin" version="22130"/>
        <capability name="Safe area layout guides" minToolsVersion="9.0"/>
        <capability name="System colors in document resources" minToolsVersion="11.0"/>
        <capability name="documents saved in the Xcode 8 format" minToolsVersion="8.0"/>
    </dependencies>
    <objects>
        <placeholder placeholderIdentifier="IBFilesOwner" id="-1" userLabel="File's Owner"/>
        <placeholder placeholderIdentifier="IBFirstResponder" id="-2" customClass="UIResponder"/>
        <tableViewCell contentMode="scaleToFill" selectionStyle="default" indentationWidth="10" rowHeight="140" id="KGk-i7-Jjw" customClass="TitleTableViewCell" customModule="BabyHouse" customModuleProvider="target">
            <rect key="frame" x="0.0" y="0.0" width="542" height="138"/>
            <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMaxY="YES"/>
            <tableViewCellContentView key="contentView" opaque="NO" clipsSubviews="YES" multipleTouchEnabled="YES" contentMode="center" tableViewCell="KGk-i7-Jjw" id="H2p-sc-9uM">
                <rect key="frame" x="0.0" y="0.0" width="542" height="138"/>
                <autoresizingMask key="autoresizingMask"/>
                <subviews>
                    <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="G4t-MV-3bm">
                        <rect key="frame" x="0.0" y="0.0" width="542" height="138"/>
                        <subviews>
                            <view hidden="YES" contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="gXj-Qq-EQi">
                                <rect key="frame" x="10" y="108" width="522" height="30"/>
                                <color key="backgroundColor" red="0.98859971759999998" green="0.91905266050000001" blue="0.93218159680000001" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                                <constraints>
                                    <constraint firstAttribute="height" constant="30" id="pXf-TD-sVN"/>
                                </constraints>
                            </view>
                            <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="bu7-x8-eB0">
                                <rect key="frame" x="10" y="10" width="522" height="128"/>
                                <color key="backgroundColor" systemColor="systemBackgroundColor"/>
                            </view>
                            <view clipsSubviews="YES" contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="sFy-1a-Rzl">
                                <rect key="frame" x="10" y="0.0" width="522" height="138"/>
                                <subviews>
                                    <imageView clipsSubviews="YES" userInteractionEnabled="NO" contentMode="scaleAspectFit" horizontalHuggingPriority="251" verticalHuggingPriority="251" image="logo-head-white" translatesAutoresizingMaskIntoConstraints="NO" id="tkm-Zb-Azy">
                                        <rect key="frame" x="370.5" y="20.5" width="96.5" height="97"/>
                                        <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                        <constraints>
                                            <constraint firstAttribute="width" secondItem="tkm-Zb-Azy" secondAttribute="height" multiplier="1:1" id="U4B-47-zNx"/>
                                        </constraints>
                                    </imageView>
                                    <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Christmas" textAlignment="natural" lineBreakMode="tailTruncation" numberOfLines="3" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="6y0-Rs-urP">
                                        <rect key="frame" x="30" y="10" width="330.5" height="118"/>
                                        <fontDescription key="fontDescription" type="system" pointSize="17"/>
                                        <color key="textColor" red="0.71372549019999998" green="0.21176470589999999" blue="0.30588235289999999" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                                        <nil key="highlightedColor"/>
                                    </label>
                                    <imageView clipsSubviews="YES" userInteractionEnabled="NO" contentMode="scaleAspectFit" horizontalHuggingPriority="251" verticalHuggingPriority="251" image="outline-add" translatesAutoresizingMaskIntoConstraints="NO" id="Zdp-YJ-JnV">
                                        <rect key="frame" x="482" y="56.5" width="25" height="25"/>
                                        <color key="backgroundColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                        <constraints>
                                            <constraint firstAttribute="width" constant="25" id="99d-74-O9T"/>
                                            <constraint firstAttribute="height" constant="25" id="npk-P3-ueO"/>
                                        </constraints>
                                        <userDefinedRuntimeAttributes>
                                            <userDefinedRuntimeAttribute type="number" keyPath="layer.cornerRadius">
                                                <real key="value" value="12.5"/>
                                            </userDefinedRuntimeAttribute>
                                            <userDefinedRuntimeAttribute type="boolean" keyPath="layer.clipsToBounds" value="YES"/>
                                        </userDefinedRuntimeAttributes>
                                    </imageView>
                                </subviews>
                                <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                <constraints>
                                    <constraint firstItem="6y0-Rs-urP" firstAttribute="leading" secondItem="sFy-1a-Rzl" secondAttribute="leading" constant="30" id="3Ng-SR-JJz"/>
                                    <constraint firstAttribute="trailing" secondItem="Zdp-YJ-JnV" secondAttribute="trailing" constant="15" id="ACe-xq-MjG"/>
                                    <constraint firstItem="6y0-Rs-urP" firstAttribute="top" secondItem="sFy-1a-Rzl" secondAttribute="top" constant="10" id="L2y-xU-s6f"/>
                                    <constraint firstItem="Zdp-YJ-JnV" firstAttribute="centerY" secondItem="sFy-1a-Rzl" secondAttribute="centerY" id="MD6-S3-EVC"/>
                                    <constraint firstItem="tkm-Zb-Azy" firstAttribute="leading" secondItem="6y0-Rs-urP" secondAttribute="trailing" constant="10" id="OK7-11-Rta"/>
                                    <constraint firstItem="tkm-Zb-Azy" firstAttribute="height" secondItem="sFy-1a-Rzl" secondAttribute="height" multiplier="0.7" id="Uiq-hz-EXL"/>
                                    <constraint firstItem="tkm-Zb-Azy" firstAttribute="centerY" secondItem="6y0-Rs-urP" secondAttribute="centerY" id="ZRM-yi-f9b"/>
                                    <constraint firstAttribute="bottom" secondItem="6y0-Rs-urP" secondAttribute="bottom" constant="10" id="ndz-gm-3ST"/>
                                    <constraint firstItem="Zdp-YJ-JnV" firstAttribute="leading" secondItem="tkm-Zb-Azy" secondAttribute="trailing" constant="15" id="sxi-JY-oO1"/>
                                </constraints>
                                <userDefinedRuntimeAttributes>
                                    <userDefinedRuntimeAttribute type="number" keyPath="layer.cornerRadius">
                                        <integer key="value" value="20"/>
                                    </userDefinedRuntimeAttribute>
                                    <userDefinedRuntimeAttribute type="boolean" keyPath="layer.clipsToBounds" value="YES"/>
                                </userDefinedRuntimeAttributes>
                            </view>
                        </subviews>
                        <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                        <constraints>
                            <constraint firstItem="gXj-Qq-EQi" firstAttribute="trailing" secondItem="sFy-1a-Rzl" secondAttribute="trailing" id="3CW-xS-TSu"/>
                            <constraint firstAttribute="trailing" secondItem="sFy-1a-Rzl" secondAttribute="trailing" constant="10" id="3od-C6-I7m"/>
                            <constraint firstAttribute="trailing" secondItem="bu7-x8-eB0" secondAttribute="trailing" constant="10" id="DJU-J4-N93"/>
                            <constraint firstItem="sFy-1a-Rzl" firstAttribute="leading" secondItem="G4t-MV-3bm" secondAttribute="leading" constant="10" id="Ig3-vh-fo5"/>
                            <constraint firstItem="gXj-Qq-EQi" firstAttribute="bottom" secondItem="sFy-1a-Rzl" secondAttribute="bottom" id="Itx-Lf-7oi"/>
                            <constraint firstItem="gXj-Qq-EQi" firstAttribute="leading" secondItem="sFy-1a-Rzl" secondAttribute="leading" id="Vnp-jp-jHS"/>
                            <constraint firstItem="bu7-x8-eB0" firstAttribute="leading" secondItem="G4t-MV-3bm" secondAttribute="leading" constant="10" id="Wqg-X4-x9G"/>
                            <constraint firstAttribute="bottom" secondItem="bu7-x8-eB0" secondAttribute="bottom" id="n8a-TA-osD"/>
                            <constraint firstAttribute="bottom" secondItem="sFy-1a-Rzl" secondAttribute="bottom" id="qSH-YB-A4h"/>
                            <constraint firstItem="sFy-1a-Rzl" firstAttribute="top" secondItem="G4t-MV-3bm" secondAttribute="top" id="vS1-QQ-Q1d"/>
                            <constraint firstItem="bu7-x8-eB0" firstAttribute="top" secondItem="G4t-MV-3bm" secondAttribute="top" constant="10" id="xSs-Lc-5an"/>
                        </constraints>
                    </view>
                </subviews>
                <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                <constraints>
                    <constraint firstAttribute="bottom" secondItem="G4t-MV-3bm" secondAttribute="bottom" id="OcC-3e-PnB"/>
                    <constraint firstAttribute="trailing" secondItem="G4t-MV-3bm" secondAttribute="trailing" id="ShJ-B3-eVm"/>
                    <constraint firstItem="G4t-MV-3bm" firstAttribute="top" secondItem="H2p-sc-9uM" secondAttribute="top" id="j45-WP-h4L"/>
                    <constraint firstItem="G4t-MV-3bm" firstAttribute="leading" secondItem="H2p-sc-9uM" secondAttribute="leading" id="u2o-VH-qZq"/>
                </constraints>
            </tableViewCellContentView>
            <viewLayoutGuide key="safeArea" id="njF-e1-oar"/>
            <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
            <connections>
                <outlet property="bgView" destination="sFy-1a-Rzl" id="Ycm-ys-D6B"/>
                <outlet property="bgViewHeader" destination="bu7-x8-eB0" id="Zc6-Jv-X2q"/>
                <outlet property="bottomConstraint" destination="qSH-YB-A4h" id="rCY-OX-whu"/>
                <outlet property="dimRoseview" destination="gXj-Qq-EQi" id="Od8-iO-a4K"/>
                <outlet property="iconImg" destination="tkm-Zb-Azy" id="Zrb-zP-mMA"/>
                <outlet property="sideImg" destination="Zdp-YJ-JnV" id="R3j-38-Drh"/>
                <outlet property="tittleLbl" destination="6y0-Rs-urP" id="5r2-oo-bRM"/>
            </connections>
            <point key="canvasLocation" x="115.94202898550725" y="75"/>
        </tableViewCell>
    </objects>
    <resources>
        <image name="logo-head-white" width="140" height="98"/>
        <image name="outline-add" width="24" height="24"/>
        <systemColor name="systemBackgroundColor">
            <color white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
        </systemColor>
    </resources>
</document>
