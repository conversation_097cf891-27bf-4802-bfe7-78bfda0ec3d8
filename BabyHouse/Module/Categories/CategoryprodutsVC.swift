//
//  CategoryprodutsVC.swift
//  BabyHouse
//
//  Created by Notetech on 01/06/22.
//

import UIKit

class CategoryprodutsVC: UIViewController {
    var viewModel: HomeVM?

    @IBOutlet var cartLbl: UILabel!
    @IBOutlet var cartView: UIView!
    @IBOutlet var notiLbl: UILabel!
    @IBOutlet var notiView: UIView!
    @IBOutlet var subtitleLbl: UILabel!
    @IBOutlet var cattitleCollectionview: UICollectionView!
    @IBOutlet var catShimmerview: ShimmerView!
    @IBOutlet var catCollectionview: UICollectionView!
    @IBOutlet var headerLbl: UILabel!
    @IBOutlet var topZView: UIView!
    var categoryArray = [Categoryies]()
    var parentSelected: Categoryies?
    var childrenCategory: Categoryies?
    @IBOutlet var top2View: UIView!
    @IBOutlet var collectionView: UICollectionView!
    var products = [Categoryproduct]()
    let perpage = "50000000000000000"

    @IBOutlet var subCategoryview: UIView!

    var homeVM: HomeViewModel = .init()

    override func viewDidLoad() {
        super.viewDidLoad()
        headerLbl.font = UIFont.with(size: 14, .bold)
        subtitleLbl.font = UIFont.with(size: 14, .medium)
        subtitleLbl.textColor = G3
        collectionView.dataSource = self
        collectionView.delegate = self
        collectionView.register(FeatureCollectionViewCell.self)
        collectionView.delegate = self
        collectionView.dataSource = self
        collectionView.contentInset = UIEdgeInsets(top: 15, left: 0, bottom: 60, right: 0)

        catCollectionview.register(CategoryCollectionViewCell.self)
        catCollectionview.delegate = self
        catCollectionview.dataSource = self
        cattitleCollectionview.register(CattitleCollectionViewCell.self)
        cattitleCollectionview.delegate = self
        cattitleCollectionview.dataSource = self
        catShimmerview.startAnimating()

        cartLbl.addCartCountObserver()
        notiLbl.addNotificationCountObserver()

        addSwiftUIView(viewModel: homeVM)

        DispatchQueue.main.asyncAfter(deadline: .now() + 0.3) {
            self.catShimmerview.isHidden = true
        }
        let title = parentSelected?.name ?? ""
//        headerLbl.text =  title + " Costumes"
        headerLbl.text = title
        subtitleLbl.text = "All " + title + " Costumes"
        getData()

        setFlowLayout()
    }

    func setFlowLayout() {
        // Define Layout here
        let layout: UICollectionViewFlowLayout = TopAlignedCollectionViewFlowLayout()
     
        layout.scrollDirection = .vertical

        // Provide Width and Height According to your need
        let width = UIScreen.main.bounds.width / 4
        let height = UIScreen.main.bounds.height / 10
        layout.itemSize = CGSize(width: width, height: height)

        // For Adjusting the cells spacing
        layout.minimumInteritemSpacing = 5
        layout.minimumLineSpacing = 5

        collectionView!.collectionViewLayout = layout
    }

    @IBAction func showNotification(_ sender: Any) {
        let vc = NotificationVC.instantiate()
        vc.isFrommenu = true
        vc.viewModel = NotificationViewModel(with: 0)
        navigationController?.pushViewController(vc, animated: true)
    }

    @IBAction func showCart(_ sender: Any) {
        let vc = ShoppingBagVC.instantiate()
        // vc.backtoHome = true
        vc.viewModel = HomeVM(with: 0)
        navigationController?.pushViewController(vc, animated: true)
    }

    override func viewDidLayoutSubviews() {
        DispatchQueue.main.async {
            self.cartView.cornerRadius = self.cartView.frame.height / 2
            self.notiView.cornerRadius = self.notiView.frame.height / 2
            self.top2View.round(corners: [.bottomLeft, .bottomRight], radius: 20)
            self.topZView.round(corners: [.bottomLeft, .bottomRight], radius: 20)
            self.topZView.gradient(colours: [Babyrose, Babyred])
            self.topZView.setShadow()
        }
    }

    @IBAction func back(_ sender: Any) {
        navigationController?.popViewController(animated: true)
    }
}

private extension CategoryprodutsVC {
    func onEnquirySend(_ model: SendEnquiryModel) {
        postEnquiryData(model)
        homeVM.sendEnquiryModel = nil
    }

    func postEnquiryData(_ model: SendEnquiryModel) {
        showActivity()
        viewModel?.postEnquiry(model: model, completion: { _, message in
            Utilities.enQueue {
                hideActivity()
                Utilities.showToast(message)
            }
        })
    }
}

private extension CategoryprodutsVC {
    func getData(onCompletion: (() -> Void)? = nil) {
        showActivity()
        let params = Catproductparam(brand: "", min_price: "", max_price: "", attributes: "", per_page: perpage, sort: "", category: childrenCategory?.id ?? parentSelected?.id ?? "0", search_key: "")
        viewModel?.categotyProducts(data: params, completion: { homeResponse in

            DispatchQueue.main.async {
                hideActivity()
                guard let home = homeResponse else {
                    let msg = LocalizationSystem.sharedInstance.localizedStringForKey(key: "http_error_text", comment: "")
                    self.collectionView.backgroundText = msg
                    return
                }
                if home.status == 200, home.error == false {
                    if let products = home.data.products {
                        self.products = products
                        if self.products.count == 0 {
                            let msg = LocalizationSystem.sharedInstance.localizedStringForKey(key: "no_prpoducts_text", comment: "")
                            self.collectionView.backgroundText = msg
                        } else {
                            self.collectionView.backgroundText = nil
                        }
                        self.collectionView.reloadData()
                        self.cattitleCollectionview.reloadData()
                        self.onScrollCVCell()
                        onCompletion?()
                        return
                    }
                }
                let msg = LocalizationSystem.sharedInstance.localizedStringForKey(key: "no_prpoducts_text", comment: "")
                self.collectionView.backgroundText = msg
                self.collectionView.backgroundText = home.messages
                self.products = [Categoryproduct]()
                self.collectionView.reloadData()
            }
        })
    }

    func onScrollCVCell() {
        if let index = categoryArray.firstIndex(where: { $0.id == parentSelected?.id }) {
            catCollectionview.scrollToItem(at: IndexPath(row: index, section: 0), at: .centeredHorizontally, animated: true)
        } else {
            print(" is not in the list")
        }

        if parentSelected?.children.count ?? 0 > 0 {
            if let index = parentSelected?.children.firstIndex(where: { $0.id == childrenCategory?.id }) {
                cattitleCollectionview.scrollToItem(at: IndexPath(row: index, section: 0), at: .centeredHorizontally, animated: true)
            } else {
                print(" is not in the list")
            }
        }
    }
}

extension CategoryprodutsVC: UICollectionViewDelegate, UICollectionViewDataSource {
    func collectionView(_ collectionView: UICollectionView, numberOfItemsInSection section: Int) -> Int {
        if collectionView == catCollectionview {
            return categoryArray.count
        } else if collectionView == cattitleCollectionview {
            return parentSelected?.children.count ?? 0
        }
        return products.count
    }

    func collectionView(_ collectionView: UICollectionView, cellForItemAt indexPath: IndexPath) -> UICollectionViewCell {
        if collectionView == catCollectionview {
            let cell: CategoryCollectionViewCell = collectionView.dequeueReusableCell(for: indexPath)
            cell.parentSelectedID = parentSelected?.id

            cell.category1 = categoryArray[indexPath.row]
            subCategoryview.isHidden = parentSelected?.children.isEmpty ?? true
            return cell
        } else if collectionView == cattitleCollectionview {
            let cell: CattitleCollectionViewCell = collectionView.dequeueReusableCell(for: indexPath)

            cell.nameLbl.text = parentSelected?.children[indexPath.row].name ?? ""

            if childrenCategory?.id == parentSelected?.children[indexPath.row].id {
                cell.nameLbl.textColor = UIColor.white
                cell.mainView.backgroundColor = G2
            } else {
                cell.nameLbl.textColor = G2
                cell.mainView.backgroundColor = .clear

                cell.mainView.layer.borderColor = G2.cgColor
                cell.mainView.layer.borderWidth = 0.8
            }
            return cell
        }

        let cell: FeatureCollectionViewCell = collectionView.dequeueReusableCell(for: indexPath)
        let product: Categoryproduct = products[indexPath.row]
        cell.feature1 = product
        let isProductAvailable: Bool = product.isProductAvailable == 1
        cell.soldOutView.isHidden = isProductAvailable
        cell.sendEnquiryButton.isHidden = isProductAvailable
        cell.onSendEnquiry = { _, product in

            if self.isLoggedIn(message: "Please login to send enquiry") {
                self.homeVM.sendEnquiryModel = SendEnquiryModel(productID: product.productID ?? "", productVariantID: product.variantID ?? "", onSend: self.onEnquirySend)
            }
        }

        cell.addtoCart.tag = indexPath.row
        cell.clickedCart = { index in

            var productvariableID: String?
            productvariableID = self.products[index].variantID
            guard let varID = productvariableID,!varID.isEmpty else {
                return
            }

            self.addtoCart(cart: AddToCart(product_variant_id: varID, quantity: "1"))
        }
        cell.wishlistBtn.tag = indexPath.row
        cell.clickedWishlist = { index, ops in
            print(ops)
            var productID: String?
            var productVarientID: String?
            productID = self.products[index].productID
            productVarientID = self.products[index].variantID
            guard let id = productID,!id.isEmpty, let varid = productVarientID,!varid.isEmpty else { return }
            switch ops {
            case .add: self.addWishlist(wishlist: AddWishlist(product_id: id, product_variant_id: varid))
            case .remove: self.deleteFromwishlist(cart: AddWishlist(product_id: id, product_variant_id: varid))
            }
        }
        return cell
    }

    func collectionView(_ collectionView: UICollectionView, didSelectItemAt indexPath: IndexPath) {
        if collectionView == catCollectionview {
            childrenCategory = nil
            let selectedParentCategory = categoryArray[indexPath.row]
            if parentSelected?.id == selectedParentCategory.id { deselectSubCategory(); return }

            parentSelected = selectedParentCategory
            headerLbl.text = parentSelected?.name

//            if parentSelected?.children.count == 0 {
//                subCategoryview.isHidden = true
//                let msg = LocalizationSystem.sharedInstance.localizedStringForKey(key: "no_prpoducts_text", comment: "")
//                self.collectionView.backgroundText = msg
//                products = [Categoryproduct]()
//                self.collectionView.reloadData()
//            }
//            else {
//                subCategoryview.isHidden = false
//                childrenCategory = parentSelected?.children.first
//                getData()
//            }
//            if !(parentSelected?.children.isEmpty ?? true){ childrenCategory = parentSelected?.children.first }

            getData()
            catCollectionview.reloadData()
            collectionView.scrollToItem(at: indexPath, at: .centeredHorizontally, animated: true)
            return
        } else if collectionView == cattitleCollectionview {
            let selectedChildCategory = parentSelected?.children[indexPath.row]
            if childrenCategory?.id == selectedChildCategory?.id { deselectSubCategory(); return }
            childrenCategory = selectedChildCategory
            getData()
            collectionView.scrollToItem(at: indexPath, at: .centeredHorizontally, animated: true)
            return
        }

//        let vc = ProductdetailVC.instantiate()
//        vc.viewModel = HomeVM(with: 0)
//        vc.productID = products[indexPath.row].productID
//        navigationController?.pushViewController(vc, animated: true)

        let product = products[indexPath.row]
        navigationController?.pushProductDetailsPage(id: product.productID)
    }

    func deselectSubCategory() {
        childrenCategory = nil
        if parentSelected?.children.count != 0 { getData() }
    }
}

extension CategoryprodutsVC: UICollectionViewDelegateFlowLayout {
    func collectionView(_ collectionView: UICollectionView, layout collectionViewLayout: UICollectionViewLayout, minimumLineSpacingForSectionAt section: Int) -> CGFloat {
        if collectionView == catCollectionview {
            0.0
        }
        return 0.0
    }

    func collectionView(_ collectionView: UICollectionView, layout collectionViewLayout: UICollectionViewLayout, minimumInteritemSpacingForSectionAt section: Int) -> CGFloat {
        return 0.0
    }

    func collectionView(_ collectionView: UICollectionView, layout collectionViewLayout: UICollectionViewLayout, sizeForItemAt indexPath: IndexPath) -> CGSize {
        if collectionView == catCollectionview {
//            categoryArray
            let label = UILabel(frame: CGRect.zero)
            let name = categoryArray[indexPath.row].name
            label.text = name
            label.sizeToFit()

            var width = name.width(withConstrainedHeight: collectionView.frame.height, font: UIFont.with(size: 12, .regular)) + 30
            if width < 85 { width = 85 }
            else if width > 85 {
                width = width - 10
            }
            return CGSize(width: width, height: 95)
        } else if collectionView == cattitleCollectionview {
            let name = parentSelected?.children[indexPath.row].name ?? ""
            let width = name.width(withConstrainedHeight: collectionView.frame.height, font: UIFont.with(size: 12, .regular)) + 40
            return CGSize(width: width, height: collectionView.frame.height)
        }

//        let cell: FeatureCollectionViewCell = collectionView.dequeueReusableCell(for: indexPath)

        let product = products[indexPath.row]
        let isProductAvailable: Bool = product.isProductAvailable == 1
//        cell.soldOutView.isHidden = isProductAvailable
//        cell.sendEnquiryButton.isHidden = isProductAvailable

        let width = collectionView.frame.width - 20
        return CGSize(width: width / 2, height: isProductAvailable ? 240.0.relativeHeight : 290.0.relativeHeight)
    }
}

private extension CategoryprodutsVC {
    func addWishlist(wishlist: AddWishlist) {
        guard let _ = UserData.getuserInfo() else {
            showAlert(title: "Warning", message: "Please login to add to Wishlist.")
            return
        }

        showActivity()
        viewModel?.addwishlist(wishlist: wishlist, completion: { data in

            DispatchQueue.main.async {
                hideActivity()
                guard let response = data else {
                    let msg = LocalizationSystem.sharedInstance.localizedStringForKey(key: "http_error_text", comment: "")
                    self.view.makeToast(msg)
                    return
                }
                if response.status == 200 {
                    // Cartsupport.shared.cartCount()
                    self.getData()
                }
                self.view.makeToast(response.messages ?? "")
            }
        })
    }

    func deleteFromwishlist(cart: AddWishlist) {
        showActivity()
        viewModel?.deleteFromWishlist(data: cart, completion: { data in
            DispatchQueue.main.async {
                hideActivity()
                guard let response = data else {
                    let msg = LocalizationSystem.sharedInstance.localizedStringForKey(key: "http_error_text", comment: "")
                    self.view.makeToast(msg)
                    return
                }
                if response.status == 200 {
                    // Cartsupport.shared.cartCount()
                    self.getData()
                }
                self.view.makeToast(response.messages ?? "")
            }
        })
    }

    func addtoCart(cart: AddToCart) {
        showActivity()
        viewModel?.addCart(cartdata: cart, completion: { data in
            hideActivity()

            DispatchQueue.main.async {
                hideActivity()
                guard let response = data else {
                    let msg = LocalizationSystem.sharedInstance.localizedStringForKey(key: "http_error_text", comment: "")
                    self.view.makeToast(msg)
                    return
                }
                if response.status == 200, response.error == false {
                    self.view.makeToast(response.messages)
                    return
                }
                self.view.makeToast(response.messages ?? "")
            }
        })
    }
}

extension CategoryprodutsVC: Storyboarded {
    static var storyboard: Storyboard { .category }
}
