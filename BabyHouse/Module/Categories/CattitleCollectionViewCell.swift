//
//  CattitleCollectionViewCell.swift
//  BabyHouse
//
//  Created by Notetech on 07/06/22.
//

import UIKit

class CattitleCollectionViewCell: UICollectionViewCell {

    @IBOutlet weak var mainView: UIView!
    @IBOutlet weak var nameLbl: UILabel!
    override func awakeFromNib() {
        super.awakeFromNib()
        nameLbl.font = UIFont.with(size:12,.bold)
       
    }
    override func layoutSubviews() {
        DispatchQueue.main.async {
            self.mainView.cornerRadius = self.mainView.frame.height / 2
        }
    }
}
extension CattitleCollectionViewCell:ReuseIdentifying,NibLoadable {}
