//
//  CategoryVM.swift
//  BabyHouse
//
//  Created by <PERSON> on 23/03/22.
//

import Foundation

class CategoryVM: BaseViewModel {
    /// DoctorId
    var dataSource: Int?
  
    required init(with dataSource: Int?) {
        self.dataSource = dataSource
    }
    
    func categoryList(completion: ((CategoryResponse?) -> Void)?) {
        guard let tokenData = GuestData.getUserInfo() else{ return}
        var token =  "Bearer " + tokenData.accessToken
        if  let  tokenValue = UserData.getuserInfo()?.accessToken {
            token = "Bearer " + tokenValue
        }
        HomeRequest.categoryList(token).response(){ (result: Result<CategoryResponse, NError>)
            in
            
            switch result {
            case .success(let response):
                completion?(response)
                
            case .failure(let error):
                DispatchQueue.main.async {
                 // hideActivity()
                }
                completion?(nil)
                print(error.localizedDescription)

            }
            
        }
    }
    
}




struct  CategoryResponse: Codable {
    let status: Int
    let error: Bool
    let messages: String
    let data: [Categoryies]
}

// MARK: - Datum
struct Categoryies: Codable {
    let id, name: String
    let catIcon: String
    let image: String
    var children:[Categoryies]
    let level: Int

    enum CodingKeys: String, CodingKey {
        case id, name
        case catIcon = "cat_icon"
        case image, children, level
    }
}
