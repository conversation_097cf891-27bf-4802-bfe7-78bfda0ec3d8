<?xml version="1.0" encoding="UTF-8"?>
<document type="com.apple.InterfaceBuilder3.CocoaTouch.XIB" version="3.0" toolsVersion="20037" targetRuntime="iOS.CocoaTouch" propertyAccessControl="none" useAutolayout="YES" useTraitCollections="YES" useSafeAreas="YES" colorMatched="YES">
    <device id="retina6_1" orientation="portrait" appearance="light"/>
    <dependencies>
        <deployment identifier="iOS"/>
        <plugIn identifier="com.apple.InterfaceBuilder.IBCocoaTouchPlugin" version="20020"/>
        <capability name="Safe area layout guides" minToolsVersion="9.0"/>
        <capability name="System colors in document resources" minToolsVersion="11.0"/>
        <capability name="documents saved in the Xcode 8 format" minToolsVersion="8.0"/>
    </dependencies>
    <objects>
        <placeholder placeholderIdentifier="IBFilesOwner" id="-1" userLabel="File's Owner"/>
        <placeholder placeholderIdentifier="IBFirstResponder" id="-2" customClass="UIResponder"/>
        <collectionViewCell opaque="NO" clipsSubviews="YES" multipleTouchEnabled="YES" contentMode="center" id="gTV-IL-0wX" customClass="CattitleCollectionViewCell" customModule="BabyHouse" customModuleProvider="target">
            <rect key="frame" x="0.0" y="0.0" width="286" height="65"/>
            <autoresizingMask key="autoresizingMask"/>
            <view key="contentView" opaque="NO" clipsSubviews="YES" multipleTouchEnabled="YES" contentMode="center">
                <rect key="frame" x="0.0" y="0.0" width="286" height="65"/>
                <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMaxY="YES"/>
                <subviews>
                    <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="Fcc-n5-hgM">
                        <rect key="frame" x="10" y="10" width="266" height="45"/>
                        <subviews>
                            <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Label" textAlignment="center" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="gFV-e1-d0c">
                                <rect key="frame" x="8" y="8" width="250" height="29"/>
                                <fontDescription key="fontDescription" type="system" pointSize="17"/>
                                <nil key="textColor"/>
                                <nil key="highlightedColor"/>
                            </label>
                        </subviews>
                        <color key="backgroundColor" systemColor="systemBackgroundColor"/>
                        <constraints>
                            <constraint firstAttribute="trailing" secondItem="gFV-e1-d0c" secondAttribute="trailing" constant="8" id="JHD-tv-E4r"/>
                            <constraint firstItem="gFV-e1-d0c" firstAttribute="leading" secondItem="Fcc-n5-hgM" secondAttribute="leading" constant="8" id="Ua5-DW-Hxq"/>
                            <constraint firstItem="gFV-e1-d0c" firstAttribute="top" secondItem="Fcc-n5-hgM" secondAttribute="top" constant="8" id="fgH-ed-6gU"/>
                            <constraint firstAttribute="bottom" secondItem="gFV-e1-d0c" secondAttribute="bottom" constant="8" id="h5S-F3-lWn"/>
                        </constraints>
                    </view>
                </subviews>
            </view>
            <viewLayoutGuide key="safeArea" id="ZTg-uK-7eu"/>
            <constraints>
                <constraint firstItem="Fcc-n5-hgM" firstAttribute="leading" secondItem="gTV-IL-0wX" secondAttribute="leading" constant="10" id="52L-Tw-CY7"/>
                <constraint firstItem="Fcc-n5-hgM" firstAttribute="top" secondItem="gTV-IL-0wX" secondAttribute="top" constant="10" id="UvC-Ld-Ic3"/>
                <constraint firstAttribute="trailing" secondItem="Fcc-n5-hgM" secondAttribute="trailing" constant="10" id="VpZ-gd-P9K"/>
                <constraint firstAttribute="bottom" secondItem="Fcc-n5-hgM" secondAttribute="bottom" constant="10" id="nkf-Xp-R1o"/>
            </constraints>
            <size key="customSize" width="286" height="65"/>
            <connections>
                <outlet property="mainView" destination="Fcc-n5-hgM" id="pjG-4e-Rwc"/>
                <outlet property="nameLbl" destination="gFV-e1-d0c" id="kNS-SU-8Pq"/>
            </connections>
            <point key="canvasLocation" x="302.89855072463769" y="100.78125"/>
        </collectionViewCell>
    </objects>
    <resources>
        <systemColor name="systemBackgroundColor">
            <color white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
        </systemColor>
    </resources>
</document>
