//
//  CategoryTableViewCell.swift
//  Saydality
//
//  Created by <PERSON><PERSON> on 01/12/21.
//

import UIKit

class CategoryTableViewCell: UITableViewCell {
   
    @IBOutlet weak var lineLbl: UILabel!
    var category:Categoryies? {
        didSet {
            viewall.text = category?.name ?? ""
        }
    }
    @IBOutlet weak var parentView: UIView!
    @IBOutlet weak var containerView: UIView!

    @IBOutlet weak var viewall: UILabel!
    override func awakeFromNib() {
        super.awakeFromNib()
        
         viewall.font = UIFont.with(size:12, .regular)
        selectionStyle = .none
        //self.containerView.backgroundColor = UIColor(colorWithHexValue:0xF2F6FE)
        lineLbl.backgroundColor = Babyred.withAlphaComponent(0.3)
    }
    override func layoutSubviews() {
        DispatchQueue.main.async {
           // self.containerView.round(corners:[.bottomLeft,.bottomRight], radius: 20)
          //  self.containerView.setShadow()
        
            }
    }
    override func setSelected(_ selected: <PERSON><PERSON>, animated: <PERSON>ol) {
        super.setSelected(selected, animated: animated)

        // Configure the view for the selected state
    }
    
}
extension CategoryTableViewCell:ReuseIdentifying,NibLoadable {}
