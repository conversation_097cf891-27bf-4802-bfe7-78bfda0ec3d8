//
//  SceneDelegate.swift
//  BabyHouse
//
//  Created by <PERSON><PERSON> on 23/03/22.
//

import UIKit
class SceneDelegate: UIResponder, UIWindowSceneDelegate {
    
    var window: UIWindow?
    
    @available(iOS 13.0, *)
    func scene(_ scene: UIScene, willConnectTo session: UISceneSession, options connectionOptions: UIScene.ConnectionOptions) {
        
         guard let windowScene = (scene as? UIWindowScene) else { return }
         ApplicationConfiguration.configureInitialViewController(on: &window)
         window?.windowScene = windowScene
    }
}


